<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sector Intelligence Leading Indicators</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #1F2025;
            color: #e0e0e0;
            height: 100vh;
            overflow: hidden;
        }

        .app-container {
            display: flex;
            height: 100vh;
        }

        /* Two-tier Sidebar */
        .sidebar-container {
            display: flex;
            height: 100vh;
        }

        /* Icon sidebar (tier 1) */
        .icon-sidebar {
            width: 80px;
            background-color: #2F3135;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding-top: 20px;
            border-right: 1px solid rgba(255,255,255,0.05);
        }

        .logo-icon {
            width: 60px;
            height: 60px;
            font-family: Georgia, 'Times New Roman', serif;
            font-size: 36px;
            font-weight: 300;
            color: #E0E0E0;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 24px;
            cursor: pointer;
            transition: color 0.2s ease;
        }

        .logo-icon:hover {
            color: #ffffff;
        }

        .icon-button {
            width: 100%;
            padding: 12px 0;
            display: flex;
            flex-direction: column;
            align-items: center;
            cursor: pointer;
            transition: background-color 0.2s;
            color: #808080;
            text-decoration: none;
        }

        .icon-button:hover {
            background-color: rgba(255,255,255,0.05);
            color: #e0e0e0;
        }

        .icon-button.active {
            background-color: rgba(255,255,255,0.08);
            color: #e0e0e0;
        }

        .icon-button svg {
            width: 28px;
            height: 28px;
            fill: none;
            stroke: currentColor;
            stroke-width: 1.5;
            margin-bottom: 6px;
        }

        .icon-button span {
            font-size: 11px;
            font-weight: 400;
            letter-spacing: 0.02em;
        }

        /* Main sidebar (tier 2) */
        .main-sidebar {
            width: 280px;
            background-color: #282A2C;
            display: flex;
            flex-direction: column;
            border-right: 1px solid rgba(255,255,255,0.05);
        }

        .sidebar-header {
            padding: 24px 20px 16px;
            border-bottom: 1px solid rgba(255,255,255,0.05);
        }

        .sidebar-header h2 {
            font-size: 24px;
            font-weight: 400;
            color: #e0e0e0;
            margin: 0;
        }

        .nav-section {
            padding: 15px 0;
        }

        .nav-item {
            display: flex;
            align-items: center;
            padding: 12px 20px;
            cursor: pointer;
            transition: background-color 0.2s;
            color: #b0b0b0;
        }

        .nav-item:hover {
            background-color: rgba(255,255,255,0.05);
        }

        .nav-item.active {
            background-color: rgba(255,255,255,0.08);
            color: #e0e0e0;
        }

        .nav-item svg {
            width: 20px;
            height: 20px;
            margin-right: 12px;
            fill: none;
            stroke: currentColor;
            stroke-width: 1.5;
        }

        .nav-label {
            font-size: 11px;
            color: #808080;
            padding: 8px 20px;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .chat-list-item {
            display: flex;
            align-items: center;
            padding: 8px 20px;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .chat-list-item:hover {
            background-color: rgba(255,255,255,0.05);
        }

        .chat-list-item svg {
            width: 16px;
            height: 16px;
            margin-right: 10px;
            fill: none;
            stroke: #808080;
            stroke-width: 1.5;
        }

        .chat-list-item .delete-icon {
            opacity: 0;
            transition: opacity 0.2s;
            cursor: pointer;
            margin-right: 0 !important;
        }

        .chat-list-item:hover .delete-icon {
            opacity: 1;
        }

        .chat-list-item .delete-icon:hover {
            stroke: #ef4444;
        }

        .new-badge {
            background-color: #8b5cf6;
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 11px;
            margin-left: auto;
        }

        /* Main Content */
        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
            background-color: #1F2025;
        }

        .header {
            padding: 16px 24px;
            border-bottom: 1px solid rgba(255,255,255,0.05);
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .header-icon {
            width: 32px;
            height: 32px;
            background-color: rgba(139, 92, 246, 0.1);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .header-title {
            font-size: 16px;
            color: #e0e0e0;
        }

        .view-agent {
            margin-left: auto;
            color: #808080;
            text-decoration: none;
            font-size: 14px;
        }

        /* Table Content */
        .table-container {
            flex: 1;
            overflow-y: auto;
            padding: 24px;
        }

        .description {
            color: #808080;
            font-size: 14px;
            margin-bottom: 24px;
        }

        table {
            width: 100%;
            border-collapse: collapse;
        }

        th {
            text-align: left;
            padding: 12px 16px;
            font-size: 13px;
            font-weight: 500;
            color: #808080;
            border-bottom: 1px solid rgba(255,255,255,0.05);
        }

        td {
            padding: 16px;
            font-size: 14px;
            border-bottom: 1px solid rgba(255,255,255,0.03);
            vertical-align: top;
        }

        tr:hover {
            background-color: rgba(255,255,255,0.02);
        }

        .indicator-number {
            color: #808080;
            width: 40px;
        }

        .indicator-name {
            color: #e0e0e0;
            min-width: 200px;
        }

        .signal-value {
            color: #e0e0e0;
            line-height: 1.5;
        }

        .direction {
            min-width: 100px;
        }

        .direction.down {
            color: #ef4444;
        }

        .direction.up {
            color: #10b981;
        }

        .impact {
            color: #b0b0b0;
            line-height: 1.5;
        }

        .sources {
            color: #808080;
            min-width: 100px;
        }

        /* Chat Input */
        .chat-input-container {
            padding: 20px;
            border-top: 1px solid rgba(255,255,255,0.05);
        }

        .chat-input-wrapper {
            background-color: #282A2C;
            border-radius: 8px;
            padding: 16px;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .chat-input {
            flex: 1;
            background: none;
            border: none;
            color: #e0e0e0;
            font-size: 14px;
            outline: none;
        }

        .chat-input::placeholder {
            color: #606060;
        }

        .add-button {
            width: 32px;
            height: 32px;
            background-color: rgba(255,255,255,0.1);
            border: none;
            border-radius: 6px;
            color: #808080;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
        }

        .add-button:hover {
            background-color: rgba(255,255,255,0.15);
        }

        .company-dropdown {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 12px;
            background-color: rgba(255,255,255,0.1);
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            color: #b0b0b0;
        }

        .company-dropdown:hover {
            background-color: rgba(255,255,255,0.15);
        }

        .footer-text {
            font-size: 12px;
            color: #606060;
            text-align: center;
            padding: 16px;
        }

        /* Scrollbar styling */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: #1F2025;
        }

        ::-webkit-scrollbar-thumb {
            background: #4a4a4a;
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: #5a5a5a;
        }

        .menu-icon {
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            color: #808080;
        }

        .avatar {
            width: 32px;
            height: 32px;
            background-color: #4ade80;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            color: #1F2025;
            margin-top: auto;
            margin: 20px;
        }

        .chat-info {
            font-size: 12px;
            color: #606060;
            padding: 0 20px 10px;
        }

        .chat-actions {
            font-size: 12px;
            padding: 0 20px 20px;
        }

        .chat-actions a {
            color: #808080;
            text-decoration: none;
        }

        .chat-actions a:hover {
            color: #b0b0b0;
        }

        .time-period {
            color: #8b5cf6;
        }

        .settings-icon {
            margin-top: auto;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="app-container">
        <!-- Two-tier Sidebar -->
        <div class="sidebar-container">
            <!-- Icon Sidebar (Tier 1) -->
            <div class="icon-sidebar">
                <div class="logo-icon" role="banner" title="Apollo Intelligence">A</div>
                
                <a href="#" class="icon-button">
                    <svg viewBox="0 0 24 24" style="stroke-width: 1.5;">
                        <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z" stroke-linejoin="round"/>
                        <polyline points="9 22 9 12 15 12 15 22" stroke-linejoin="round"/>
                    </svg>
                    <span>Home</span>
                </a>
                
                <a href="#" class="icon-button active">
                    <svg viewBox="0 0 24 24" style="stroke-width: 1.5;">
                        <path d="M21 11.5a8.38 8.38 0 0 1-.9 3.8 8.5 8.5 0 0 1-7.6 4.7 8.38 8.38 0 0 1-3.8-.9L3 21l1.9-5.7a8.38 8.38 0 0 1-.9-3.8 8.5 8.5 0 0 1 4.7-7.6 8.38 8.38 0 0 1 3.8-.9h.5a8.48 8.48 0 0 1 8 8v.5z" stroke-linejoin="round"/>
                    </svg>
                    <span>Chat</span>
                </a>
                
                <a href="#" class="icon-button">
                    <svg viewBox="0 0 24 24" style="stroke-width: 1.5;">
                        <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2" stroke-linecap="round" stroke-linejoin="round"/>
                        <circle cx="9" cy="7" r="4" stroke-linecap="round" stroke-linejoin="round"/>
                        <path d="M23 21v-2a4 4 0 0 0-3-3.87" stroke-linecap="round" stroke-linejoin="round"/>
                        <path d="M16 3.13a4 4 0 0 1 0 7.75" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                    <span>People</span>
                </a>
                
                <a href="#" class="icon-button">
                    <svg viewBox="0 0 24 24" style="stroke-width: 1.5;">
                        <path d="M13 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V9z" stroke-linejoin="round"/>
                        <polyline points="13 2 13 9 20 9" stroke-linejoin="round"/>
                    </svg>
                    <span>Content</span>
                </a>
            </div>

            <!-- Main Sidebar (Tier 2) -->
            <div class="main-sidebar">
                <div class="sidebar-header">
                    <h2>Chat</h2>
                </div>
                
                <div class="nav-item">
                    <svg viewBox="0 0 24 24" style="stroke-width: 1.5;">
                        <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z" stroke-linejoin="round"/>
                        <polyline points="14 2 14 8 20 8" stroke-linejoin="round"/>
                        <line x1="12" y1="18" x2="12" y2="12" stroke-linecap="round"/>
                        <line x1="9" y1="15" x2="15" y2="15" stroke-linecap="round"/>
                    </svg>
                    <span>New Chat</span>
                    <svg class="dropdown-arrow" style="width: 16px; height: 16px; margin-left: 8px; margin-right: 0; stroke: currentColor; fill: none; stroke-width: 1.5;" viewBox="0 0 24 24">
                        <polyline points="6 9 12 15 18 9"/>
                    </svg>
                </div>
                
                <div class="nav-section">
                    <div class="nav-item">
                        <svg viewBox="0 0 24 24" style="stroke-width: 1.5;">
                            <circle cx="12" cy="12" r="3"/>
                            <path d="M12 1v6m0 6v6m4.22-10.22l4.24-4.24M6.34 6.34L2.1 2.1m17.8 17.8l-4.24-4.24M6.34 17.66L2.1 21.9"/>
                        </svg>
                        <span>Agents</span>
                        <span class="new-badge">NEW</span>
                    </div>
                    
                    <div class="nav-item">
                        <svg viewBox="0 0 24 24" style="stroke-width: 1.5;">
                            <path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z"/>
                            <polyline points="14 2 14 8 20 8"/>
                            <line x1="8" y1="13" x2="16" y2="13"/>
                            <line x1="8" y1="17" x2="16" y2="17"/>
                            <polyline points="10 9 9 9 8 9"/>
                        </svg>
                        <span>Prompts</span>
                    </div>
                    
                    <div class="nav-item">
                        <svg viewBox="0 0 24 24" style="stroke-width: 1.5;">
                            <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>
                            <rect x="7" y="7" width="3" height="9"/>
                            <rect x="14" y="7" width="3" height="5"/>
                        </svg>
                        <span>Apps</span>
                    </div>
                </div>

                <div class="nav-section">
                    <div class="nav-label">Today</div>
                    <div class="chat-list-item">
                        <svg viewBox="0 0 24 24" style="stroke-width: 1.5;">
                            <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
                            <polyline points="14 2 14 8 20 8"/>
                            <line x1="16" y1="13" x2="8" y2="13"/>
                            <line x1="16" y1="17" x2="8" y2="17"/>
                            <polyline points="10 9 8 9 8 9"/>
                        </svg>
                        <span>Sector Intelligence...</span>
                        <svg viewBox="0 0 24 24" class="delete-icon" style="width: 16px; height: 16px; margin-left: auto; stroke-width: 1.5;">
                            <polyline points="3 6 5 6 21 6"/>
                            <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"/>
                        </svg>
                    </div>
                </div>

                <div class="nav-section">
                    <div class="nav-label">Past 7 Days</div>
                    <div class="chat-list-item">
                        <svg viewBox="0 0 24 24" style="stroke-width: 1.5;">
                            <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
                            <polyline points="14 2 14 8 20 8"/>
                            <line x1="16" y1="13" x2="8" y2="13"/>
                            <line x1="16" y1="17" x2="8" y2="17"/>
                            <polyline points="10 9 8 9 8 9"/>
                        </svg>
                        <span>Sector Intelligence I...</span>
                    </div>
                    <div class="chat-list-item">
                        <svg viewBox="0 0 24 24" style="stroke-width: 1.5;">
                            <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
                            <polyline points="14 2 14 8 20 8"/>
                            <line x1="16" y1="13" x2="8" y2="13"/>
                            <line x1="16" y1="17" x2="8" y2="17"/>
                            <polyline points="10 9 8 9 8 9"/>
                        </svg>
                        <span>Sector Intelligence I...</span>
                    </div>
                </div>

                <div style="margin-top: auto;">
                    <div class="chat-info">Chats are saved up to 30 days.</div>
                    <div class="chat-actions">
                        <a href="#">Delete</a> or <a href="#">Disable</a>
                    </div>
                    <div class="avatar">AV</div>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <div class="header">
                <div class="menu-icon">☰</div>
                <div class="header-icon">
                    <svg width="16" height="16" viewBox="0 0 24 24" style="stroke: #8b5cf6; fill: none; stroke-width: 1.5;">
                        <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
                        <polyline points="14 2 14 8 20 8"/>
                        <line x1="16" y1="13" x2="8" y2="13"/>
                        <line x1="16" y1="17" x2="8" y2="17"/>
                    </svg>
                </div>
                <h1 class="header-title">Sector Intelligence leading indicators</h1>
                <a href="#" class="view-agent">• View agent</a>
            </div>

            <div class="table-container">
                <p class="description">Shows key agricultural sector trends from authoritative sources, with implications for Deere & Company - including a recent pullback in precision ag segment revenues.</p>
                
                <table>
                    <thead>
                        <tr>
                            <th>#</th>
                            <th>Leading Indicator</th>
                            <th>Latest Signal <span class="time-period">(Time period)</span></th>
                            <th>Direction vs. Prior Period</th>
                            <th>Why It Matters for Deere & Company</th>
                            <th>Sources</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td class="indicator-number">1</td>
                            <td class="indicator-name">U.S. net farm-income trajectory</td>
                            <td class="signal-value">2025 net farm income projected at $100.9 bn, = 26.9 % YoY (2025 forecast)</td>
                            <td class="direction down">Down</td>
                            <td class="impact">Farm cash flow leads large-equipment orders by 6-12 months; weaker income signals lower future machinery sales</td>
                            <td class="sources"></td>
                        </tr>
                        <tr>
                            <td class="indicator-number">2</td>
                            <td class="indicator-name">Commodity-price basket (corn/soy/wheat)</td>
                            <td class="signal-value">2025 cash receipts for major crops forecast lower; production expenses (incl. interest) up</td>
                            <td class="direction down">Down</td>
                            <td class="impact">Depressed crop prices reduce upgrade economics, curbing demand for new equipment</td>
                            <td class="sources"></td>
                        </tr>
                        <tr>
                            <td class="indicator-number">3</td>
                            <td class="indicator-name">Borrowing costs for farmers</td>
                            <td class="signal-value">Fed funds ≥ 5 %; DE Financial credit-loss provisions doubled YoY (mid-2025)</td>
                            <td class="direction up">Up</td>
                            <td class="impact">Higher financing costs force farmers to delay or cancel big-ticket purchases</td>
                            <td class="sources"></td>
                        </tr>
                        <tr>
                            <td class="indicator-number">4</td>
                            <td class="indicator-name">Dealer & farmer sentiment</td>
                            <td class="signal-value">Purdue/CME Barometer at [?]; DE conference call notes "softness" in precision ag revenues</td>
                            <td class="direction down">Down</td>
                            <td class="impact">Sentiment dips typically precede order cancellations & inventory build-ups</td>
                            <td class="sources"></td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="chat-input-container">
                <div class="chat-input-wrapper">
                    <input type="text" class="chat-input" placeholder="Ask anything about Apollo...">
                    <button class="add-button">+</button>
                    <div class="company-dropdown">
                        <svg width="16" height="16" viewBox="0 0 24 24" style="stroke: currentColor; fill: none; stroke-width: 1.5;">
                            <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>
                            <path d="M8 12h8M8 8h8M8 16h5"/>
                        </svg>
                        <span>Company</span>
                        <svg width="12" height="12" viewBox="0 0 24 24" style="stroke: currentColor; fill: none; stroke-width: 1.5;">
                            <polyline points="6 9 12 15 18 9"/>
                        </svg>
                    </div>
                </div>
                <div class="footer-text">
                    In accordance with Apollo's AI guidelines, assess and confirm the applicability, veracity, and accuracy of outputs.
                </div>
            </div>
        </div>
    </div>

    <script>
        // SPA functionality
        const app = {
            currentView: 'sector-intelligence',
            
            init() {
                this.attachEventListeners();
                this.renderView();
            },

            attachEventListeners() {
                // Logo click
                document.querySelector('.logo-icon').addEventListener('click', () => {
                    console.log('Logo clicked - navigate to home');
                    // In a real SPA, this would navigate to home
                });

                // Icon sidebar clicks
                document.querySelectorAll('.icon-button').forEach((button) => {
                    button.addEventListener('click', (e) => {
                        e.preventDefault();
                        document.querySelectorAll('.icon-button').forEach(b => b.classList.remove('active'));
                        button.classList.add('active');
                        console.log('Icon clicked:', button.querySelector('span').textContent);
                    });
                });

                // Navigation clicks
                document.querySelectorAll('.nav-item').forEach(item => {
                    item.addEventListener('click', (e) => {
                        console.log('Nav item clicked:', item.textContent.trim());
                    });
                });

                // Chat list items
                document.querySelectorAll('.chat-list-item').forEach(item => {
                    item.addEventListener('click', (e) => {
                        // Don't trigger if clicking the delete icon
                        if (!e.target.closest('.delete-icon')) {
                            console.log('Chat item clicked:', item.textContent.trim());
                        }
                    });

                    // Delete icon click
                    const deleteIcon = item.querySelector('.delete-icon');
                    if (deleteIcon) {
                        deleteIcon.addEventListener('click', (e) => {
                            e.stopPropagation();
                            console.log('Delete chat:', item.textContent.trim());
                        });
                    }
                });

                // Chat input
                const chatInput = document.querySelector('.chat-input');
                chatInput.addEventListener('keypress', (e) => {
                    if (e.key === 'Enter' && e.target.value.trim()) {
                        console.log('Query:', e.target.value);
                        e.target.value = '';
                    }
                });

                // Table row hover effects
                document.querySelectorAll('tbody tr').forEach(row => {
                    row.addEventListener('mouseenter', () => {
                        row.style.cursor = 'pointer';
                    });
                });

                // Company dropdown
                document.querySelector('.company-dropdown').addEventListener('click', () => {
                    console.log('Company dropdown clicked');
                });

                // Add button
                document.querySelector('.add-button').addEventListener('click', () => {
                    console.log('Add button clicked');
                });
            },

            renderView() {
                // This would handle different views in a real SPA
                console.log('Rendering view:', this.currentView);
            }
        };

        // Initialize app when DOM is ready
        document.addEventListener('DOMContentLoaded', () => {
            app.init();
        });
    </script>
</body>
</html>