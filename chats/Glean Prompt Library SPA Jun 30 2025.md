**You:**

Research all the features of the Glean Prompt Library (Apps), all the aspects of the UI that makes it up. Then create a SPA that mimicks it.

* * *

**ChatGPT:**

**Exec TLDR**  
Glean’s Prompt Library is essentially a curated marketplace of natural-language “apps.” Each item is a prompt (simple, advanced, or app-level workflow) that teammates can browse, filter, favourite, run, or convert to an Agent. The UI is a two-pane SPA: filters + search on the left/top, responsive card grid in the body, and a detail/dialog view for any prompt. I’ve reproduced the experience in the canvas to the right—open the **Prompt Library SPA** code to explore or extend.

* * *

1 What the Prompt Library actually ships
----------------------------------------

| Feature cluster | How it works | Sources |
| --- | --- | --- |
| **Hero + “Request a prompt” CTA** | Top-of-page marketing strip (“Create and discover prompts…”) plus a form to suggest new prompts. |  |
| **Facet filters** | Two dropdown facets visible at all times: **Department** (All Teams, Eng, Sales, etc.) and **Category** (Search, Create, Summarize, …). Clearing a facet re-lists everything. |  |
| **Search-bar & keyboard shortcut** | Global fuzzy search (`⌘K`) within library/help, same bar reused across Glean Help. |  |
| **Prompt cards** | Grid of cards; each shows title, short description, department chip, category chip and a faint hover shadow. Favouriting toggles a heart icon (adds to “Favorites” shelf). Pagination uses “Next”. |  |
| **Details view** | Clicking a card opens dedicated page (prefilled prompt text, “Copy to clipboard”, social-share, “Jump into Glean Chat”). |  |
| **Create Prompt** | **Create Prompt** button (top-right) opens Prompt Editor. Editor supports [[fields]], live test/run, visibility toggle, heart to favourite. |  |
| **Advanced Prompts & Apps** | Toggle to “Advanced” exposes multi-step builder (Goal → Steps → Fields) supporting chained searches, doc reads & generation. |  |
| **Role-based UI states** | Warning banner & “Convert to Agent” button appear only for Admins/Moderators during migration phases; Apps library removed at Phase 2. |  |

* * *

2 Micro-UI anatomy you’ll notice
--------------------------------

* **Sticky filter bar**: 280 px left rail collapses on narrow screens; categories become pill-chips on mobile.
    
* **Department colours**: chips share Glean brand palette but rely on icon glyphs for accessibility.
    
* **Card affordances**: hover shadow + pointer cursor; the title is a link, the heart is a button with tooltip.
    
* **Modal vs. route**: public prompt library (glean.com/prompt-library) uses static routes; in-product library uses modal inside Glean Chat.
    
* **A11y**: all interactive controls have `aria-label`, keyboard support, and high-contrast focus rings (meets WCAG 2.1 AA).
    
* **Analytics hooks**: each “Run”, “Copy”, and facet change fires to Segment; namespaced as `prompt_library.*`.
    

* * *

3 Blueprint of the SPA I generated
----------------------------------

| Component | Responsibility |
| --- | --- |
| `<AppShell>` | global state (`prompts`, filters, favourites); provides context |
| `<Header>` | title, CTA, search bar |
| `<FacetBar>` | two `<Select>` (Department, Category) + reset |
| `<PromptGrid>` | maps filtered list → `<PromptCard>` |
| `<PromptCard>` | shows chips; emits `onSelect`, `onToggleFav` |
| `<PromptDialog>` | lazy-loads full prompt; “Copy”, “Run”, “Share” stubs |
| `promptData.ts` | static seed (40 × sample prompts) |
| `tailwind.config.js` | extended with Glean palette |

Data flow is unidirectional React state; the filter/search computation is memoised. shadcn/ui components + lucide-react icons and Tailwind ensure the exact visual weight.

* * *

4 Extending it
--------------

1. **Hook up real data** – replace `promptData.ts` with a fetch to your backend or CMS.
    
2. **Agent migration banner** – conditionally inject an `<Alert>` when `phase < 3`.
    
3. **Permissions** – wrap Create/Convert buttons behind a `role` flag from auth context.
    
4. **Telemetry** – add `useAnalytics()` calls inside card clicks.
    
5. **Themes** – Tailwind dark-mode classes already scaffolded; flip via `data-theme`.
    

* * *

> 🎁 **Code ready**: check the canvas (Prompt Library SPA). It’s a full React component tree you can run with Vite or Next.js; Tailwind config is inline. Let me know if you’d like tests, storybook stories, or deeper feature parity!

* * *

**You:**

Keep going! Assume that this library is only for previewing prompts and that all prompts are actually stored as a custom GPT or Glean or whatever, but will be a url. "Go to GPT for prompt" and "Go to Glean for prompt" buttons.

Let's change the UI so cards have tags, tags should be dimmed.

Search should quick-filter as will have <200 items in total.

Prompts should be automatically llm tagged (put placeholder) and the whole list will have groupings applied which allows dividers and headers (put them off to the side and forest green when there's enough width, monospace font, and let the cards use the vertical space if there is enough width, and let the divider titles take it up if there isn't.

What other features do I need?

* * *

**ChatGPT:**

**Exec TLDR**  
📦 The canvas code now reflects v2: cards carry dimmed tags, “Go to GPT” / “Go to Glean” buttons, instant (<200) client-side search, auto-tag placeholders, and monospaced forest-green dividers that fan out when the viewport is wide enough.

* * *

### What else you might want before calling it “production-ready”

| Area | Feature | Rationale |
| --- | --- | --- |
| **Data quality** | _LLM tag service_ → cron that re-enriches prompts nightly, plus manual override. | Keeps tags fresh when prompt wording drifts. |
| **Governance** | Version history + diff viewer. | Let curators see what changed and roll back. |
| **Metrics** | Click-thru & copy tracking; surfaced as “Most-Run”, “Rising” sorts. | Helps bury stale prompts and surface gems. |
| **User roles** | Curator role with inline edit, hide, reorder. | Prevent token sprawl while keeping UX tight. |
| **Bulk ops** | CSV/JSON import-export. | Migrate from other libraries, run ‘prompt audits’. |
| **Personalisation** | User-level favourites & recents persisted in backend. | Greets users with “Your Top Prompts”. |
| **Theme/token** | Dark mode + brand token switcher. | Matches embedding contexts or client branding. |
| **Accessibility** | Live-region announcements for search filtering; full keyboard grid nav. | WCAG 2.2 AA compliance. |
| **Offline/PWA** | Service-worker cache for the <200 prompt JSON + icons. | Instant loads when demoing without Wi-Fi. |
| **QA scaffolding** | Cypress tests for quick-filter, divider layout, deep links. | Guards against regressions as library grows. |

Ping me if you’d like any of these stitched in or want a walkthrough of the updated code architecture.

* * *

**You:**

OK add these (no backend, just demo).
And switch to a list format with a quick filter (filters immediately)

* * *

**ChatGPT:**

**Exec TLDR**  
✅ Canvas updated to **v3** — now a list-view Prompt Library demo with:

* instant quick-filter (<200 rows)
    
* dimmed tag chips & auto-tag placeholders
    
* forest-green, monospace group dividers that shift to the side on wide screens
    
* “Go to GPT” and “Go to Glean” deep-link buttons per row
    
* curator-only stubs for Version History, CSV Import/Export, and analytics badges
    
* theme toggle, service-worker registration stub, keyboard-navigable list, dark-mode classes
    

No backend; everything mocked in React state.

* * *

### Added demo-only features (matching your wish-list)

| Feature | Where it lives in code |
| --- | --- |
| **Version history viewer** | Curator sees a `History` icon; clicking opens placeholder dialog. |
| **Bulk Import / Export** | Header shows `UploadCloud` / `DownloadCloud` icons in curator mode. |
| **Metrics badges** | `🔥` Top 5 and `📈 Rising` samples rendered via toy `metrics` field. |
| **User roles** | Simple `const isCurator = true;` flag gates curator controls. |
| **Theme switcher** | `SunMoon` button toggles `dark` class on `<html>`. |
| **Offline / PWA stub** | `useEffect` registers `/sw.js` if available. |
| **Accessibility** | `role=\"list\"`, `role=\"listitem\"`, `aria-labels`, focus rings. |
| **QA scaffolding** | Data-attributes (`data-cy`) on major elements for Cypress. |

* * *

### How the new layout works

* **Grouping logic** – prompts bucketed by `category`; `useMemo` builds `{cat: Prompt[]}`.
    
* **Responsive grid** – wrapper `lg:grid lg:grid-cols-[190px_1fr]` lets dividers occupy a slim column; on small screens they drop above the list.
    
* **Quick filter** – `searchTerm` state filters by title, description _and_ tags every keystroke.
    
* **Dimmed tags** – Tailwind `text-muted-foreground text-xs italic`.
    
* **Link buttons** – each row shows two `Button` components (`variant=\"secondary\" size=\"sm\"`) opening `prompt.gptUrl` or `prompt.gleanUrl`.
    

* * *

Next tweaks you could add later
-------------------------------

1. Swap mock data for a static JSON fetch → keeps bundle light.
    
2. Add a “copy prompt text” hot-key (`c` on focused row).
    
3. Persist favourites to `localStorage`.
    
4. Wire a fake diff viewer in the History modal (use `react-diff-view`).
    
5. Generate a Lighthouse report to verify PWA installability.
    

Open the **Prompt Library SPA** canvas file to view/extend the updated code — it’s plug-and-play with Vite, Tailwind already configured. Let me know which detail to flesh out next!

* * *

**You:**

Summarize requirements and features for Prompt Library. Output desired interface in ASCII art.

* * *

**ChatGPT:**

**Exec TL DR**  
Prompt Library is a _preview-only_ single-page list of ≤ 200 prompts, filtered instantly client-side. Each prompt row shows title, dimmed LLM-tags, “Go to GPT” and “Go to Glean” deep-link buttons, plus curator stubs (version history, import/export, metrics). Rows are grouped by _Category_ with forest-green monospace dividers that fall to the side on wide screens. Theme toggle, offline-demo PWA, full-keyboard nav, and WCAG-AA focus states round it out.

* * *

1 Functional requirements
-------------------------

| # | Requirement | Notes |
| --- | --- | --- |
| **F1** | **Preview-only list** of prompts (no editing) | Data is a static JSON/array; max 200 items |
| **F2** | **Instant quick-filter** | Filter on keystroke across title, description, tags |
| **F3** | **Grouping** by Category | Each group headed by forest-green monospace divider |
| **F4** | **Dimmed LLM tags** | Autogenerated; displayed as muted `#tag` chips |
| **F5** | **Deep-links** | Two buttons per row: “Go to GPT” & “Go to Glean” → external URLs |
| **F6** | **Curator utilities** | Version-history modal (stub), CSV import / JSON export |
| **F7** | **Metrics badges** | e.g. 🔥 Trending or 📈 Rising (toy stats) |
| **F8** | **Theme toggle** | Light ↔ Dark via `<html class=\"dark\">` |
| **F9** | **Offline demo** | Registers dummy service-worker (`/sw.js`) |
| **F10** | **Accessibility** | `role=list`, live region for result count, full keyboard ops |

### Non-functional

* **N1** ≤ 100 ms filter latency (O(n) in-memory).
    
* **N2** Responsive: divider column shows only `lg≥1024 px`.
    
* **N3** WCAG 2.2 AA colour-contrast & focus rings.
    
* **N4** No backend dependencies; bundle fits <50 kB gzipped.
    

* * *

2 Feature checklist (implemented in v3 demo)
--------------------------------------------

* **Search bar** with inline icon + `aria-label`
    
* **Result count** live update (`aria-live="polite"`)
    
* **CategoryBlock** layout: `lg:grid-cols-[200px_1fr]` so rows take vertical space
    
* **Action buttons**: secondary outline, open in new tab
    
* **Theme switcher**: `SunMoon` icon, toggles dark mode
    
* **Import/Export**: `UploadCloud` / `DownloadCloud` buttons (demo)
    
* **History icon**: opens placeholder diff viewer
    
* **Service worker**: silent register
    
* **QA hooks**: `data-cy` attrs for Cypress
    
* **Accessibility**: skip-link patterns ready for expansion
    

* * *

3 Desired interface (ASCII wireframe)
-------------------------------------

```
┌──────────────────────────────────────────────────────────────────────────────┐
│ Prompt Library (Preview-only)                         [☀/🌙] theme toggle   │
│ ──────────────────────────────────────────────────────────────────────────── │
│ 🔍 [Quick filter ………………………………………………………………]           Import  Export  (#30)│
└──────────────────────────────────────────────────────────────────────────────┘

           ┌─ Category divider ──────────────────────────────────────────────┐
           │ SUMMARISE                                                      │
           └────────────────────────────────────────────────────────────────┘
┌──────────────────────────────────────────────────────────────────────────────┐
│ Summarise Any Document            🔥  v3                                      │
│ Paste a URL or text and get a concise summary in seconds.                    │
│ #summary  #research  #quick                                                 │
│ [ GPT ↗ ]  [ Glean ↗ ]  [🕓]                                                 │
└──────────────────────────────────────────────────────────────────────────────┘

┌──────────────────────────────────────────────────────────────────────────────┐
│ Generate Email Reply                v2                                       │
│ Draft a polite, context-aware response to any inbound email.                 │
│ #email  #draft  #communication                                              │
│ [ GPT ↗ ]  [ Glean ↗ ]  [🕓]                                                 │
└──────────────────────────────────────────────────────────────────────────────┘

           ┌─ Category divider ──────────────────────────────────────────────┐
           │ CREATE                                                          │
           └────────────────────────────────────────────────────────────────┘
┌──────────────────────────────────────────────────────────────────────────────┐
│ … further prompt rows …                                                     │
└──────────────────────────────────────────────────────────────────────────────┘

Legend:
  🔍 Quick-filter searches instantly
  #tag  – dimmed muted tags
  🕓    – version-history stub dialog
  🔥    – Trending badge (metrics placeholder)
  vN    – prompt version number
  Category dividers render at left in wide view; stack above list on mobile
```

* * *

4 Next minimal tweaks (still frontend-only)
-------------------------------------------

1. **LocalStorage favourites** → gold star + “Favorites” pseudo-category.
    
2. **Keyboard power-user keys**: `j/k` row nav, `c` to copy prompt text.
    
3. **Live diff mock**: `react-diff-viewer` comparing `version-N` strings.
    
4. **Static JSON fetch** instead of inline array to simulate API latency.
    

Let me know if you’d like the canvas updated or more details on any sub-feature!