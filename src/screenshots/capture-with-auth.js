const { chromium } = require('playwright');
const fs = require('fs');
const path = require('path');

async function captureWithAuth() {
  console.log('🚀 Starting screenshot capture with auth login...');
  
  // Create timestamped directory structure
  const now = new Date();
  const dateStr = now.toISOString().split('T')[0]; // YYYY-MM-DD
  const timeStr = now.toTimeString().split(' ')[0].replace(/:/g, '-'); // HH-MM-SS
  const screenshotDir = path.join(__dirname, 'screenshots', dateStr, timeStr, 'authenticated');
  
  // Ensure directory exists
  fs.mkdirSync(screenshotDir, { recursive: true });
  console.log(`📁 Created screenshot directory: ${screenshotDir}`);

  const browser = await chromium.launch();
  
  try {
    // Create multiple pages for parallel capture after auth
    const [authPage, detailPage, interactivePage] = await Promise.all([
      browser.newPage(),
      browser.newPage(), 
      browser.newPage()
    ]);

    // Set viewport size for all pages
    await Promise.all([
      authPage.setViewportSize({ width: 1400, height: 1000 }),
      detailPage.setViewportSize({ width: 1400, height: 1000 }),
      interactivePage.setViewportSize({ width: 1400, height: 1000 })
    ]);

    // Step 1: Handle the two-step auth login process
    console.log('🔐 Starting authentication process...');
    
    await authPage.goto('http://localhost:5174', { waitUntil: 'networkidle', timeout: 30000 });
    
    // Take screenshot of initial landing page
    await authPage.screenshot({
      path: path.join(screenshotDir, '01-landing-page.png'),
      fullPage: true
    });
    console.log('✅ Landing page screenshot captured');

    // Click the "Sign In" button (first click)
    await authPage.click('button:has-text("Sign In")');
    await authPage.waitForTimeout(1000);
    
    // Take screenshot of login modal/page
    await authPage.screenshot({
      path: path.join(screenshotDir, '02-login-modal.png'),
      fullPage: true
    });
    console.log('✅ Login modal screenshot captured');

    // Look for common login elements and perform second click
    // Try multiple selectors for the second required click
    const possibleSelectors = [
      'button:has-text("Continue")',
      'button:has-text("Login")', 
      'button:has-text("Access Library")',
      'button:has-text("Enter")',
      'button[type="submit"]',
      '.login-button',
      '.continue-button',
      '.access-button',
      'input[type="submit"]'
    ];

    let secondClickSuccess = false;
    for (const selector of possibleSelectors) {
      try {
        const element = await authPage.locator(selector).first();
        if (await element.isVisible({ timeout: 2000 })) {
          await element.click();
          await authPage.waitForTimeout(2000);
          secondClickSuccess = true;
          console.log(`✅ Second click successful with selector: ${selector}`);
          break;
        }
      } catch (error) {
        // Continue to next selector
        continue;
      }
    }

    if (!secondClickSuccess) {
      console.log('⚠️ Could not find second click element, trying generic approach');
      // Try clicking any button that's not the sign in button
      try {
        await authPage.click('button:not(:has-text("Sign In"))');
        await authPage.waitForTimeout(2000);
        secondClickSuccess = true;
        console.log('✅ Generic second click successful');
      } catch (error) {
        console.log('❌ Second click failed, continuing with current state');
      }
    }

    // Take screenshot after login attempt
    await authPage.screenshot({
      path: path.join(screenshotDir, '03-after-login.png'),
      fullPage: true
    });
    console.log('✅ Post-login screenshot captured');

    // Wait for authenticated content to load
    await authPage.waitForTimeout(3000);

    // Check if we successfully authenticated by looking for authenticated elements
    const authenticatedElements = [
      '.prompt-card',
      '.search-input', 
      '.sidebar',
      '.header',
      '.main-content'
    ];

    let authSuccess = false;
    for (const selector of authenticatedElements) {
      try {
        await authPage.waitForSelector(selector, { timeout: 5000 });
        authSuccess = true;
        console.log(`✅ Authentication successful - found ${selector}`);
        break;
      } catch (error) {
        continue;
      }
    }

    if (!authSuccess) {
      console.log('⚠️ Authentication may have failed, capturing current state');
      await authPage.screenshot({
        path: path.join(screenshotDir, '04-auth-failed-state.png'),
        fullPage: true
      });
    }

    // Copy auth state to other pages (share cookies/session)
    const cookies = await authPage.context().cookies();
    await detailPage.context().addCookies(cookies);
    await interactivePage.context().addCookies(cookies);

    // Navigate other pages to authenticated app
    await Promise.all([
      detailPage.goto('http://localhost:5174', { waitUntil: 'networkidle' }),
      interactivePage.goto('http://localhost:5174', { waitUntil: 'networkidle' })
    ]);

    console.log('✅ All pages authenticated and loaded');

    // Parallel screenshot capture functions for authenticated content
    const basicAuthCapture = async () => {
      try {
        // Full authenticated page
        await authPage.screenshot({
          path: path.join(screenshotDir, '05-authenticated-full-page.png'),
          fullPage: true
        });

        // Viewport of authenticated page
        await authPage.screenshot({
          path: path.join(screenshotDir, '06-authenticated-viewport.png'),
          fullPage: false
        });

        console.log('✅ Basic authenticated screenshots captured');
      } catch (error) {
        console.error('❌ Basic auth capture error:', error.message);
      }
    };

    const promptDetailCapture = async () => {
      try {
        // Wait for prompt cards
        await detailPage.waitForSelector('.prompt-card', { timeout: 10000 });
        
        // Press P key for detail view
        await detailPage.keyboard.press('KeyP');
        await detailPage.waitForTimeout(1500);
        
        await detailPage.screenshot({
          path: path.join(screenshotDir, '07-p-key-detail-authenticated.png'),
          fullPage: true
        });

        // Close detail and click a card
        await detailPage.keyboard.press('Escape');
        await detailPage.waitForTimeout(500);
        
        const firstCard = detailPage.locator('.prompt-card').first();
        await firstCard.click();
        await detailPage.waitForTimeout(1500);
        
        await detailPage.screenshot({
          path: path.join(screenshotDir, '08-card-click-detail-authenticated.png'),
          fullPage: true
        });

        console.log('✅ Authenticated prompt detail screenshots captured');
      } catch (error) {
        console.error('❌ Prompt detail auth capture error:', error.message);
      }
    };

    const interactiveAuthCapture = async () => {
      try {
        // Search functionality - try multiple selectors
        const searchSelectors = ['input[placeholder*="Search"]', 'input[placeholder*="prompts"]', '.search-input', 'input[type="text"]'];
        let searchFilled = false;
        
        for (const selector of searchSelectors) {
          try {
            await interactivePage.fill(selector, 'Financial');
            searchFilled = true;
            break;
          } catch (error) {
            continue;
          }
        }
        await interactivePage.waitForTimeout(1000);
        
        await interactivePage.screenshot({
          path: path.join(screenshotDir, '09-search-authenticated.png'),
          fullPage: false
        });

        // Category filtering - clear search if it was filled
        if (searchFilled) {
          for (const selector of searchSelectors) {
            try {
              await interactivePage.fill(selector, '');
              break;
            } catch (error) {
              continue;
            }
          }
        }
        await interactivePage.click('button:has-text("Investment")');
        await interactivePage.waitForTimeout(1000);
        
        await interactivePage.screenshot({
          path: path.join(screenshotDir, '10-category-filter-authenticated.png'),
          fullPage: false
        });

        // Status filtering
        await interactivePage.click('button:has-text("Investment")'); // Clear
        await interactivePage.locator('label:has-text("Approved")').click();
        await interactivePage.waitForTimeout(1000);
        
        await interactivePage.screenshot({
          path: path.join(screenshotDir, '11-status-filter-authenticated.png'),
          fullPage: false
        });

        console.log('✅ Interactive authenticated screenshots captured');
      } catch (error) {
        console.error('❌ Interactive auth capture error:', error.message);
      }
    };

    // Run all authenticated captures in parallel
    await Promise.all([
      basicAuthCapture(),
      promptDetailCapture(),
      interactiveAuthCapture()
    ]);

    // Close all pages
    await Promise.all([
      authPage.close(),
      detailPage.close(),
      interactivePage.close()
    ]);

    console.log('🎯 Authenticated screenshot capture completed successfully!');

  } catch (error) {
    console.error('❌ Error in authenticated capture:', error.message);
    
    // Try to capture error state
    try {
      const errorPage = await browser.newPage();
      await errorPage.setViewportSize({ width: 1400, height: 1000 });
      await errorPage.goto('http://localhost:5174', { timeout: 10000 });
      await errorPage.screenshot({
        path: path.join(screenshotDir, 'error-state-auth.png'),
        fullPage: true
      });
      console.log('📸 Auth error state screenshot captured');
      await errorPage.close();
    } catch (screenshotError) {
      console.error('❌ Could not take auth error screenshot:', screenshotError.message);
    }
  }
  
  await browser.close();
  console.log('🔚 Authenticated capture complete');
}

captureWithAuth();