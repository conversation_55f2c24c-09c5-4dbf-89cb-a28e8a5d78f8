const { chromium } = require('playwright');
const fs = require('fs');
const path = require('path');

async function captureAllScreenshots() {
  console.log('🚀 Starting parallel screenshot capture with auth...');
  
  // Create timestamped directory structure
  const now = new Date();
  const dateStr = now.toISOString().split('T')[0]; // YYYY-MM-DD
  const timeStr = now.toTimeString().split(' ')[0].replace(/:/g, '-'); // HH-MM-SS
  const screenshotDir = path.join(__dirname, 'screenshots', dateStr, timeStr);
  
  // Ensure directory exists
  fs.mkdirSync(screenshotDir, { recursive: true });
  console.log(`📁 Created screenshot directory: ${screenshotDir}`);

  const browser = await chromium.launch();
  
  try {
    // Create auth page first, then other pages
    const authPage = await browser.newPage();
    await authPage.setViewportSize({ width: 1400, height: 1000 });

    // Handle authentication first
    console.log('🔐 Handling authentication...');
    await authPage.goto('http://localhost:5174', { waitUntil: 'networkidle', timeout: 30000 });
    
    // Click Sign In button
    await authPage.click('button:has-text("Sign In")');
    await authPage.waitForTimeout(1000);
    
    // Try second click (generic approach that worked before)
    try {
      await authPage.click('button:not(:has-text("Sign In"))');
      await authPage.waitForTimeout(2000);
    } catch (error) {
      console.log('⚠️ Second click failed, continuing...');
    }

    // Wait for authentication
    await authPage.waitForSelector('.prompt-card', { timeout: 10000 });
    console.log('✅ Authentication successful');

    // Create additional pages and share auth cookies
    const [basicPage, interactivePage] = await Promise.all([
      browser.newPage(),
      browser.newPage()
    ]);

    // Set viewport size for both pages
    await Promise.all([
      basicPage.setViewportSize({ width: 1400, height: 1000 }),
      interactivePage.setViewportSize({ width: 1400, height: 1000 })
    ]);

    // Share cookies from auth page
    const cookies = await authPage.context().cookies();
    await Promise.all([
      basicPage.context().addCookies(cookies),
      interactivePage.context().addCookies(cookies)
    ]);

    // Navigate both pages to the authenticated app
    await Promise.all([
      basicPage.goto('http://localhost:5174', { waitUntil: 'networkidle', timeout: 30000 }),
      interactivePage.goto('http://localhost:5174', { waitUntil: 'networkidle', timeout: 30000 })
    ]);

    console.log('✅ All pages loaded and authenticated successfully');
    
    // Wait for React to render
    await Promise.all([
      basicPage.waitForTimeout(2000),
      interactivePage.waitForTimeout(2000)
    ]);

    // Capture basic screenshots in parallel
    const basicScreenshots = Promise.all([
      basicPage.screenshot({
        path: path.join(screenshotDir, 'full-page.png'),
        fullPage: true
      }),
      basicPage.screenshot({
        path: path.join(screenshotDir, 'viewport.png'),
        fullPage: false
      })
    ]);

    // Interactive screenshots - must be sequential for interactions
    const interactiveScreenshots = async () => {
      // Search functionality test - try multiple selectors
      const searchSelectors = ['input[placeholder*="Search"]', 'input[placeholder*="prompts"]', '.search-input', 'input[type="text"]'];
      let searchFilled = false;
      
      for (const selector of searchSelectors) {
        try {
          await interactivePage.fill(selector, 'Financial');
          searchFilled = true;
          break;
        } catch (error) {
          continue;
        }
      }
      await interactivePage.waitForTimeout(500);
      const searchShot = interactivePage.screenshot({
        path: path.join(screenshotDir, 'search-financial.png')
      });

      // Clear and test category filtering
      if (searchFilled) {
        for (const selector of searchSelectors) {
          try {
            await interactivePage.fill(selector, '');
            break;
          } catch (error) {
            continue;
          }
        }
      }
      await interactivePage.click('button:has-text("Investment")');
      await interactivePage.waitForTimeout(500);
      const categoryShot = interactivePage.screenshot({
        path: path.join(screenshotDir, 'category-investment.png')
      });

      // Clear category and test status filtering
      await interactivePage.click('button:has-text("Investment")');
      await interactivePage.waitForTimeout(500);
      await interactivePage.locator('label:has-text("Approved")').click();
      await interactivePage.waitForTimeout(500);
      const statusShot = interactivePage.screenshot({
        path: path.join(screenshotDir, 'status-approved.png')
      });

      return Promise.all([searchShot, categoryShot, statusShot]);
    };

    // Run basic and interactive screenshots in parallel
    await Promise.all([
      basicScreenshots,
      interactiveScreenshots()
    ]);

    console.log('✅ Full page screenshot captured');
    console.log('✅ Viewport screenshot captured');
    console.log('✅ Search "Financial" screenshot captured');
    console.log('✅ Investment category screenshot captured');
    console.log('✅ Approved status screenshot captured');

    // Close all pages
    await Promise.all([
      authPage.close(),
      basicPage.close(),
      interactivePage.close()
    ]);

  } catch (error) {
    console.error('❌ Error capturing screenshots:', error.message);
    
    // Try to capture error state
    try {
      const errorPage = await browser.newPage();
      await errorPage.setViewportSize({ width: 1400, height: 1000 });
      await errorPage.goto('http://localhost:5174', { timeout: 10000 });
      await errorPage.screenshot({
        path: path.join(screenshotDir, 'error-state.png'),
        fullPage: true
      });
      console.log('📸 Error state screenshot captured');
      await errorPage.close();
    } catch (screenshotError) {
      console.error('❌ Could not take error screenshot:', screenshotError.message);
    }
  }
  
  await browser.close();
  console.log('🔚 Parallel capture complete');
}

captureAllScreenshots();