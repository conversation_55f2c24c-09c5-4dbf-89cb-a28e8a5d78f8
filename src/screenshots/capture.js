const { chromium } = require('playwright');
const fs = require('fs');
const path = require('path');

async function captureScreenshots() {
  // Create timestamped directory structure
  const now = new Date();
  const dateStr = now.toISOString().split('T')[0]; // YYYY-MM-DD
  const timeStr = now.toTimeString().split(' ')[0].replace(/:/g, '-'); // HH-MM-SS
  const screenshotDir = path.join(__dirname, 'screenshots', dateStr, timeStr);
  
  // Ensure directory exists
  fs.mkdirSync(screenshotDir, { recursive: true });
  console.log(`📁 Created screenshot directory: ${screenshotDir}`);

  const browser = await chromium.launch();
  const page = await browser.newPage();
  
  // Set viewport size for consistent screenshots
  await page.setViewportSize({ width: 1400, height: 1000 });
  
  try {
    // Navigate to the React app
    await page.goto('http://localhost:5173');
    
    // Wait for the app to load
    await page.waitForSelector('.header', { timeout: 10000 });
    await page.waitForSelector('.sidebar', { timeout: 5000 });
    await page.waitForSelector('.prompt-card', { timeout: 5000 });
    
    // Take full page screenshot
    await page.screenshot({
      path: path.join(screenshotDir, 'full-page.png'),
      fullPage: true
    });
    
    console.log('✅ Full page screenshot captured');
    
    // Take viewport screenshot
    await page.screenshot({
      path: path.join(screenshotDir, 'viewport.png'),
      fullPage: false
    });
    
    console.log('✅ Viewport screenshot captured');
    
    // Test search functionality
    await page.fill('.search-input', 'Financial');
    await page.waitForTimeout(1000); // Wait for filtering
    
    await page.screenshot({
      path: path.join(screenshotDir, 'search-filter.png'),
      fullPage: false
    });
    
    console.log('✅ Search filter screenshot captured');
    
    // Clear search and test category filtering
    await page.fill('.search-input', '');
    await page.click('button:has-text("Investment")');
    await page.waitForTimeout(1000);
    
    await page.screenshot({
      path: path.join(screenshotDir, 'category-filter.png'),
      fullPage: false
    });
    
    console.log('✅ Category filter screenshot captured');
    
    // Test status filtering
    await page.click('button:has-text("Investment")'); // Clear category
    await page.click('input[name="status"] + .radio-custom', { force: true });
    await page.waitForTimeout(1000);
    
    await page.screenshot({
      path: path.join(screenshotDir, 'status-filter.png'),
      fullPage: false
    });
    
    console.log('✅ Status filter screenshot captured');
    
  } catch (error) {
    console.error('❌ Error capturing screenshots:', error);
  }
  
  await browser.close();
}

captureScreenshots();