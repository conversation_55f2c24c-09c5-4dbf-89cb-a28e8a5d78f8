"use strict";var Et=Object.create;var oe=Object.defineProperty;var vt=Object.getOwnPropertyDescriptor;var wt=Object.getOwnPropertyNames;var yt=Object.getPrototypeOf,gt=Object.prototype.hasOwnProperty;var v=(e,r)=>()=>(r||e((r={exports:{}}).exports,r),r.exports),Ct=(e,r)=>{for(var t in r)oe(e,t,{get:r[t],enumerable:!0})},or=(e,r,t,n)=>{if(r&&typeof r=="object"||typeof r=="function")for(let i of wt(r))!gt.call(e,i)&&i!==t&&oe(e,i,{get:()=>r[i],enumerable:!(n=vt(r,i))||n.enumerable});return e};var sr=(e,r,t)=>(t=e!=null?Et(yt(e)):{},or(r||!e||!e.__esModule?oe(t,"default",{value:e,enumerable:!0}):t,e)),bt=e=>or(oe({},"__esModule",{value:!0}),e);var Ne=v((Mn,ar)=>{var U=require("buffer").Buffer,Ae=[0,1996959894,3993919788,2567524794,124634137,1886057615,3915621685,2657392035,249268274,2044508324,3772115230,2547177864,162941995,2125561021,3887607047,2428444049,498536548,1789927666,4089016648,2227061214,450548861,1843258603,4107580753,2211677639,325883990,1684777152,4251122042,2321926636,335633487,1661365465,4195302755,2366115317,997073096,1281953886,3579855332,2724688242,1006888145,1258607687,3524101629,2768942443,901097722,1119000684,3686517206,2898065728,853044451,1172266101,3705015759,2882616665,651767980,1373503546,3369554304,3218104598,565507253,1454621731,3485111705,3099436303,671266974,1594198024,3322730930,2970347812,795835527,1483230225,3244367275,3060149565,1994146192,31158534,2563907772,4023717930,1907459465,112637215,2680153253,3904427059,2013776290,251722036,2517215374,3775830040,2137656763,141376813,2439277719,3865271297,1802195444,476864866,2238001368,4066508878,1812370925,453092731,2181625025,4111451223,1706088902,314042704,2344532202,4240017532,1658658271,366619977,2362670323,4224994405,1303535960,984961486,2747007092,3569037538,1256170817,1037604311,2765210733,3554079995,1131014506,879679996,2909243462,3663771856,1141124467,855842277,2852801631,3708648649,1342533948,654459306,3188396048,3373015174,1466479909,544179635,3110523913,3462522015,1591671054,702138776,2966460450,3352799412,1504918807,783551873,3082640443,3233442989,3988292384,2596254646,62317068,1957810842,3939845945,2647816111,81470997,1943803523,3814918930,2489596804,225274430,2053790376,3826175755,2466906013,167816743,2097651377,4027552580,2265490386,503444072,1762050814,4150417245,2154129355,426522225,1852507879,4275313526,2312317920,282753626,1742555852,4189708143,2394877945,397917763,1622183637,3604390888,2714866558,953729732,1340076626,3518719985,2797360999,1068828381,1219638859,3624741850,2936675148,906185462,1090812512,3747672003,2825379669,829329135,1181335161,3412177804,3160834842,628085408,1382605366,3423369109,3138078467,570562233,1426400815,3317316542,2998733608,733239954,1555261956,3268935591,3050360625,752459403,1541320221,2607071920,3965973030,1969922972,40735498,2617837225,3943577151,1913087877,83908371,2512341634,3803740692,2075208622,213261112,2463272603,3855990285,2094854071,198958881,2262029012,4057260610,1759359992,534414190,2176718541,4139329115,1873836001,414664567,2282248934,4279200368,1711684554,285281116,2405801727,4167216745,1634467795,376229701,2685067896,3608007406,1308918612,956543938,2808555105,3495958263,1231636301,1047427035,2932959818,3654703836,1088359270,936918e3,2847714899,3736837829,1202900863,817233897,3183342108,3401237130,1404277552,615818150,3134207493,3453421203,1423857449,601450431,3009837614,3294710456,1567103746,711928724,3020668471,3272380065,1510334235,755167117];typeof Int32Array!="undefined"&&(Ae=new Int32Array(Ae));function fr(e){if(U.isBuffer(e))return e;var r=typeof U.alloc=="function"&&typeof U.from=="function";if(typeof e=="number")return r?U.alloc(e):new U(e);if(typeof e=="string")return r?U.from(e):new U(e);throw new Error("input must be buffer, number, or string, received "+typeof e)}function Ft(e){var r=fr(4);return r.writeInt32BE(e,0),r}function Ue(e,r){e=fr(e),U.isBuffer(r)&&(r=r.readUInt32BE(0));for(var t=~~r^-1,n=0;n<e.length;n++)t=Ae[(t^e[n])&255]^t>>>8;return t^-1}function Te(){return Ft(Ue.apply(null,arguments))}Te.signed=function(){return Ue.apply(null,arguments)};Te.unsigned=function(){return Ue.apply(null,arguments)>>>0};ar.exports=Te});var Fr=v(We=>{var ur=require("fs"),ce=require("stream").Transform,cr=require("stream").PassThrough,dr=require("zlib"),Pe=require("util"),It=require("events").EventEmitter,lr=Ne();We.ZipFile=P;We.dateToDosDateTime=br;Pe.inherits(P,It);function P(){this.outputStream=new cr,this.entries=[],this.outputStreamCursor=0,this.ended=!1,this.allDone=!1,this.forceZip64Eocd=!1}P.prototype.addFile=function(e,r,t){var n=this;r=de(r,!1),t==null&&(t={});var i=new m(r,!1,t);n.entries.push(i),ur.stat(e,function(s,o){if(s)return n.emit("error",s);if(!o.isFile())return n.emit("error",new Error("not a file: "+e));i.uncompressedSize=o.size,t.mtime==null&&i.setLastModDate(o.mtime),t.mode==null&&i.setFileAttributesMode(o.mode),i.setFileDataPumpFunction(function(){var f=ur.createReadStream(e);i.state=m.FILE_DATA_IN_PROGRESS,f.on("error",function(u){n.emit("error",u)}),hr(n,i,f)}),N(n)})};P.prototype.addReadStream=function(e,r,t){var n=this;r=de(r,!1),t==null&&(t={});var i=new m(r,!1,t);n.entries.push(i),i.setFileDataPumpFunction(function(){i.state=m.FILE_DATA_IN_PROGRESS,hr(n,i,e)}),N(n)};P.prototype.addBuffer=function(e,r,t){var n=this;if(r=de(r,!1),e.length>1073741823)throw new Error("buffer too large: "+e.length+" > 1073741823");if(t==null&&(t={}),t.size!=null)throw new Error("options.size not allowed");var i=new m(r,!1,t);i.uncompressedSize=e.length,i.crc32=lr.unsigned(e),i.crcAndFileSizeKnown=!0,n.entries.push(i),i.compress?dr.deflateRaw(e,function(o,f){s(f)}):s(e);function s(o){i.compressedSize=o.length,i.setFileDataPumpFunction(function(){q(n,o),q(n,i.getDataDescriptor()),i.state=m.FILE_DATA_DONE,setImmediate(function(){N(n)})}),N(n)}};P.prototype.addEmptyDirectory=function(e,r){var t=this;if(e=de(e,!0),r==null&&(r={}),r.size!=null)throw new Error("options.size not allowed");if(r.compress!=null)throw new Error("options.compress not allowed");var n=new m(e,!0,r);t.entries.push(n),n.setFileDataPumpFunction(function(){q(t,n.getDataDescriptor()),n.state=m.FILE_DATA_DONE,N(t)}),N(t)};var St=T([80,75,5,6]);P.prototype.end=function(e,r){if(typeof e=="function"&&(r=e,e=null),e==null&&(e={}),!this.ended){if(this.ended=!0,this.finalSizeCallback=r,this.forceZip64Eocd=!!e.forceZip64Format,e.comment){if(typeof e.comment=="string"?this.comment=zt(e.comment):this.comment=e.comment,this.comment.length>65535)throw new Error("comment is too large");if(J(this.comment,St))throw new Error("comment contains end of central directory record signature")}else this.comment=le;N(this)}};function q(e,r){e.outputStream.write(r),e.outputStreamCursor+=r.length}function hr(e,r,t){var n=new Ze,i=new ue,s=r.compress?new dr.DeflateRaw:new cr,o=new ue;t.pipe(n).pipe(i).pipe(s).pipe(o).pipe(e.outputStream,{end:!1}),o.on("end",function(){if(r.crc32=n.crc32,r.uncompressedSize==null)r.uncompressedSize=i.byteCount;else if(r.uncompressedSize!==i.byteCount)return e.emit("error",new Error("file data stream has unexpected number of bytes"));r.compressedSize=o.byteCount,e.outputStreamCursor+=r.compressedSize,q(e,r.getDataDescriptor()),r.state=m.FILE_DATA_DONE,N(e)})}function N(e){if(e.allDone)return;if(e.ended&&e.finalSizeCallback!=null){var r=Lt(e);r!=null&&(e.finalSizeCallback(r),e.finalSizeCallback=null)}var t=n();function n(){for(var s=0;s<e.entries.length;s++){var o=e.entries[s];if(o.state<m.FILE_DATA_DONE)return o}return null}if(t!=null){if(t.state<m.READY_TO_PUMP_FILE_DATA||t.state===m.FILE_DATA_IN_PROGRESS)return;t.relativeOffsetOfLocalHeader=e.outputStreamCursor;var i=t.getLocalFileHeader();q(e,i),t.doFileDataPump()}else e.ended&&(e.offsetOfStartOfCentralDirectory=e.outputStreamCursor,e.entries.forEach(function(s){var o=s.getCentralDirectoryRecord();q(e,o)}),q(e,Ot(e)),e.outputStream.end(),e.allDone=!0)}function Lt(e){for(var r=0,t=0,n=0;n<e.entries.length;n++){var i=e.entries[n];if(i.compress)return-1;if(i.state>=m.READY_TO_PUMP_FILE_DATA){if(i.uncompressedSize==null)return-1}else if(i.uncompressedSize==null)return null;i.relativeOffsetOfLocalHeader=r;var s=i.useZip64Format();r+=mr+i.utf8FileName.length,r+=i.uncompressedSize,i.crcAndFileSizeKnown||(s?r+=gr:r+=yr),t+=Cr+i.utf8FileName.length+i.fileComment.length,s&&(t+=Be)}var o=0;return(e.forceZip64Eocd||e.entries.length>=65535||t>=65535||r>=4294967295)&&(o+=fe+Me),o+=ae+e.comment.length,r+t+o}var fe=56,Me=20,ae=22;function Ot(e,r){var t=!1,n=e.entries.length;(e.forceZip64Eocd||e.entries.length>=65535)&&(n=65535,t=!0);var i=e.outputStreamCursor-e.offsetOfStartOfCentralDirectory,s=i;(e.forceZip64Eocd||i>=4294967295)&&(s=4294967295,t=!0);var o=e.offsetOfStartOfCentralDirectory;if((e.forceZip64Eocd||e.offsetOfStartOfCentralDirectory>=4294967295)&&(o=4294967295,t=!0),r)return t?fe+Me+ae:ae;var f=g(ae+e.comment.length);if(f.writeUInt32LE(101010256,0),f.writeUInt16LE(0,4),f.writeUInt16LE(0,6),f.writeUInt16LE(n,8),f.writeUInt16LE(n,10),f.writeUInt32LE(s,12),f.writeUInt32LE(o,16),f.writeUInt16LE(e.comment.length,20),e.comment.copy(f,22),!t)return f;var u=g(fe);u.writeUInt32LE(101075792,0),I(u,fe-12,4),u.writeUInt16LE(Er,12),u.writeUInt16LE(xr,14),u.writeUInt32LE(0,16),u.writeUInt32LE(0,20),I(u,e.entries.length,24),I(u,e.entries.length,32),I(u,i,40),I(u,e.offsetOfStartOfCentralDirectory,48);var l=g(Me);return l.writeUInt32LE(117853008,0),l.writeUInt32LE(0,4),I(l,e.outputStreamCursor,8),l.writeUInt32LE(1,16),Buffer.concat([u,l,f])}function de(e,r){if(e==="")throw new Error("empty metadataPath");if(e=e.replace(/\\/g,"/"),/^[a-zA-Z]:/.test(e)||/^\//.test(e))throw new Error("absolute path: "+e);if(e.split("/").indexOf("..")!==-1)throw new Error("invalid relative path: "+e);var t=/\/$/.test(e);if(r)t||(e+="/");else if(t)throw new Error("file path cannot end with '/': "+e);return e}var le=g(0);function m(e,r,t){if(this.utf8FileName=T(e),this.utf8FileName.length>65535)throw new Error("utf8 file name too long. "+utf8FileName.length+" > 65535");if(this.isDirectory=r,this.state=m.WAITING_FOR_METADATA,this.setLastModDate(t.mtime!=null?t.mtime:new Date),t.mode!=null?this.setFileAttributesMode(t.mode):this.setFileAttributesMode(r?16893:33204),r?(this.crcAndFileSizeKnown=!0,this.crc32=0,this.uncompressedSize=0,this.compressedSize=0):(this.crcAndFileSizeKnown=!1,this.crc32=null,this.uncompressedSize=null,this.compressedSize=null,t.size!=null&&(this.uncompressedSize=t.size)),r?this.compress=!1:(this.compress=!0,t.compress!=null&&(this.compress=!!t.compress)),this.forceZip64Format=!!t.forceZip64Format,t.fileComment){if(typeof t.fileComment=="string"?this.fileComment=T(t.fileComment,"utf-8"):this.fileComment=t.fileComment,this.fileComment.length>65535)throw new Error("fileComment is too large")}else this.fileComment=le}m.WAITING_FOR_METADATA=0;m.READY_TO_PUMP_FILE_DATA=1;m.FILE_DATA_IN_PROGRESS=2;m.FILE_DATA_DONE=3;m.prototype.setLastModDate=function(e){var r=br(e);this.lastModFileTime=r.time,this.lastModFileDate=r.date};m.prototype.setFileAttributesMode=function(e){if((e&65535)!==e)throw new Error("invalid mode. expected: 0 <= "+e+" <= 65535");this.externalFileAttributes=e<<16>>>0};m.prototype.setFileDataPumpFunction=function(e){this.doFileDataPump=e,this.state=m.READY_TO_PUMP_FILE_DATA};m.prototype.useZip64Format=function(){return this.forceZip64Format||this.uncompressedSize!=null&&this.uncompressedSize>4294967294||this.compressedSize!=null&&this.compressedSize>4294967294||this.relativeOffsetOfLocalHeader!=null&&this.relativeOffsetOfLocalHeader>4294967294};var mr=30,pr=20,xr=45,Er=831,vr=2048,wr=8;m.prototype.getLocalFileHeader=function(){var e=0,r=0,t=0;this.crcAndFileSizeKnown&&(e=this.crc32,r=this.compressedSize,t=this.uncompressedSize);var n=g(mr),i=vr;return this.crcAndFileSizeKnown||(i|=wr),n.writeUInt32LE(67324752,0),n.writeUInt16LE(pr,4),n.writeUInt16LE(i,6),n.writeUInt16LE(this.getCompressionMethod(),8),n.writeUInt16LE(this.lastModFileTime,10),n.writeUInt16LE(this.lastModFileDate,12),n.writeUInt32LE(e,14),n.writeUInt32LE(r,18),n.writeUInt32LE(t,22),n.writeUInt16LE(this.utf8FileName.length,26),n.writeUInt16LE(0,28),Buffer.concat([n,this.utf8FileName])};var yr=16,gr=24;m.prototype.getDataDescriptor=function(){if(this.crcAndFileSizeKnown)return le;if(this.useZip64Format()){var e=g(gr);return e.writeUInt32LE(134695760,0),e.writeUInt32LE(this.crc32,4),I(e,this.compressedSize,8),I(e,this.uncompressedSize,16),e}else{var e=g(yr);return e.writeUInt32LE(134695760,0),e.writeUInt32LE(this.crc32,4),e.writeUInt32LE(this.compressedSize,8),e.writeUInt32LE(this.uncompressedSize,12),e}};var Cr=46,Be=28;m.prototype.getCentralDirectoryRecord=function(){var e=g(Cr),r=vr;this.crcAndFileSizeKnown||(r|=wr);var t=this.compressedSize,n=this.uncompressedSize,i=this.relativeOffsetOfLocalHeader,s,o;return this.useZip64Format()?(t=4294967295,n=4294967295,i=4294967295,s=xr,o=g(Be),o.writeUInt16LE(1,0),o.writeUInt16LE(Be-4,2),I(o,this.uncompressedSize,4),I(o,this.compressedSize,12),I(o,this.relativeOffsetOfLocalHeader,20)):(s=pr,o=le),e.writeUInt32LE(33639248,0),e.writeUInt16LE(Er,4),e.writeUInt16LE(s,6),e.writeUInt16LE(r,8),e.writeUInt16LE(this.getCompressionMethod(),10),e.writeUInt16LE(this.lastModFileTime,12),e.writeUInt16LE(this.lastModFileDate,14),e.writeUInt32LE(this.crc32,16),e.writeUInt32LE(t,20),e.writeUInt32LE(n,24),e.writeUInt16LE(this.utf8FileName.length,28),e.writeUInt16LE(o.length,30),e.writeUInt16LE(this.fileComment.length,32),e.writeUInt16LE(0,34),e.writeUInt16LE(0,36),e.writeUInt32LE(this.externalFileAttributes,38),e.writeUInt32LE(i,42),Buffer.concat([e,this.utf8FileName,o,this.fileComment])};m.prototype.getCompressionMethod=function(){var e=0,r=8;return this.compress?r:e};function br(e){var r=0;r|=e.getDate()&31,r|=(e.getMonth()+1&15)<<5,r|=(e.getFullYear()-1980&127)<<9;var t=0;return t|=Math.floor(e.getSeconds()/2),t|=(e.getMinutes()&63)<<5,t|=(e.getHours()&31)<<11,{date:r,time:t}}function I(e,r,t){var n=Math.floor(r/4294967296),i=r%4294967296;e.writeUInt32LE(i,t),e.writeUInt32LE(n,t+4)}Pe.inherits(ue,ce);function ue(e){ce.call(this,e),this.byteCount=0}ue.prototype._transform=function(e,r,t){this.byteCount+=e.length,t(null,e)};Pe.inherits(Ze,ce);function Ze(e){ce.call(this,e),this.crc32=0}Ze.prototype._transform=function(e,r,t){this.crc32=lr.unsigned(e,this.crc32),t(null,e)};var qe="\0\u263A\u263B\u2665\u2666\u2663\u2660\u2022\u25D8\u25CB\u25D9\u2642\u2640\u266A\u266B\u263C\u25BA\u25C4\u2195\u203C\xB6\xA7\u25AC\u21A8\u2191\u2193\u2192\u2190\u221F\u2194\u25B2\u25BC !\"#$%&'()*+,-./**********:;<=>?@ABCDEFGHIJKLMNOPQRSTUVWXYZ[\\]^_`abcdefghijklmnopqrstuvwxyz{|}~\u2302\xC7\xFC\xE9\xE2\xE4\xE0\xE5\xE7\xEA\xEB\xE8\xEF\xEE\xEC\xC4\xC5\xC9\xE6\xC6\xF4\xF6\xF2\xFB\xF9\xFF\xD6\xDC\xA2\xA3\xA5\u20A7\u0192\xE1\xED\xF3\xFA\xF1\xD1\xAA\xBA\xBF\u2310\xAC\xBD\xBC\xA1\xAB\xBB\u2591\u2592\u2593\u2502\u2524\u2561\u2562\u2556\u2555\u2563\u2551\u2557\u255D\u255C\u255B\u2510\u2514\u2534\u252C\u251C\u2500\u253C\u255E\u255F\u255A\u2554\u2569\u2566\u2560\u2550\u256C\u2567\u2568\u2564\u2565\u2559\u2558\u2552\u2553\u256B\u256A\u2518\u250C\u2588\u2584\u258C\u2590\u2580\u03B1\xDF\u0393\u03C0\u03A3\u03C3\xB5\u03C4\u03A6\u0398\u03A9\u03B4\u221E\u03C6\u03B5\u2229\u2261\xB1\u2265\u2264\u2320\u2321\xF7\u2248\xB0\u2219\xB7\u221A\u207F\xB2\u25A0\xA0";if(qe.length!==256)throw new Error("assertion failure");var se=null;function zt(e){if(/^[\x20-\x7e]*$/.test(e))return T(e,"utf-8");if(se==null){se={};for(var r=0;r<qe.length;r++)se[qe[r]]=r}for(var t=g(e.length),r=0;r<e.length;r++){var n=se[e[r]];if(n==null)throw new Error("character not encodable in CP437: "+JSON.stringify(e[r]));t[r]=n}return t}function g(e){g=r;try{return g(e)}catch{return g=t,g(e)}function r(n){return Buffer.allocUnsafe(n)}function t(n){return new Buffer(n)}}function T(e,r){T=t;try{return T(e,r)}catch{return T=n,T(e,r)}function t(i,s){return Buffer.from(i,s)}function n(i,s){return new Buffer(i,s)}}function J(e,r){J=t;try{return J(e,r)}catch{return J=n,J(e,r)}function t(i,s){return i.includes(s)}function n(i,s){for(var o=0;o<=i.length-s.length;o++)for(var f=0;;f++){if(f===s.length)return!0;if(i[o+f]!==s[f])break}return!1}}});var Or=v((qn,Lr)=>{Lr.exports=he;function he(){this.pending=0,this.max=1/0,this.listeners=[],this.waiting=[],this.error=null}he.prototype.go=function(e){this.pending<this.max?Sr(this,e):this.waiting.push(e)};he.prototype.wait=function(e){this.pending===0?e(this.error):this.listeners.push(e)};he.prototype.hold=function(){return Ir(this)};function Ir(e){e.pending+=1;var r=!1;return t;function t(i){if(r)throw new Error("callback called twice");if(r=!0,e.error=e.error||i,e.pending-=1,e.waiting.length>0&&e.pending<e.max)Sr(e,e.waiting.shift());else if(e.pending===0){var s=e.listeners;e.listeners=[],s.forEach(n)}}function n(i){i(e.error)}}function Sr(e,r){r(Ir(e))}});var _r=v(k=>{var Q=require("fs"),me=require("util"),Ge=require("stream"),zr=Ge.Readable,Ye=Ge.Writable,_t=Ge.PassThrough,Rt=Or(),pe=require("events").EventEmitter;k.createFromBuffer=Dt;k.createFromFd=At;k.BufferSlicer=D;k.FdSlicer=R;me.inherits(R,pe);function R(e,r){r=r||{},pe.call(this),this.fd=e,this.pend=new Rt,this.pend.max=1,this.refCount=0,this.autoClose=!!r.autoClose}R.prototype.read=function(e,r,t,n,i){var s=this;s.pend.go(function(o){Q.read(s.fd,e,r,t,n,function(f,u,l){o(),i(f,u,l)})})};R.prototype.write=function(e,r,t,n,i){var s=this;s.pend.go(function(o){Q.write(s.fd,e,r,t,n,function(f,u,l){o(),i(f,u,l)})})};R.prototype.createReadStream=function(e){return new xe(this,e)};R.prototype.createWriteStream=function(e){return new Ee(this,e)};R.prototype.ref=function(){this.refCount+=1};R.prototype.unref=function(){var e=this;if(e.refCount-=1,e.refCount>0)return;if(e.refCount<0)throw new Error("invalid unref");e.autoClose&&Q.close(e.fd,r);function r(t){t?e.emit("error",t):e.emit("close")}};me.inherits(xe,zr);function xe(e,r){r=r||{},zr.call(this,r),this.context=e,this.context.ref(),this.start=r.start||0,this.endOffset=r.end,this.pos=this.start,this.destroyed=!1}xe.prototype._read=function(e){var r=this;if(!r.destroyed){var t=Math.min(r._readableState.highWaterMark,e);if(r.endOffset!=null&&(t=Math.min(t,r.endOffset-r.pos)),t<=0){r.destroyed=!0,r.push(null),r.context.unref();return}r.context.pend.go(function(n){if(r.destroyed)return n();var i=Buffer.allocUnsafe(t);Q.read(r.context.fd,i,0,t,r.pos,function(s,o){s?r.destroy(s):o===0?(r.destroyed=!0,r.push(null),r.context.unref()):(r.pos+=o,r.push(i.slice(0,o))),n()})})}};xe.prototype.destroy=function(e){this.destroyed||(e=e||new Error("stream destroyed"),this.destroyed=!0,this.emit("error",e),this.context.unref())};me.inherits(Ee,Ye);function Ee(e,r){r=r||{},Ye.call(this,r),this.context=e,this.context.ref(),this.start=r.start||0,this.endOffset=r.end==null?1/0:+r.end,this.bytesWritten=0,this.pos=this.start,this.destroyed=!1,this.on("finish",this.destroy.bind(this))}Ee.prototype._write=function(e,r,t){var n=this;if(!n.destroyed){if(n.pos+e.length>n.endOffset){var i=new Error("maximum file length exceeded");i.code="ETOOBIG",n.destroy(),t(i);return}n.context.pend.go(function(s){if(n.destroyed)return s();Q.write(n.context.fd,e,0,e.length,n.pos,function(o,f){o?(n.destroy(),s(),t(o)):(n.bytesWritten+=f,n.pos+=f,n.emit("progress"),s(),t())})})}};Ee.prototype.destroy=function(){this.destroyed||(this.destroyed=!0,this.context.unref())};me.inherits(D,pe);function D(e,r){pe.call(this),r=r||{},this.refCount=0,this.buffer=e,this.maxChunkSize=r.maxChunkSize||Number.MAX_SAFE_INTEGER}D.prototype.read=function(e,r,t,n,i){if(!(0<=r&&r<=e.length))throw new RangeError("offset outside buffer: 0 <= "+r+" <= "+e.length);if(n<0)throw new RangeError("position is negative: "+n);if(r+t>e.length&&(t=e.length-r),n+t>this.buffer.length&&(t=this.buffer.length-n),t<=0){setImmediate(function(){i(null,0)});return}this.buffer.copy(e,r,n,n+t),setImmediate(function(){i(null,t)})};D.prototype.write=function(e,r,t,n,i){e.copy(this.buffer,n,r,r+t),setImmediate(function(){i(null,t,e)})};D.prototype.createReadStream=function(e){e=e||{};var r=new _t(e);r.destroyed=!1,r.start=e.start||0,r.endOffset=e.end,r.pos=r.endOffset||this.buffer.length;for(var t=this.buffer.slice(r.start,r.pos),n=0;;){var i=n+this.maxChunkSize;if(i>=t.length){n<t.length&&r.write(t.slice(n,t.length));break}r.write(t.slice(n,i)),n=i}return r.end(),r.destroy=function(){r.destroyed=!0},r};D.prototype.createWriteStream=function(e){var r=this;e=e||{};var t=new Ye(e);return t.start=e.start||0,t.endOffset=e.end==null?this.buffer.length:+e.end,t.bytesWritten=0,t.pos=t.start,t.destroyed=!1,t._write=function(n,i,s){if(!t.destroyed){var o=t.pos+n.length;if(o>t.endOffset){var f=new Error("maximum file length exceeded");f.code="ETOOBIG",t.destroyed=!0,s(f);return}n.copy(r.buffer,t.pos,0,n.length),t.bytesWritten+=n.length,t.pos=o,t.emit("progress"),s()}},t.destroy=function(){t.destroyed=!0},t};D.prototype.ref=function(){this.refCount+=1};D.prototype.unref=function(){if(this.refCount-=1,this.refCount<0)throw new Error("invalid unref")};function Dt(e,r){return new D(e,r)}function At(e,r){return new R(e,r)}});var Xe=v(C=>{var je=require("fs"),Ut=require("zlib"),Rr=_r(),Tt=Ne(),ye=require("util"),ge=require("events").EventEmitter,Dr=require("stream").Transform,Ke=require("stream").PassThrough,Nt=require("stream").Writable;C.open=Mt;C.fromFd=Ar;C.fromBuffer=Bt;C.fromRandomAccessReader=Ve;C.dosDateTimeToDate=Nr;C.getFileNameLowLevel=Mr;C.validateFileName=Br;C.parseExtraFields=qr;C.ZipFile=_;C.Entry=ee;C.LocalFileHeader=Tr;C.RandomAccessReader=M;function Mt(e,r,t){typeof r=="function"&&(t=r,r=null),r==null&&(r={}),r.autoClose==null&&(r.autoClose=!0),r.lazyEntries==null&&(r.lazyEntries=!1),r.decodeStrings==null&&(r.decodeStrings=!0),r.validateEntrySizes==null&&(r.validateEntrySizes=!0),r.strictFileNames==null&&(r.strictFileNames=!1),t==null&&(t=we),je.open(e,"r",function(n,i){if(n)return t(n);Ar(i,r,function(s,o){s&&je.close(i,we),t(s,o)})})}function Ar(e,r,t){typeof r=="function"&&(t=r,r=null),r==null&&(r={}),r.autoClose==null&&(r.autoClose=!1),r.lazyEntries==null&&(r.lazyEntries=!1),r.decodeStrings==null&&(r.decodeStrings=!0),r.validateEntrySizes==null&&(r.validateEntrySizes=!0),r.strictFileNames==null&&(r.strictFileNames=!1),t==null&&(t=we),je.fstat(e,function(n,i){if(n)return t(n);var s=Rr.createFromFd(e,{autoClose:!0});Ve(s,i.size,r,t)})}function Bt(e,r,t){typeof r=="function"&&(t=r,r=null),r==null&&(r={}),r.autoClose=!1,r.lazyEntries==null&&(r.lazyEntries=!1),r.decodeStrings==null&&(r.decodeStrings=!0),r.validateEntrySizes==null&&(r.validateEntrySizes=!0),r.strictFileNames==null&&(r.strictFileNames=!1);var n=Rr.createFromBuffer(e,{maxChunkSize:65536});Ve(n,e.length,r,t)}function Ve(e,r,t,n){typeof t=="function"&&(n=t,t=null),t==null&&(t={}),t.autoClose==null&&(t.autoClose=!0),t.lazyEntries==null&&(t.lazyEntries=!1),t.decodeStrings==null&&(t.decodeStrings=!0);var i=!!t.decodeStrings;if(t.validateEntrySizes==null&&(t.validateEntrySizes=!0),t.strictFileNames==null&&(t.strictFileNames=!1),n==null&&(n=we),typeof r!="number")throw new Error("expected totalSize parameter to be a number");if(r>Number.MAX_SAFE_INTEGER)throw new Error("zip file too large. only file sizes up to 2^52 are supported due to JavaScript's Number type being an IEEE 754 double.");e.ref();var s=22,o=20,f=65535,u=Math.min(o+s+f,r),l=A(u),a=r-l.length;j(e,l,0,u,a,function(c){if(c)return n(c);for(var d=u-s;d>=0;d-=1)if(l.readUInt32LE(d)===101010256){var h=l.subarray(d),E=h.readUInt16LE(4),p=h.readUInt16LE(10),x=h.readUInt32LE(16),L=h.readUInt16LE(20),B=h.length-s;if(L!==B)return n(new Error("Invalid comment length. Expected: "+B+". Found: "+L+". Are there extra bytes at the end of the file? Or is the end of central dir signature `PK\u263A\u263B` in the comment?"));var ne=i?ve(h.subarray(22),!1):h.subarray(22);if(d-o>=0&&l.readUInt32LE(d-o)===117853008){var G=l.subarray(d-o,d-o+o),nr=Y(G,8),O=A(56);return j(e,O,0,O.length,nr,function(ie){return ie?n(ie):O.readUInt32LE(0)!==101075792?n(new Error("invalid zip64 end of central directory record signature")):(E=O.readUInt32LE(16),E!==0?n(new Error("multi-disk zip files are not supported: found disk number: "+E)):(p=Y(O,32),x=Y(O,48),n(null,new _(e,x,r,p,ne,t.autoClose,t.lazyEntries,i,t.validateEntrySizes,t.strictFileNames))))})}return E!==0?n(new Error("multi-disk zip files are not supported: found disk number: "+E)):n(null,new _(e,x,r,p,ne,t.autoClose,t.lazyEntries,i,t.validateEntrySizes,t.strictFileNames))}n(new Error("End of central directory record signature not found. Either not a zip file, or file is truncated."))})}ye.inherits(_,ge);function _(e,r,t,n,i,s,o,f,u,l){var a=this;ge.call(a),a.reader=e,a.reader.on("error",function(c){Ur(a,c)}),a.reader.once("close",function(){a.emit("close")}),a.readEntryCursor=r,a.fileSize=t,a.entryCount=n,a.comment=i,a.entriesRead=0,a.autoClose=!!s,a.lazyEntries=!!o,a.decodeStrings=!!f,a.validateEntrySizes=!!u,a.strictFileNames=!!l,a.isOpen=!0,a.emittedError=!1,a.lazyEntries||a._readEntry()}_.prototype.close=function(){this.isOpen&&(this.isOpen=!1,this.reader.unref())};function z(e,r){e.autoClose&&e.close(),Ur(e,r)}function Ur(e,r){e.emittedError||(e.emittedError=!0,e.emit("error",r))}_.prototype.readEntry=function(){if(!this.lazyEntries)throw new Error("readEntry() called without lazyEntries:true");this._readEntry()};_.prototype._readEntry=function(){var e=this;if(e.entryCount===e.entriesRead){setImmediate(function(){e.autoClose&&e.close(),!e.emittedError&&e.emit("end")});return}if(!e.emittedError){var r=A(46);j(e.reader,r,0,r.length,e.readEntryCursor,function(t){if(t)return z(e,t);if(!e.emittedError){var n=new ee,i=r.readUInt32LE(0);if(i!==33639248)return z(e,new Error("invalid central directory file header signature: 0x"+i.toString(16)));if(n.versionMadeBy=r.readUInt16LE(4),n.versionNeededToExtract=r.readUInt16LE(6),n.generalPurposeBitFlag=r.readUInt16LE(8),n.compressionMethod=r.readUInt16LE(10),n.lastModFileTime=r.readUInt16LE(12),n.lastModFileDate=r.readUInt16LE(14),n.crc32=r.readUInt32LE(16),n.compressedSize=r.readUInt32LE(20),n.uncompressedSize=r.readUInt32LE(24),n.fileNameLength=r.readUInt16LE(28),n.extraFieldLength=r.readUInt16LE(30),n.fileCommentLength=r.readUInt16LE(32),n.internalFileAttributes=r.readUInt16LE(36),n.externalFileAttributes=r.readUInt32LE(38),n.relativeOffsetOfLocalHeader=r.readUInt32LE(42),n.generalPurposeBitFlag&64)return z(e,new Error("strong encryption is not supported"));e.readEntryCursor+=46,r=A(n.fileNameLength+n.extraFieldLength+n.fileCommentLength),j(e.reader,r,0,r.length,e.readEntryCursor,function(s){if(s)return z(e,s);if(!e.emittedError){n.fileNameRaw=r.subarray(0,n.fileNameLength);var o=n.fileNameLength+n.extraFieldLength;n.extraFieldRaw=r.subarray(n.fileNameLength,o),n.fileCommentRaw=r.subarray(o,o+n.fileCommentLength);try{n.extraFields=qr(n.extraFieldRaw)}catch(p){return z(e,p)}if(e.decodeStrings){var f=(n.generalPurposeBitFlag&2048)!==0;n.fileComment=ve(n.fileCommentRaw,f),n.fileName=Mr(n.generalPurposeBitFlag,n.fileNameRaw,n.extraFields,e.strictFileNames);var u=Br(n.fileName);if(u!=null)return z(e,new Error(u))}else n.fileComment=n.fileCommentRaw,n.fileName=n.fileNameRaw;n.comment=n.fileComment,e.readEntryCursor+=r.length,e.entriesRead+=1;for(var l=0;l<n.extraFields.length;l++){var a=n.extraFields[l];if(a.id===1){var c=a.data,d=0;if(n.uncompressedSize===4294967295){if(d+8>c.length)return z(e,new Error("zip64 extended information extra field does not include uncompressed size"));n.uncompressedSize=Y(c,d),d+=8}if(n.compressedSize===4294967295){if(d+8>c.length)return z(e,new Error("zip64 extended information extra field does not include compressed size"));n.compressedSize=Y(c,d),d+=8}if(n.relativeOffsetOfLocalHeader===4294967295){if(d+8>c.length)return z(e,new Error("zip64 extended information extra field does not include relative header offset"));n.relativeOffsetOfLocalHeader=Y(c,d),d+=8}break}}if(e.validateEntrySizes&&n.compressionMethod===0){var h=n.uncompressedSize;if(n.isEncrypted()&&(h+=12),n.compressedSize!==h){var E="compressed/uncompressed size mismatch for stored file: "+n.compressedSize+" != "+n.uncompressedSize;return z(e,new Error(E))}}e.emit("entry",n),e.lazyEntries||e._readEntry()}})}})}};_.prototype.openReadStream=function(e,r,t){var n=this,i=0,s=e.compressedSize;if(t==null&&(t=r,r=null),r==null)r={};else{if(r.decrypt!=null){if(!e.isEncrypted())throw new Error("options.decrypt can only be specified for encrypted entries");if(r.decrypt!==!1)throw new Error("invalid options.decrypt value: "+r.decrypt);if(e.isCompressed()&&r.decompress!==!1)throw new Error("entry is encrypted and compressed, and options.decompress !== false")}if(r.decompress!=null){if(!e.isCompressed())throw new Error("options.decompress can only be specified for compressed entries");if(!(r.decompress===!1||r.decompress===!0))throw new Error("invalid options.decompress value: "+r.decompress)}if(r.start!=null||r.end!=null){if(e.isCompressed()&&r.decompress!==!1)throw new Error("start/end range not allowed for compressed entry without options.decompress === false");if(e.isEncrypted()&&r.decrypt!==!1)throw new Error("start/end range not allowed for encrypted entry without options.decrypt === false")}if(r.start!=null){if(i=r.start,i<0)throw new Error("options.start < 0");if(i>e.compressedSize)throw new Error("options.start > entry.compressedSize")}if(r.end!=null){if(s=r.end,s<0)throw new Error("options.end < 0");if(s>e.compressedSize)throw new Error("options.end > entry.compressedSize");if(s<i)throw new Error("options.end < options.start")}}if(!n.isOpen)return t(new Error("closed"));if(e.isEncrypted()&&r.decrypt!==!1)return t(new Error("entry is encrypted, and options.decrypt !== false"));var o;if(e.compressionMethod===0)o=!1;else if(e.compressionMethod===8)o=r.decompress!=null?r.decompress:!0;else return t(new Error("unsupported compression method: "+e.compressionMethod));n.readLocalFileHeader(e,{minimal:!0},function(f,u){if(f)return t(f);n.openReadStreamLowLevel(u.fileDataStart,e.compressedSize,i,s,o,e.uncompressedSize,t)})};_.prototype.openReadStreamLowLevel=function(e,r,t,n,i,s,o){var f=this,u=e+r,l=f.reader.createReadStream({start:e+t,end:e+n}),a=l;if(i){var c=!1,d=Ut.createInflateRaw();l.on("error",function(h){setImmediate(function(){c||d.emit("error",h)})}),l.pipe(d),f.validateEntrySizes?(a=new re(s),d.on("error",function(h){setImmediate(function(){c||a.emit("error",h)})}),d.pipe(a)):a=d,He(a,function(){c=!0,d!==a&&d.unpipe(a),l.unpipe(d),l.destroy()})}o(null,a)};_.prototype.readLocalFileHeader=function(e,r,t){var n=this;t==null&&(t=r,r=null),r==null&&(r={}),n.reader.ref();var i=A(30);j(n.reader,i,0,i.length,e.relativeOffsetOfLocalHeader,function(s){try{if(s)return t(s);var o=i.readUInt32LE(0);if(o!==67324752)return t(new Error("invalid local file header signature: 0x"+o.toString(16)));var f=i.readUInt16LE(26),u=i.readUInt16LE(28),l=e.relativeOffsetOfLocalHeader+30+f+u;if(l+e.compressedSize>n.fileSize)return t(new Error("file data overflows file bounds: "+l+" + "+e.compressedSize+" > "+n.fileSize));if(r.minimal)return t(null,{fileDataStart:l});var a=new Tr;a.fileDataStart=l,a.versionNeededToExtract=i.readUInt16LE(4),a.generalPurposeBitFlag=i.readUInt16LE(6),a.compressionMethod=i.readUInt16LE(8),a.lastModFileTime=i.readUInt16LE(10),a.lastModFileDate=i.readUInt16LE(12),a.crc32=i.readUInt32LE(14),a.compressedSize=i.readUInt32LE(18),a.uncompressedSize=i.readUInt32LE(22),a.fileNameLength=f,a.extraFieldLength=u,i=A(f+u),n.reader.ref(),j(n.reader,i,0,i.length,e.relativeOffsetOfLocalHeader+30,function(c){try{return c?t(c):(a.fileName=i.subarray(0,f),a.extraField=i.subarray(f),t(null,a))}finally{n.reader.unref()}})}finally{n.reader.unref()}})};function ee(){}ee.prototype.getLastModDate=function(e){if(e==null&&(e={}),!e.forceDosFormat)for(var r=0;r<this.extraFields.length;r++){var t=this.extraFields[r];if(t.id===21589){var n=t.data;if(n.length<5)continue;var i=n[0],s=1;if(!(i&s))continue;var o=n.readInt32LE(1);return new Date(o*1e3)}else if(t.id===10)for(var n=t.data,f=4;f<n.length+4;){var u=n.readUInt16LE(f);f+=2;var l=n.readUInt16LE(f);if(f+=2,u!==1){f+=l;continue}if(l<8||f+l>n.length)break;var a=4294967296*n.readInt32LE(f+4)+n.readUInt32LE(f),c=a/1e4-116444736e5;return new Date(c)}}return Nr(this.lastModFileDate,this.lastModFileTime,e.timezone)};ee.prototype.isEncrypted=function(){return(this.generalPurposeBitFlag&1)!==0};ee.prototype.isCompressed=function(){return this.compressionMethod===8};function Tr(){}function Nr(e,r,t){var n=e&31,i=(e>>5&15)-1,s=(e>>9&127)+1980,o=0,f=(r&31)*2,u=r>>5&63,l=r>>11&31;if(t==null||t==="local")return new Date(s,i,n,l,u,f,o);if(t==="UTC")return new Date(Date.UTC(s,i,n,l,u,f,o));throw new Error("unrecognized options.timezone: "+options.timezone)}function Mr(e,r,t,n){for(var i=null,s=0;s<t.length;s++){var o=t[s];if(o.id===28789){if(o.data.length<6||o.data.readUInt8(0)!==1)continue;var f=o.data.readUInt32LE(1);if(Tt.unsigned(r)!==f)continue;i=ve(o.data.subarray(5),!0);break}}if(i==null){var u=(e&2048)!==0;i=ve(r,u)}return n||(i=i.replace(/\\/g,"/")),i}function Br(e){return e.indexOf("\\")!==-1?"invalid characters in fileName: "+e:/^[a-zA-Z]:/.test(e)||/^\//.test(e)?"absolute path: "+e:e.split("/").indexOf("..")!==-1?"invalid relative path: "+e:null}function qr(e){for(var r=[],t=0;t<e.length-3;){var n=e.readUInt16LE(t+0),i=e.readUInt16LE(t+2),s=t+4,o=s+i;if(o>e.length)throw new Error("extra field length exceeds extra field buffer size");var f=e.subarray(s,o);r.push({id:n,data:f}),t=o}return r}function j(e,r,t,n,i,s){if(n===0)return setImmediate(function(){s(null,A(0))});e.read(r,t,n,i,function(o,f){if(o)return s(o);if(f<n)return s(new Error("unexpected EOF"));s()})}ye.inherits(re,Dr);function re(e){Dr.call(this),this.actualByteCount=0,this.expectedByteCount=e}re.prototype._transform=function(e,r,t){if(this.actualByteCount+=e.length,this.actualByteCount>this.expectedByteCount){var n="too many bytes in the stream. expected "+this.expectedByteCount+". got at least "+this.actualByteCount;return t(new Error(n))}t(null,e)};re.prototype._flush=function(e){if(this.actualByteCount<this.expectedByteCount){var r="not enough bytes in the stream. expected "+this.expectedByteCount+". got only "+this.actualByteCount;return e(new Error(r))}e()};ye.inherits(M,ge);function M(){ge.call(this),this.refCount=0}M.prototype.ref=function(){this.refCount+=1};M.prototype.unref=function(){var e=this;if(e.refCount-=1,e.refCount>0)return;if(e.refCount<0)throw new Error("invalid unref");e.close(r);function r(t){if(t)return e.emit("error",t);e.emit("close")}};M.prototype.createReadStream=function(e){e==null&&(e={});var r=e.start,t=e.end;if(r===t){var n=new Ke;return setImmediate(function(){n.end()}),n}var i=this._readStreamForRange(r,t),s=!1,o=new Ce(this);i.on("error",function(u){setImmediate(function(){s||o.emit("error",u)})}),He(o,function(){i.unpipe(o),o.unref(),i.destroy()});var f=new re(t-r);return o.on("error",function(u){setImmediate(function(){s||f.emit("error",u)})}),He(f,function(){s=!0,o.unpipe(f),o.destroy()}),i.pipe(o).pipe(f)};M.prototype._readStreamForRange=function(e,r){throw new Error("not implemented")};M.prototype.read=function(e,r,t,n,i){var s=this.createReadStream({start:n,end:n+t}),o=new Nt,f=0;o._write=function(u,l,a){u.copy(e,r+f,0,u.length),f+=u.length,a()},o.on("finish",i),s.on("error",function(u){i(u)}),s.pipe(o)};M.prototype.close=function(e){setImmediate(e)};ye.inherits(Ce,Ke);function Ce(e){Ke.call(this),this.context=e,this.context.ref(),this.unreffedYet=!1}Ce.prototype._flush=function(e){this.unref(),e()};Ce.prototype.unref=function(e){this.unreffedYet||(this.unreffedYet=!0,this.context.unref())};var qt="\0\u263A\u263B\u2665\u2666\u2663\u2660\u2022\u25D8\u25CB\u25D9\u2642\u2640\u266A\u266B\u263C\u25BA\u25C4\u2195\u203C\xB6\xA7\u25AC\u21A8\u2191\u2193\u2192\u2190\u221F\u2194\u25B2\u25BC !\"#$%&'()*+,-./**********:;<=>?@ABCDEFGHIJKLMNOPQRSTUVWXYZ[\\]^_`abcdefghijklmnopqrstuvwxyz{|}~\u2302\xC7\xFC\xE9\xE2\xE4\xE0\xE5\xE7\xEA\xEB\xE8\xEF\xEE\xEC\xC4\xC5\xC9\xE6\xC6\xF4\xF6\xF2\xFB\xF9\xFF\xD6\xDC\xA2\xA3\xA5\u20A7\u0192\xE1\xED\xF3\xFA\xF1\xD1\xAA\xBA\xBF\u2310\xAC\xBD\xBC\xA1\xAB\xBB\u2591\u2592\u2593\u2502\u2524\u2561\u2562\u2556\u2555\u2563\u2551\u2557\u255D\u255C\u255B\u2510\u2514\u2534\u252C\u251C\u2500\u253C\u255E\u255F\u255A\u2554\u2569\u2566\u2560\u2550\u256C\u2567\u2568\u2564\u2565\u2559\u2558\u2552\u2553\u256B\u256A\u2518\u250C\u2588\u2584\u258C\u2590\u2580\u03B1\xDF\u0393\u03C0\u03A3\u03C3\xB5\u03C4\u03A6\u0398\u03A9\u03B4\u221E\u03C6\u03B5\u2229\u2261\xB1\u2265\u2264\u2320\u2321\xF7\u2248\xB0\u2219\xB7\u221A\u207F\xB2\u25A0\xA0";function ve(e,r){if(r)return e.toString("utf8");for(var t="",n=0;n<e.length;n++)t+=qt[e[n]];return t}function Y(e,r){var t=e.readUInt32LE(r),n=e.readUInt32LE(r+4);return n*4294967296+t}var A;typeof Buffer.allocUnsafe=="function"?A=function(e){return Buffer.allocUnsafe(e)}:A=function(e){return new Buffer(e)};function He(e,r){typeof e.destroy=="function"?e._destroy=function(t,n){r(),n!=null&&n(t)}:e.destroy=r}function we(e){if(e)throw e}});var Zr=v((Wn,Pr)=>{var H=1e3,K=H*60,V=K*60,Z=V*24,Pt=Z*7,Zt=Z*365.25;Pr.exports=function(e,r){r=r||{};var t=typeof e;if(t==="string"&&e.length>0)return Wt(e);if(t==="number"&&isFinite(e))return r.long?Yt(e):Gt(e);throw new Error("val is not a non-empty string or a valid number. val="+JSON.stringify(e))};function Wt(e){if(e=String(e),!(e.length>100)){var r=/^(-?(?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(e);if(r){var t=parseFloat(r[1]),n=(r[2]||"ms").toLowerCase();switch(n){case"years":case"year":case"yrs":case"yr":case"y":return t*Zt;case"weeks":case"week":case"w":return t*Pt;case"days":case"day":case"d":return t*Z;case"hours":case"hour":case"hrs":case"hr":case"h":return t*V;case"minutes":case"minute":case"mins":case"min":case"m":return t*K;case"seconds":case"second":case"secs":case"sec":case"s":return t*H;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return t;default:return}}}}function Gt(e){var r=Math.abs(e);return r>=Z?Math.round(e/Z)+"d":r>=V?Math.round(e/V)+"h":r>=K?Math.round(e/K)+"m":r>=H?Math.round(e/H)+"s":e+"ms"}function Yt(e){var r=Math.abs(e);return r>=Z?be(e,r,Z,"day"):r>=V?be(e,r,V,"hour"):r>=K?be(e,r,K,"minute"):r>=H?be(e,r,H,"second"):e+" ms"}function be(e,r,t,n){var i=r>=t*1.5;return Math.round(e/t)+" "+n+(i?"s":"")}});var $e=v((Gn,Wr)=>{function jt(e){t.debug=t,t.default=t,t.coerce=u,t.disable=o,t.enable=i,t.enabled=f,t.humanize=Zr(),t.destroy=l,Object.keys(e).forEach(a=>{t[a]=e[a]}),t.names=[],t.skips=[],t.formatters={};function r(a){let c=0;for(let d=0;d<a.length;d++)c=(c<<5)-c+a.charCodeAt(d),c|=0;return t.colors[Math.abs(c)%t.colors.length]}t.selectColor=r;function t(a){let c,d=null,h,E;function p(...x){if(!p.enabled)return;let L=p,B=Number(new Date),ne=B-(c||B);L.diff=ne,L.prev=c,L.curr=B,c=B,x[0]=t.coerce(x[0]),typeof x[0]!="string"&&x.unshift("%O");let G=0;x[0]=x[0].replace(/%([a-zA-Z%])/g,(O,ie)=>{if(O==="%%")return"%";G++;let ir=t.formatters[ie];if(typeof ir=="function"){let xt=x[G];O=ir.call(L,xt),x.splice(G,1),G--}return O}),t.formatArgs.call(L,x),(L.log||t.log).apply(L,x)}return p.namespace=a,p.useColors=t.useColors(),p.color=t.selectColor(a),p.extend=n,p.destroy=t.destroy,Object.defineProperty(p,"enabled",{enumerable:!0,configurable:!1,get:()=>d!==null?d:(h!==t.namespaces&&(h=t.namespaces,E=t.enabled(a)),E),set:x=>{d=x}}),typeof t.init=="function"&&t.init(p),p}function n(a,c){let d=t(this.namespace+(typeof c=="undefined"?":":c)+a);return d.log=this.log,d}function i(a){t.save(a),t.namespaces=a,t.names=[],t.skips=[];let c=(typeof a=="string"?a:"").trim().replace(" ",",").split(",").filter(Boolean);for(let d of c)d[0]==="-"?t.skips.push(d.slice(1)):t.names.push(d)}function s(a,c){let d=0,h=0,E=-1,p=0;for(;d<a.length;)if(h<c.length&&(c[h]===a[d]||c[h]==="*"))c[h]==="*"?(E=h,p=d,h++):(d++,h++);else if(E!==-1)h=E+1,p++,d=p;else return!1;for(;h<c.length&&c[h]==="*";)h++;return h===c.length}function o(){let a=[...t.names,...t.skips.map(c=>"-"+c)].join(",");return t.enable(""),a}function f(a){for(let c of t.skips)if(s(a,c))return!1;for(let c of t.names)if(s(a,c))return!0;return!1}function u(a){return a instanceof Error?a.stack||a.message:a}function l(){console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.")}return t.enable(t.load()),t}Wr.exports=jt});var Gr=v((b,Fe)=>{b.formatArgs=Kt;b.save=Vt;b.load=Xt;b.useColors=Ht;b.storage=$t();b.destroy=(()=>{let e=!1;return()=>{e||(e=!0,console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."))}})();b.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"];function Ht(){if(typeof window!="undefined"&&window.process&&(window.process.type==="renderer"||window.process.__nwjs))return!0;if(typeof navigator!="undefined"&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/))return!1;let e;return typeof document!="undefined"&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||typeof window!="undefined"&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||typeof navigator!="undefined"&&navigator.userAgent&&(e=navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/))&&parseInt(e[1],10)>=31||typeof navigator!="undefined"&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/)}function Kt(e){if(e[0]=(this.useColors?"%c":"")+this.namespace+(this.useColors?" %c":" ")+e[0]+(this.useColors?"%c ":" ")+"+"+Fe.exports.humanize(this.diff),!this.useColors)return;let r="color: "+this.color;e.splice(1,0,r,"color: inherit");let t=0,n=0;e[0].replace(/%[a-zA-Z%]/g,i=>{i!=="%%"&&(t++,i==="%c"&&(n=t))}),e.splice(n,0,r)}b.log=console.debug||console.log||(()=>{});function Vt(e){try{e?b.storage.setItem("debug",e):b.storage.removeItem("debug")}catch{}}function Xt(){let e;try{e=b.storage.getItem("debug")}catch{}return!e&&typeof process!="undefined"&&"env"in process&&(e=process.env.DEBUG),e}function $t(){try{return localStorage}catch{}}Fe.exports=$e()(b);var{formatters:Jt}=Fe.exports;Jt.j=function(e){try{return JSON.stringify(e)}catch(r){return"[UnexpectedJSONParseError]: "+r.message}}});var jr=v((Yn,Yr)=>{"use strict";Yr.exports=(e,r=process.argv)=>{let t=e.startsWith("-")?"":e.length===1?"-":"--",n=r.indexOf(t+e),i=r.indexOf("--");return n!==-1&&(i===-1||n<i)}});var Vr=v((jn,Kr)=>{"use strict";var Qt=require("os"),Hr=require("tty"),F=jr(),{env:w}=process,Ie;F("no-color")||F("no-colors")||F("color=false")||F("color=never")?Ie=0:(F("color")||F("colors")||F("color=true")||F("color=always"))&&(Ie=1);function kt(){if("FORCE_COLOR"in w)return w.FORCE_COLOR==="true"?1:w.FORCE_COLOR==="false"?0:w.FORCE_COLOR.length===0?1:Math.min(Number.parseInt(w.FORCE_COLOR,10),3)}function en(e){return e===0?!1:{level:e,hasBasic:!0,has256:e>=2,has16m:e>=3}}function rn(e,{streamIsTTY:r,sniffFlags:t=!0}={}){let n=kt();n!==void 0&&(Ie=n);let i=t?Ie:n;if(i===0)return 0;if(t){if(F("color=16m")||F("color=full")||F("color=truecolor"))return 3;if(F("color=256"))return 2}if(e&&!r&&i===void 0)return 0;let s=i||0;if(w.TERM==="dumb")return s;if(process.platform==="win32"){let o=Qt.release().split(".");return Number(o[0])>=10&&Number(o[2])>=10586?Number(o[2])>=14931?3:2:1}if("CI"in w)return["TRAVIS","CIRCLECI","APPVEYOR","GITLAB_CI","GITHUB_ACTIONS","BUILDKITE","DRONE"].some(o=>o in w)||w.CI_NAME==="codeship"?1:s;if("TEAMCITY_VERSION"in w)return/^(9\.(0*[1-9]\d*)\.|\d{2,}\.)/.test(w.TEAMCITY_VERSION)?1:0;if(w.COLORTERM==="truecolor")return 3;if("TERM_PROGRAM"in w){let o=Number.parseInt((w.TERM_PROGRAM_VERSION||"").split(".")[0],10);switch(w.TERM_PROGRAM){case"iTerm.app":return o>=3?3:2;case"Apple_Terminal":return 2}}return/-256(color)?$/i.test(w.TERM)?2:/^screen|^xterm|^vt100|^vt220|^rxvt|color|ansi|cygwin|linux/i.test(w.TERM)||"COLORTERM"in w?1:s}function Je(e,r={}){let t=rn(e,{streamIsTTY:e&&e.isTTY,...r});return en(t)}Kr.exports={supportsColor:Je,stdout:Je({isTTY:Hr.isatty(1)}),stderr:Je({isTTY:Hr.isatty(2)})}});var $r=v((y,Le)=>{var tn=require("tty"),Se=require("util");y.init=cn;y.log=fn;y.formatArgs=on;y.save=an;y.load=un;y.useColors=nn;y.destroy=Se.deprecate(()=>{},"Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.");y.colors=[6,2,3,4,5,1];try{let e=Vr();e&&(e.stderr||e).level>=2&&(y.colors=[20,21,26,27,32,33,38,39,40,41,42,43,44,45,56,57,62,63,68,69,74,75,76,77,78,79,80,81,92,93,98,99,112,113,128,129,134,135,148,149,160,161,162,163,164,165,166,167,168,169,170,171,172,173,178,179,184,185,196,197,198,199,200,201,202,203,204,205,206,207,208,209,214,215,220,221])}catch{}y.inspectOpts=Object.keys(process.env).filter(e=>/^debug_/i.test(e)).reduce((e,r)=>{let t=r.substring(6).toLowerCase().replace(/_([a-z])/g,(i,s)=>s.toUpperCase()),n=process.env[r];return/^(yes|on|true|enabled)$/i.test(n)?n=!0:/^(no|off|false|disabled)$/i.test(n)?n=!1:n==="null"?n=null:n=Number(n),e[t]=n,e},{});function nn(){return"colors"in y.inspectOpts?!!y.inspectOpts.colors:tn.isatty(process.stderr.fd)}function on(e){let{namespace:r,useColors:t}=this;if(t){let n=this.color,i="\x1B[3"+(n<8?n:"8;5;"+n),s=`  ${i};1m${r} \x1B[0m`;e[0]=s+e[0].split(`
`).join(`
`+s),e.push(i+"m+"+Le.exports.humanize(this.diff)+"\x1B[0m")}else e[0]=sn()+r+" "+e[0]}function sn(){return y.inspectOpts.hideDate?"":new Date().toISOString()+" "}function fn(...e){return process.stderr.write(Se.formatWithOptions(y.inspectOpts,...e)+`
`)}function an(e){e?process.env.DEBUG=e:delete process.env.DEBUG}function un(){return process.env.DEBUG}function cn(e){e.inspectOpts={};let r=Object.keys(y.inspectOpts);for(let t=0;t<r.length;t++)e.inspectOpts[r[t]]=y.inspectOpts[r[t]]}Le.exports=$e()(y);var{formatters:Xr}=Le.exports;Xr.o=function(e){return this.inspectOpts.colors=this.useColors,Se.inspect(e,this.inspectOpts).split(`
`).map(r=>r.trim()).join(" ")};Xr.O=function(e){return this.inspectOpts.colors=this.useColors,Se.inspect(e,this.inspectOpts)}});var Jr=v((Hn,Qe)=>{typeof process=="undefined"||process.type==="renderer"||process.browser===!0||process.__nwjs?Qe.exports=Gr():Qe.exports=$r()});var et=v((Kn,kr)=>{kr.exports=Qr;function Qr(e,r){if(e&&r)return Qr(e)(r);if(typeof e!="function")throw new TypeError("need wrapper function");return Object.keys(e).forEach(function(n){t[n]=e[n]}),t;function t(){for(var n=new Array(arguments.length),i=0;i<n.length;i++)n[i]=arguments[i];var s=e.apply(this,n),o=n[n.length-1];return typeof s=="function"&&s!==o&&Object.keys(o).forEach(function(f){s[f]=o[f]}),s}}});var er=v((Vn,ke)=>{var rt=et();ke.exports=rt(Oe);ke.exports.strict=rt(tt);Oe.proto=Oe(function(){Object.defineProperty(Function.prototype,"once",{value:function(){return Oe(this)},configurable:!0}),Object.defineProperty(Function.prototype,"onceStrict",{value:function(){return tt(this)},configurable:!0})});function Oe(e){var r=function(){return r.called?r.value:(r.called=!0,r.value=e.apply(this,arguments))};return r.called=!1,r}function tt(e){var r=function(){if(r.called)throw new Error(r.onceError);return r.called=!0,r.value=e.apply(this,arguments)},t=e.name||"Function wrapped with `once`";return r.onceError=t+" shouldn't be called more than once",r.called=!1,r}});var ot=v((Xn,it)=>{var dn=er(),ln=function(){},hn=function(e){return e.setHeader&&typeof e.abort=="function"},mn=function(e){return e.stdio&&Array.isArray(e.stdio)&&e.stdio.length===3},nt=function(e,r,t){if(typeof r=="function")return nt(e,null,r);r||(r={}),t=dn(t||ln);var n=e._writableState,i=e._readableState,s=r.readable||r.readable!==!1&&e.readable,o=r.writable||r.writable!==!1&&e.writable,f=!1,u=function(){e.writable||l()},l=function(){o=!1,s||t.call(e)},a=function(){s=!1,o||t.call(e)},c=function(x){t.call(e,x?new Error("exited with error code: "+x):null)},d=function(x){t.call(e,x)},h=function(){process.nextTick(E)},E=function(){if(!f){if(s&&!(i&&i.ended&&!i.destroyed))return t.call(e,new Error("premature close"));if(o&&!(n&&n.ended&&!n.destroyed))return t.call(e,new Error("premature close"))}},p=function(){e.req.on("finish",l)};return hn(e)?(e.on("complete",l),e.on("abort",h),e.req?p():e.on("request",p)):o&&!n&&(e.on("end",u),e.on("close",u)),mn(e)&&e.on("exit",c),e.on("end",a),e.on("finish",l),r.error!==!1&&e.on("error",d),e.on("close",h),function(){f=!0,e.removeListener("complete",l),e.removeListener("abort",h),e.removeListener("request",p),e.req&&e.req.removeListener("finish",l),e.removeListener("end",u),e.removeListener("close",u),e.removeListener("finish",l),e.removeListener("exit",c),e.removeListener("end",a),e.removeListener("error",d),e.removeListener("close",h)}};it.exports=nt});var at=v(($n,ft)=>{var pn=er(),xn=ot(),ze;try{ze=require("fs")}catch{}var te=function(){},En=/^v?\.0/.test(process.version),_e=function(e){return typeof e=="function"},vn=function(e){return!En||!ze?!1:(e instanceof(ze.ReadStream||te)||e instanceof(ze.WriteStream||te))&&_e(e.close)},wn=function(e){return e.setHeader&&_e(e.abort)},yn=function(e,r,t,n){n=pn(n);var i=!1;e.on("close",function(){i=!0}),xn(e,{readable:r,writable:t},function(o){if(o)return n(o);i=!0,n()});var s=!1;return function(o){if(!i&&!s){if(s=!0,vn(e))return e.close(te);if(wn(e))return e.abort();if(_e(e.destroy))return e.destroy();n(o||new Error("stream was destroyed"))}}},st=function(e){e()},gn=function(e,r){return e.pipe(r)},Cn=function(){var e=Array.prototype.slice.call(arguments),r=_e(e[e.length-1]||te)&&e.pop()||te;if(Array.isArray(e[0])&&(e=e[0]),e.length<2)throw new Error("pump requires two streams per minimum");var t,n=e.map(function(i,s){var o=s<e.length-1,f=s>0;return yn(i,o,f,function(u){t||(t=u),u&&n.forEach(st),!o&&(n.forEach(st),r(t))})});return e.reduce(gn)};ft.exports=Cn});var ct=v((Jn,ut)=>{"use strict";var{PassThrough:bn}=require("stream");ut.exports=e=>{e={...e};let{array:r}=e,{encoding:t}=e,n=t==="buffer",i=!1;r?i=!(t||n):t=t||"utf8",n&&(t=null);let s=new bn({objectMode:i});t&&s.setEncoding(t);let o=0,f=[];return s.on("data",u=>{f.push(u),i?o=f.length:o+=u.length}),s.getBufferedValue=()=>r?f:n?Buffer.concat(f,o):f.join(""),s.getBufferedLength=()=>o,s}});var dt=v((Qn,X)=>{"use strict";var{constants:Fn}=require("buffer"),In=at(),Sn=ct(),Re=class extends Error{constructor(){super("maxBuffer exceeded"),this.name="MaxBufferError"}};async function De(e,r){if(!e)return Promise.reject(new Error("Expected a stream"));r={maxBuffer:1/0,...r};let{maxBuffer:t}=r,n;return await new Promise((i,s)=>{let o=f=>{f&&n.getBufferedLength()<=Fn.MAX_LENGTH&&(f.bufferedData=n.getBufferedValue()),s(f)};n=In(e,Sn(r),f=>{if(f){o(f);return}i()}),n.on("data",()=>{n.getBufferedLength()>t&&o(new Re)})}),n.getBufferedValue()}X.exports=De;X.exports.default=De;X.exports.buffer=(e,r)=>De(e,{...r,encoding:"buffer"});X.exports.array=(e,r)=>De(e,{...r,array:!0});X.exports.MaxBufferError=Re});var ht=v((kn,lt)=>{"use strict";var S=Jr()("extract-zip"),{createWriteStream:Ln,promises:$}=require("fs"),On=dt(),W=require("path"),{promisify:tr}=require("util"),zn=require("stream"),_n=Xe(),Rn=tr(_n.open),Dn=tr(zn.pipeline),rr=class{constructor(r,t){this.zipPath=r,this.opts=t}async extract(){return S("opening",this.zipPath,"with opts",this.opts),this.zipfile=await Rn(this.zipPath,{lazyEntries:!0}),this.canceled=!1,new Promise((r,t)=>{this.zipfile.on("error",n=>{this.canceled=!0,t(n)}),this.zipfile.readEntry(),this.zipfile.on("close",()=>{this.canceled||(S("zip extraction complete"),r())}),this.zipfile.on("entry",async n=>{if(this.canceled){S("skipping entry",n.fileName,{cancelled:this.canceled});return}if(S("zipfile entry",n.fileName),n.fileName.startsWith("__MACOSX/")){this.zipfile.readEntry();return}let i=W.dirname(W.join(this.opts.dir,n.fileName));try{await $.mkdir(i,{recursive:!0});let s=await $.realpath(i);if(W.relative(this.opts.dir,s).split(W.sep).includes(".."))throw new Error(`Out of bound path "${s}" found while processing file ${n.fileName}`);await this.extractEntry(n),S("finished processing",n.fileName),this.zipfile.readEntry()}catch(s){this.canceled=!0,this.zipfile.close(),t(s)}})})}async extractEntry(r){if(this.canceled){S("skipping entry extraction",r.fileName,{cancelled:this.canceled});return}this.opts.onEntry&&this.opts.onEntry(r,this.zipfile);let t=W.join(this.opts.dir,r.fileName),n=r.externalFileAttributes>>16&65535,i=61440,s=16384,f=(n&i)===40960,u=(n&i)===s;!u&&r.fileName.endsWith("/")&&(u=!0);let l=r.versionMadeBy>>8;u||(u=l===0&&r.externalFileAttributes===16),S("extracting entry",{filename:r.fileName,isDir:u,isSymlink:f});let a=this.getExtractedMode(n,u)&511,c=u?t:W.dirname(t),d={recursive:!0};if(u&&(d.mode=a),S("mkdir",{dir:c,...d}),await $.mkdir(c,d),u)return;S("opening read stream",t);let h=await tr(this.zipfile.openReadStream.bind(this.zipfile))(r);if(f){let E=await On(h);S("creating symlink",E,t),await $.symlink(E,t)}else await Dn(h,Ln(t,{mode:a}))}getExtractedMode(r,t){let n=r;return n===0&&(t?(this.opts.defaultDirMode&&(n=parseInt(this.opts.defaultDirMode,10)),n||(n=493)):(this.opts.defaultFileMode&&(n=parseInt(this.opts.defaultFileMode,10)),n||(n=420))),n}};lt.exports=async function(e,r){if(S("creating target directory",r.dir),!W.isAbsolute(r.dir))throw new Error("Target directory is expected to be absolute");return await $.mkdir(r.dir,{recursive:!0}),r.dir=await $.realpath(r.dir),new rr(e,r).extract()}});var Tn={};Ct(Tn,{extract:()=>Un,yauzl:()=>pt,yazl:()=>mt});module.exports=bt(Tn);var mt=sr(Fr()),pt=sr(Xe()),An=ht(),Un=An;0&&(module.exports={extract,yauzl,yazl});
