const { chromium } = require('playwright');
const fs = require('fs');
const path = require('path');

async function capturePromptDetails() {
  console.log('🚀 Starting prompt detail screenshot capture with auth...');
  
  // Create timestamped directory structure
  const now = new Date();
  const dateStr = now.toISOString().split('T')[0]; // YYYY-MM-DD
  const timeStr = now.toTimeString().split(' ')[0].replace(/:/g, '-'); // HH-MM-SS
  const screenshotDir = path.join(__dirname, 'screenshots', dateStr, timeStr, 'prompt-details');
  
  // Ensure directory exists
  fs.mkdirSync(screenshotDir, { recursive: true });
  console.log(`📁 Created screenshot directory: ${screenshotDir}`);

  const browser = await chromium.launch();
  
  try {
    // Create auth page first
    const authPage = await browser.newPage();
    await authPage.setViewportSize({ width: 1400, height: 1000 });

    // Handle authentication first
    console.log('🔐 Handling authentication...');
    await authPage.goto('http://localhost:5174', { waitUntil: 'networkidle', timeout: 30000 });
    
    // Click Sign In button
    await authPage.click('button:has-text("Sign In")');
    await authPage.waitForTimeout(1000);
    
    // Try second click (generic approach)
    try {
      await authPage.click('button:not(:has-text("Sign In"))');
      await authPage.waitForTimeout(2000);
    } catch (error) {
      console.log('⚠️ Second click failed, continuing...');
    }

    // Wait for authentication
    await authPage.waitForSelector('.prompt-card', { timeout: 10000 });
    console.log('✅ Authentication successful');

    // Create multiple pages for parallel capture
    const [cardClickPage, pKeyPage, combinedPage] = await Promise.all([
      browser.newPage(),
      browser.newPage(),
      browser.newPage()
    ]);

    // Set viewport size for all pages
    await Promise.all([
      cardClickPage.setViewportSize({ width: 1400, height: 1000 }),
      pKeyPage.setViewportSize({ width: 1400, height: 1000 }),
      combinedPage.setViewportSize({ width: 1400, height: 1000 })
    ]);

    // Share cookies from auth page
    const cookies = await authPage.context().cookies();
    await Promise.all([
      cardClickPage.context().addCookies(cookies),
      pKeyPage.context().addCookies(cookies),
      combinedPage.context().addCookies(cookies)
    ]);

    // Navigate all pages to the authenticated app
    await Promise.all([
      cardClickPage.goto('http://localhost:5174', { waitUntil: 'networkidle', timeout: 30000 }),
      pKeyPage.goto('http://localhost:5174', { waitUntil: 'networkidle', timeout: 30000 }),
      combinedPage.goto('http://localhost:5174', { waitUntil: 'networkidle', timeout: 30000 })
    ]);

    console.log('✅ All pages loaded and authenticated successfully');
    
    // Wait for React to render and prompt cards to be available
    await Promise.all([
      cardClickPage.waitForSelector('.prompt-card', { timeout: 10000 }),
      pKeyPage.waitForSelector('.prompt-card', { timeout: 10000 }),
      combinedPage.waitForSelector('.prompt-card', { timeout: 10000 })
    ]);

    // Parallel capture functions
    const cardClickCapture = async () => {
      try {
        // Get all prompt cards
        const cards = await cardClickPage.locator('.prompt-card').all();
        console.log(`🔍 Found ${cards.length} prompt cards for clicking`);
        
        if (cards.length === 0) {
          throw new Error('No prompt cards found');
        }

        // Click first few cards (limit to avoid too many screenshots)
        const cardsToClick = Math.min(3, cards.length);
        
        for (let i = 0; i < cardsToClick; i++) {
          // Click the card
          await cards[i].click();
          await cardClickPage.waitForTimeout(1000); // Wait for modal/detail to open
          
          // Take screenshot of the detail view
          await cardClickPage.screenshot({
            path: path.join(screenshotDir, `card-click-detail-${i + 1}.png`),
            fullPage: true
          });
          
          console.log(`✅ Card click detail ${i + 1} screenshot captured`);
          
          // Close modal/detail if it exists (press Escape or click close)
          await cardClickPage.keyboard.press('Escape');
          await cardClickPage.waitForTimeout(500);
        }
      } catch (error) {
        console.error('❌ Card click capture error:', error.message);
      }
    };

    const pKeyCapture = async () => {
      try {
        // Take initial screenshot
        await pKeyPage.screenshot({
          path: path.join(screenshotDir, 'before-p-key.png'),
          fullPage: false
        });
        
        // Press 'P' key
        await pKeyPage.keyboard.press('KeyP');
        await pKeyPage.waitForTimeout(1000); // Wait for detail view to open
        
        // Take screenshot after pressing P
        await pKeyPage.screenshot({
          path: path.join(screenshotDir, 'after-p-key-detail.png'),
          fullPage: true
        });
        
        console.log('✅ P key detail screenshot captured');
        
        // Press Escape to close if needed
        await pKeyPage.keyboard.press('Escape');
        await pKeyPage.waitForTimeout(500);
      } catch (error) {
        console.error('❌ P key capture error:', error.message);
      }
    };

    const combinedWorkflowCapture = async () => {
      try {
        // Combined workflow: Search -> Select -> P key -> Detail
        const searchSelectors = ['input[placeholder*="Search"]', 'input[placeholder*="prompts"]', '.search-input', 'input[type="text"]'];
        let searchFilled = false;
        
        for (const selector of searchSelectors) {
          try {
            await combinedPage.fill(selector, 'Financial');
            searchFilled = true;
            break;
          } catch (error) {
            continue;
          }
        }
        await combinedPage.waitForTimeout(500);
        
        // Take screenshot of filtered results
        await combinedPage.screenshot({
          path: path.join(screenshotDir, 'filtered-results.png'),
          fullPage: false
        });
        
        // Press P key to show details of filtered results
        await combinedPage.keyboard.press('KeyP');
        await combinedPage.waitForTimeout(1000);
        
        await combinedPage.screenshot({
          path: path.join(screenshotDir, 'filtered-p-key-detail.png'),
          fullPage: true
        });
        
        console.log('✅ Combined workflow screenshot captured');
        
        // Close detail view
        await combinedPage.keyboard.press('Escape');
        await combinedPage.waitForTimeout(500);
        
        // Clear search and try clicking a card
        if (searchFilled) {
          for (const selector of searchSelectors) {
            try {
              await combinedPage.fill(selector, '');
              break;
            } catch (error) {
              continue;
            }
          }
        }
        await combinedPage.waitForTimeout(500);
        
        const firstCard = combinedPage.locator('.prompt-card').first();
        await firstCard.click();
        await combinedPage.waitForTimeout(1000);
        
        await combinedPage.screenshot({
          path: path.join(screenshotDir, 'combined-card-click-detail.png'),
          fullPage: true
        });
        
        console.log('✅ Combined card click detail screenshot captured');
        
      } catch (error) {
        console.error('❌ Combined workflow capture error:', error.message);
      }
    };

    // Run all capture functions in parallel
    await Promise.all([
      cardClickCapture(),
      pKeyCapture(),
      combinedWorkflowCapture()
    ]);

    // Close all pages
    await Promise.all([
      authPage.close(),
      cardClickPage.close(),
      pKeyPage.close(),
      combinedPage.close()
    ]);

    console.log('🎯 Prompt detail capture completed successfully!');

  } catch (error) {
    console.error('❌ Error capturing prompt details:', error.message);
    
    // Try to capture error state
    try {
      const errorPage = await browser.newPage();
      await errorPage.setViewportSize({ width: 1400, height: 1000 });
      await errorPage.goto('http://localhost:5174', { timeout: 10000 });
      await errorPage.screenshot({
        path: path.join(screenshotDir, 'error-state.png'),
        fullPage: true
      });
      console.log('📸 Error state screenshot captured');
      await errorPage.close();
    } catch (screenshotError) {
      console.error('❌ Could not take error screenshot:', screenshotError.message);
    }
  }
  
  await browser.close();
  console.log('🔚 Prompt detail capture complete');
}

capturePromptDetails();