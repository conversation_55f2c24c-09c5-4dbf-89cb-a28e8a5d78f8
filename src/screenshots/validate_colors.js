#!/usr/bin/env node

/**
 * Color Validation Script
 * Validates the extracted Apollo color palette for accuracy and completeness
 */

import { apolloColors, colorHelpers } from './apollo_colors.js';
import fs from 'fs';

function validateColor(color, colorName) {
  const issues = [];

  // Validate hex format
  if (!color.hex || !/^#[0-9A-F]{6}$/i.test(color.hex)) {
    issues.push(`Invalid hex format: ${color.hex}`);
  }

  // Validate RGB values
  if (!color.rgb || color.rgb.length !== 3) {
    issues.push(`Invalid RGB format: ${JSON.stringify(color.rgb)}`);
  } else {
    color.rgb.forEach((value, index) => {
      if (value < 0 || value > 255 || !Number.isInteger(value)) {
        issues.push(`Invalid RGB value at index ${index}: ${value}`);
      }
    });
  }

  // Validate RGB vs Hex consistency
  if (color.hex && color.rgb && color.rgb.length === 3) {
    const calculatedHex = colorHelpers.rgbToHex(color.rgb);
    if (calculatedHex.toLowerCase() !== color.hex.toLowerCase()) {
      issues.push(`RGB/Hex mismatch: ${JSON.stringify(color.rgb)} should be ${calculatedHex}, but hex is ${color.hex}`);
    }
  }

  // Validate CMYK values (should be 0-100)
  if (color.cmyk && color.cmyk.length === 4) {
    color.cmyk.forEach((value, index) => {
      if (value < 0 || value > 100 || !Number.isInteger(value)) {
        issues.push(`Invalid CMYK value at index ${index}: ${value}`);
      }
    });
  }

  // Validate required fields
  if (!color.name) {
    issues.push('Missing name field');
  }

  return issues;
}

function validateTints(tintGroup, groupName) {
  const issues = [];
  
  if (!tintGroup.master) {
    issues.push(`${groupName}: Missing master color`);
  }
  
  if (!tintGroup.variants) {
    issues.push(`${groupName}: Missing variants`);
  } else {
    const expectedTints = [20, 40, 60, 80];
    expectedTints.forEach(tint => {
      if (!tintGroup.variants[tint]) {
        issues.push(`${groupName}: Missing ${tint}% tint`);
      } else {
        const variant = tintGroup.variants[tint];
        const variantIssues = validateColor(variant, `${groupName} ${tint}%`);
        issues.push(...variantIssues.map(issue => `${groupName} ${tint}%: ${issue}`));
      }
    });
  }

  return issues;
}

function runValidation() {
  console.log('🎨 Apollo Color Palette Validation');
  console.log('=====================================\n');

  let totalIssues = 0;

  // Validate primary colors
  console.log('📋 Validating Primary Colors...');
  Object.entries(apolloColors.primary).forEach(([key, color]) => {
    const issues = validateColor(color, key);
    if (issues.length > 0) {
      console.log(`❌ Primary.${key}:`);
      issues.forEach(issue => console.log(`   - ${issue}`));
      totalIssues += issues.length;
    } else {
      console.log(`✅ Primary.${key} - Valid`);
    }
  });

  // Validate secondary colors
  console.log('\n📋 Validating Secondary Colors...');
  Object.entries(apolloColors.secondary).forEach(([key, color]) => {
    const issues = validateColor(color, key);
    if (issues.length > 0) {
      console.log(`❌ Secondary.${key}:`);
      issues.forEach(issue => console.log(`   - ${issue}`));
      totalIssues += issues.length;
    } else {
      console.log(`✅ Secondary.${key} - Valid`);
    }
  });

  // Validate tertiary colors
  console.log('\n📋 Validating Tertiary Colors...');
  Object.entries(apolloColors.tertiary).forEach(([key, color]) => {
    const issues = validateColor(color, key);
    if (issues.length > 0) {
      console.log(`❌ Tertiary.${key}:`);
      issues.forEach(issue => console.log(`   - ${issue}`));
      totalIssues += issues.length;
    } else {
      console.log(`✅ Tertiary.${key} - Valid`);
    }
  });

  // Validate tints
  console.log('\n📋 Validating Tinted Palette...');
  Object.entries(apolloColors.tints).forEach(([key, tintGroup]) => {
    const issues = validateTints(tintGroup, key);
    if (issues.length > 0) {
      console.log(`❌ Tints.${key}:`);
      issues.forEach(issue => console.log(`   - ${issue}`));
      totalIssues += issues.length;
    } else {
      console.log(`✅ Tints.${key} - Valid`);
    }
  });

  // Summary
  console.log('\n=====================================');
  console.log('📊 Validation Summary:');
  console.log(`Total colors validated: ${colorHelpers.getAllColors().length}`);
  console.log(`Total tint groups: ${Object.keys(apolloColors.tints).length}`);
  console.log(`Total issues found: ${totalIssues}`);

  if (totalIssues === 0) {
    console.log('🎉 All colors pass validation!');
  } else {
    console.log('⚠️  Some issues found - please review and correct');
  }

  // Create a summary report
  const report = {
    validationDate: new Date().toISOString(),
    totalColors: colorHelpers.getAllColors().length,
    totalTintGroups: Object.keys(apolloColors.tints).length,
    totalIssues: totalIssues,
    status: totalIssues === 0 ? 'PASS' : 'FAIL'
  };

  fs.writeFileSync('./color_validation_report.json', JSON.stringify(report, null, 2));
  console.log('\n📄 Validation report saved to: color_validation_report.json');

  return totalIssues === 0;
}

// Run validation if this script is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runValidation();
}