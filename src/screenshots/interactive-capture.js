const { chromium } = require('playwright');
const fs = require('fs');
const path = require('path');

async function captureInteractiveScreenshots() {
  console.log('🚀 Starting interactive screenshot capture...');
  
  // Create timestamped directory structure
  const now = new Date();
  const dateStr = now.toISOString().split('T')[0]; // YYYY-MM-DD
  const timeStr = now.toTimeString().split(' ')[0].replace(/:/g, '-'); // HH-MM-SS
  const screenshotDir = path.join(__dirname, 'screenshots', dateStr, timeStr);
  
  // Ensure directory exists
  fs.mkdirSync(screenshotDir, { recursive: true });
  console.log(`📁 Created screenshot directory: ${screenshotDir}`);
  
  const browser = await chromium.launch();
  const page = await browser.newPage();
  await page.setViewportSize({ width: 1400, height: 1000 });
  
  try {
    await page.goto('http://localhost:5173', { waitUntil: 'networkidle' });
    await page.waitForSelector('.prompt-card');
    
    // Search functionality test
    await page.fill('.search-input', 'Financial');
    await page.waitForTimeout(500);
    await page.screenshot({
      path: path.join(screenshotDir, 'search-financial.png')
    });
    console.log('✅ Search "Financial" screenshot captured');
    
    // Clear and test category filtering
    await page.fill('.search-input', '');
    await page.click('button:has-text("Investment")');
    await page.waitForTimeout(500);
    await page.screenshot({
      path: path.join(screenshotDir, 'category-investment.png')
    });
    console.log('✅ Investment category screenshot captured');
    
    // Clear category and test status filtering
    await page.click('button:has-text("Investment")');
    await page.waitForTimeout(500);
    
    // Click on "Approved" status
    await page.locator('label:has-text("Approved")').click();
    await page.waitForTimeout(500);
    await page.screenshot({
      path: path.join(screenshotDir, 'status-approved.png')
    });
    console.log('✅ Approved status screenshot captured');
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
  
  await browser.close();
  console.log('🔚 Interactive capture complete');
}

captureInteractiveScreenshots();