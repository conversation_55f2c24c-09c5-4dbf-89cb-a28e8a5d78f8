const { chromium } = require('playwright');
const fs = require('fs');
const path = require('path');

async function captureScreenshots() {
  console.log('🚀 Starting screenshot capture...');
  
  // Create timestamped directory structure
  const now = new Date();
  const dateStr = now.toISOString().split('T')[0]; // YYYY-MM-DD
  const timeStr = now.toTimeString().split(' ')[0].replace(/:/g, '-'); // HH-MM-SS
  const screenshotDir = path.join(__dirname, 'screenshots', dateStr, timeStr);
  
  // Ensure directory exists
  fs.mkdirSync(screenshotDir, { recursive: true });
  console.log(`📁 Created screenshot directory: ${screenshotDir}`);
  
  const browser = await chromium.launch({
    headless: true
  });
  
  const page = await browser.newPage();
  
  // Set viewport size
  await page.setViewportSize({ width: 1400, height: 1000 });
  
  try {
    console.log('📱 Navigating to http://localhost:5173...');
    
    // Navigate to the React app
    await page.goto('http://localhost:5173', { 
      waitUntil: 'networkidle',
      timeout: 30000 
    });
    
    console.log('✅ Page loaded successfully');
    
    // Wait a bit for React to render
    await page.waitForTimeout(3000);
    
    // Get page title and content for debugging
    const title = await page.title();
    console.log('📄 Page title:', title);
    
    // Check if React root div has content
    const hasContent = await page.locator('#root').count();
    console.log('🔍 React root elements found:', hasContent);
    
    // Take full page screenshot regardless of content
    await page.screenshot({
      path: path.join(screenshotDir, 'full-page.png'),
      fullPage: true
    });
    
    console.log('✅ Full page screenshot captured');
    
    // Take viewport screenshot
    await page.screenshot({
      path: path.join(screenshotDir, 'viewport.png'),
      fullPage: false
    });
    
    console.log('✅ Viewport screenshot captured');
    
    // Try to capture console logs for debugging
    const consoleMessages = [];
    page.on('console', msg => {
      consoleMessages.push(`${msg.type()}: ${msg.text()}`);
    });
    
    await page.waitForTimeout(2000);
    
    if (consoleMessages.length > 0) {
      console.log('🔍 Browser console messages:');
      consoleMessages.forEach(msg => console.log('  ', msg));
    }
    
  } catch (error) {
    console.error('❌ Error capturing screenshots:', error.message);
    
    // Take screenshot anyway to see what's happening
    try {
      await page.screenshot({
        path: path.join(screenshotDir, 'error-state.png'),
        fullPage: true
      });
      console.log('📸 Error state screenshot captured');
    } catch (screenshotError) {
      console.error('❌ Could not take error screenshot:', screenshotError.message);
    }
  }
  
  await browser.close();
  console.log('🔚 Browser closed');
}

captureScreenshots();