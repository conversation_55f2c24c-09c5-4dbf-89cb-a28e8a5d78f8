/**
 * Apollo Color Palette - Extracted from Apollo Color Palette Guide.pdf (Slides 4 & 5)
 * 
 * This object contains the complete Apollo brand color palette including:
 * - Primary colors (neutrals)
 * - Secondary colors (brand colors)
 * - Tertiary colors (accent colors)
 * - Tinted palette (variations)
 * 
 * Each color includes hex, RGB, CMYK, and Pantone values where available.
 * Usage guidelines are included for proper brand compliance.
 */

export const apolloColors = {
  // Primary neutral palette
  primary: {
    grey: {
      name: 'Grey',
      hex: '#55585A',
      rgb: [85, 88, 90],
      cmyk: [62, 50, 47, 40],
      pantone: '425 C / 4280 U'
    },
    clay: {
      name: '<PERSON>',
      hex: '#93867A',
      rgb: [147, 134, 122],
      cmyk: [39, 43, 54, 21],
      pantone: '403 C / 2955 U'
    },
    ecru: {
      name: 'Ecru',
      hex: '#CAC2BA',
      rgb: [202, 194, 186],
      cmyk: [23, 21, 25, 3],
      pantone: 'Warm Gray C / 400 U'
    },
    white: {
      name: '<PERSON>',
      hex: '#FFFFFF',
      rgb: [255, 255, 255],
      cmyk: [0, 0, 0, 0],
      pantone: 'C0 M0 Y0 K0'
    }
  },

  // Secondary brand colors
  secondary: {
    apolloGreen: {
      name: 'Apollo Green',
      hex: '#007B63',
      rgb: [0, 123, 99],
      cmyk: [87, 30, 69, 14],
      pantone: '3295 C / 3536 U',
      restrictions: 'Do not use Apollo Green to represent companies other than Apollo.'
    },
    saxeBlue: {
      name: 'Saxe Blue',
      hex: '#395878',
      rgb: [57, 88, 120],
      cmyk: [83, 59, 32, 18],
      pantone: '4377 C / 2955 U'
    },
    skyBlue: {
      name: 'Sky Blue',
      hex: '#73A2D3',
      rgb: [115, 162, 211],
      cmyk: [55, 27, 12, 0],
      pantone: '7453 C / 659 U'
    },
    orange: {
      name: 'Orange',
      hex: '#EC9A5F',
      rgb: [236, 154, 95],
      cmyk: [4, 47, 66, 0],
      pantone: '157 C / 157 U'
    }
  },

  // Tertiary accent colors
  tertiary: {
    ink: {
      name: 'Ink',
      hex: '#000000',
      rgb: [0, 0, 0],
      cmyk: [91, 79, 62, 97],
      pantone: 'Black C / Black U',
      usage: 'For text, icons and graphic elements'
    },
    matchstick: {
      name: 'Matchstick',
      hex: '#CA504B',
      rgb: [202, 80, 75],
      cmyk: [16, 79, 66, 5],
      pantone: '7416 C / 7417 U',
      usage: 'For cultural initiatives and to show decline/negative results in data'
    },
    burntSienna: {
      name: 'Burnt Sienna',
      hex: '#EB6758',
      rgb: [235, 103, 88],
      cmyk: [1, 71, 61, 0],
      pantone: '2033 C / 1797 U',
      usage: 'For cultural initiatives'
    }
  },

  // Tinted palette variations (20%, 40%, 60%, 80% tints)
  tints: {
    grey: {
      master: '#55585A',
      variants: {
        80: { name: 'Grey 80%', hex: '#6B6C70', rgb: [107, 108, 112] },
        60: { name: 'Grey 60%', hex: '#838488', rgb: [131, 132, 136] },
        40: { name: 'Grey 40%', hex: '#9B9C9E', rgb: [155, 156, 158] },
        20: { name: 'Grey 20%', hex: '#B4B4B6', rgb: [180, 180, 182] }
      }
    },
    clay: {
      master: '#93867A',
      variants: {
        80: { name: 'Clay 80%', hex: '#A89588', rgb: [168, 149, 136] },
        60: { name: 'Clay 60%', hex: '#B6A39C', rgb: [182, 163, 156] },
        40: { name: 'Clay 40%', hex: '#C4B1AB', rgb: [196, 177, 171] },
        20: { name: 'Clay 20%', hex: '#D2BFBA', rgb: [210, 191, 186] }
      }
    },
    apolloGreen: {
      master: '#007B63',
      variants: {
        80: { name: 'Apollo Green 80%', hex: '#339880', rgb: [51, 152, 128] },
        60: { name: 'Apollo Green 60%', hex: '#66B59D', rgb: [102, 181, 157] },
        40: { name: 'Apollo Green 40%', hex: '#99D2BA', rgb: [153, 210, 186] },
        20: { name: 'Apollo Green 20%', hex: '#CCEFD7', rgb: [204, 239, 215] }
      }
    },
    saxeBlue: {
      master: '#395878',
      variants: {
        80: { name: 'Saxe Blue 80%', hex: '#737391', rgb: [115, 115, 145] },
        60: { name: 'Saxe Blue 60%', hex: '#8A8EAC', rgb: [138, 142, 172] },
        40: { name: 'Saxe Blue 40%', hex: '#A1A9C7', rgb: [161, 169, 199] },
        20: { name: 'Saxe Blue 20%', hex: '#B8C4E2', rgb: [184, 196, 226] }
      }
    },
    skyBlue: {
      master: '#73A2D3',
      variants: {
        80: { name: 'Sky Blue 80%', hex: '#8FCBDB', rgb: [143, 203, 219] },
        60: { name: 'Sky Blue 60%', hex: '#ABD5E3', rgb: [171, 213, 227] },
        40: { name: 'Sky Blue 40%', hex: '#C7DFEB', rgb: [199, 223, 235] },
        20: { name: 'Sky Blue 20%', hex: '#E3E9F3', rgb: [227, 233, 243] }
      }
    },
    orange: {
      master: '#EC9A5F',
      variants: {
        80: { name: 'Orange 80%', hex: '#EFAB7F', rgb: [239, 171, 127] },
        60: { name: 'Orange 60%', hex: '#F2BC9F', rgb: [242, 188, 159] },
        40: { name: 'Orange 40%', hex: '#F5CDBF', rgb: [245, 205, 191] },
        20: { name: 'Orange 20%', hex: '#F8DEDF', rgb: [248, 222, 223] }
      }
    },
    burntSienna: {
      master: '#EB6758',
      variants: {
        80: { name: 'Burnt Sienna 80%', hex: '#EF8579', rgb: [239, 133, 121] },
        60: { name: 'Burnt Sienna 60%', hex: '#F3A39A', rgb: [243, 163, 154] },
        40: { name: 'Burnt Sienna 40%', hex: '#F7C1BB', rgb: [247, 193, 187] },
        20: { name: 'Burnt Sienna 20%', hex: '#FBDFDC', rgb: [251, 223, 220] }
      }
    },
    matchstick: {
      master: '#CA504B',
      variants: {
        80: { name: 'Matchstick 80%', hex: '#D47166', rgb: [212, 113, 102] },
        60: { name: 'Matchstick 60%', hex: '#DE9281', rgb: [222, 146, 129] },
        40: { name: 'Matchstick 40%', hex: '#E8B39C', rgb: [232, 179, 156] },
        20: { name: 'Matchstick 20%', hex: '#F2D4B7', rgb: [242, 212, 183] }
      }
    }
  }
};

// Helper functions for working with the color palette
export const colorHelpers = {
  /**
   * Get all colors as a flat array
   */
  getAllColors() {
    const colors = [];
    Object.values(apolloColors.primary).forEach(color => colors.push(color));
    Object.values(apolloColors.secondary).forEach(color => colors.push(color));
    Object.values(apolloColors.tertiary).forEach(color => colors.push(color));
    return colors;
  },

  /**
   * Get color by name (case insensitive)
   */
  getColorByName(name) {
    const lowerName = name.toLowerCase();
    const allColors = this.getAllColors();
    return allColors.find(color => color.name.toLowerCase() === lowerName);
  },

  /**
   * Get color by hex value
   */
  getColorByHex(hex) {
    const normalizedHex = hex.toLowerCase();
    const allColors = this.getAllColors();
    return allColors.find(color => color.hex.toLowerCase() === normalizedHex);
  },

  /**
   * Get all tints for a specific color
   */
  getTints(colorName) {
    const lowerName = colorName.toLowerCase().replace(/\s+/g, '');
    const tintKey = Object.keys(apolloColors.tints).find(key => 
      key.toLowerCase() === lowerName
    );
    return tintKey ? apolloColors.tints[tintKey] : null;
  },

  /**
   * Convert RGB array to hex
   */
  rgbToHex(rgb) {
    return '#' + rgb.map(x => {
      const hex = x.toString(16);
      return hex.length === 1 ? '0' + hex : hex;
    }).join('').toUpperCase();
  },

  /**
   * Convert hex to RGB array
   */
  hexToRgb(hex) {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    return result ? [
      parseInt(result[1], 16),
      parseInt(result[2], 16),
      parseInt(result[3], 16)
    ] : null;
  }
};

export default apolloColors;