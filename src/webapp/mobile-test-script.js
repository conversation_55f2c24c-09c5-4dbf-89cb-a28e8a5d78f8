// Mobile Test Script - Run in browser console
// Open browser dev tools and paste this script to test mobile functionality

console.log('🔍 Mobile Responsiveness Test Script');
console.log('=====================================');

function testMobileFeatures() {
  console.log('\n1. Testing Mobile Hamburger Menu...');
  
  // Check if hamburger button exists
  const hamburgerButton = document.querySelector('.mobile-menu-button');
  if (hamburgerButton) {
    console.log('✅ Hamburger button found');
    
    // Test visibility based on viewport
    const isVisible = window.getComputedStyle(hamburgerButton).display !== 'none';
    if (window.innerWidth <= 768) {
      console.log(isVisible ? '✅ Hamburger button visible on mobile' : '❌ Hamburger button not visible on mobile');
    } else {
      console.log(!isVisible ? '✅ Hamburger button hidden on desktop' : '❌ Hamburger button should be hidden on desktop');
    }
    
    // Test touch target size
    const rect = hamburgerButton.getBoundingClientRect();
    const minSize = 44;
    const actualSize = Math.min(rect.width, rect.height);
    console.log(actualSize >= minSize ? `✅ Touch target adequate (${actualSize}px)` : `❌ Touch target too small (${actualSize}px, should be >= ${minSize}px)`);
    
    // Test click functionality
    const sidebarContainer = document.querySelector('.sidebar-container');
    const initialOpen = sidebarContainer.classList.contains('mobile-open');
    
    hamburgerButton.click();
    setTimeout(() => {
      const newOpen = sidebarContainer.classList.contains('mobile-open');
      console.log(newOpen !== initialOpen ? '✅ Hamburger click toggles sidebar' : '❌ Hamburger click does not toggle sidebar');
      
      // Test overlay
      const overlay = document.querySelector('.sidebar-overlay');
      if (overlay) {
        const overlayActive = overlay.classList.contains('active');
        console.log(overlayActive === newOpen ? '✅ Overlay state matches sidebar state' : '❌ Overlay state mismatch');
      }
      
      // Test ESC key
      const escEvent = new KeyboardEvent('keydown', { key: 'Escape' });
      document.dispatchEvent(escEvent);
      
      setTimeout(() => {
        const afterEsc = sidebarContainer.classList.contains('mobile-open');
        console.log(!afterEsc ? '✅ ESC key closes sidebar' : '❌ ESC key does not close sidebar');
      }, 100);
      
    }, 100);
    
  } else {
    console.log('❌ Hamburger button not found');
  }
  
  console.log('\n2. Testing Responsive Layout...');
  
  // Test sidebar responsiveness
  const sidebarContainer = document.querySelector('.sidebar-container');
  const iconSidebar = document.querySelector('.icon-sidebar');
  const mainSidebar = document.querySelector('.main-sidebar');
  
  if (sidebarContainer && iconSidebar && mainSidebar) {
    const iconStyles = window.getComputedStyle(iconSidebar);
    const mainStyles = window.getComputedStyle(mainSidebar);
    
    console.log(`📱 Current viewport: ${window.innerWidth}px x ${window.innerHeight}px`);
    console.log(`📐 Icon sidebar width: ${iconStyles.width}`);
    console.log(`📐 Main sidebar width: ${mainStyles.width}`);
    
    if (window.innerWidth <= 768) {
      const sidebarStyles = window.getComputedStyle(sidebarContainer);
      console.log(sidebarStyles.position === 'fixed' ? '✅ Mobile: Sidebar is fixed positioned' : '❌ Mobile: Sidebar should be fixed positioned');
    } else if (window.innerWidth <= 1024) {
      console.log('📱 Tablet viewport detected');
    } else {
      console.log('🖥️ Desktop viewport detected');
    }
  }
  
  console.log('\n3. Testing Prompt Cards Responsiveness...');
  
  const promptCards = document.querySelectorAll('.prompt-card');
  if (promptCards.length > 0) {
    console.log(`✅ Found ${promptCards.length} prompt cards`);
    
    promptCards.forEach((card, index) => {
      const cardStyles = window.getComputedStyle(card);
      const cardWidth = card.getBoundingClientRect().width;
      
      if (index === 0) { // Test first card as representative
        console.log(`📐 Card width: ${cardWidth}px`);
        console.log(`📐 Card padding: ${cardStyles.padding}`);
        console.log(`📐 Card gap: ${cardStyles.gap}`);
        
        if (window.innerWidth <= 768) {
          console.log(cardStyles.flexDirection === 'column' ? '✅ Mobile: Cards use column layout' : '❌ Mobile: Cards should use column layout');
        }
      }
    });
  } else {
    console.log('❌ No prompt cards found');
  }
  
  console.log('\n4. Testing Touch Interactions...');
  
  // Test all clickable elements for adequate touch targets
  const clickableElements = document.querySelectorAll('button, .vote-count, .category-item, .status-item, .nav-item, .icon-button');
  let touchTargetIssues = 0;
  
  clickableElements.forEach((element, index) => {
    const rect = element.getBoundingClientRect();
    const minSize = 44;
    const actualSize = Math.min(rect.width, rect.height);
    
    if (actualSize < minSize && rect.width > 0 && rect.height > 0) {
      touchTargetIssues++;
      if (touchTargetIssues <= 3) { // Only log first few to avoid spam
        console.log(`⚠️ Small touch target: ${element.className} (${actualSize}px)`);
      }
    }
  });
  
  console.log(touchTargetIssues === 0 ? '✅ All touch targets adequate' : `⚠️ ${touchTargetIssues} touch targets may be too small`);
  
  console.log('\n5. Testing Scrolling and Navigation...');
  
  const mainContent = document.querySelector('.main-content') || document.querySelector('[data-testid="main-content"]');
  if (mainContent) {
    const contentStyles = window.getComputedStyle(mainContent);
    console.log(`📐 Main content overflow: ${contentStyles.overflow}`);
    console.log(contentStyles.overflowY === 'auto' || contentStyles.overflowY === 'scroll' ? '✅ Content scrollable' : '⚠️ Content may not be scrollable');
  }
  
  console.log('\n📋 Test Summary Complete');
  console.log('========================');
  console.log('💡 Resize your browser window and run this script again to test different breakpoints');
  console.log('💡 Use browser dev tools device emulation to test touch interactions');
}

// Auto-run the test
testMobileFeatures();

// Export for manual running
window.testMobileFeatures = testMobileFeatures;