# Apollo Prompt Library - Database Schema Documentation

## Overview

This document describes the database schema for the Apollo Prompt Library, including the recent addition of the `inputOutputExamples` field to support prompt examples with input/output pairs.

## Schema Version

Current schema version: `v1.1.0` (includes inputOutputExamples field)

## Database Technology

- **Database**: SQLite3 (better-sqlite3)
- **Location**: `apollo_prompts.db` (in project root)
- **Migrations**: Tracked in `migrations` table

## Tables

### 1. users
Stores user accounts and authentication information.

```sql
CREATE TABLE users (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  username TEXT UNIQUE NOT NULL,
  email TEXT UNIQUE NOT NULL,
  full_name TEXT NOT NULL,
  role TEXT NOT NULL DEFAULT 'user',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

**Indexes:**
- Primary key on `id`
- Unique constraint on `username`
- Unique constraint on `email`

### 2. categories
Stores prompt categories for organization.

```sql
CREATE TABLE categories (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  name TEXT UNIQUE NOT NULL,
  description TEXT,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

**Indexes:**
- Primary key on `id`
- Unique constraint on `name`

### 3. prompts ⭐ (Updated)
Stores the main prompt data including the new inputOutputExamples field.

```sql
CREATE TABLE prompts (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  title TEXT NOT NULL,
  description TEXT NOT NULL,
  content TEXT NOT NULL,
  category_id INTEGER NOT NULL,
  author_id INTEGER NOT NULL,
  status TEXT NOT NULL DEFAULT 'In Development',
  difficulty TEXT NOT NULL DEFAULT 'Intermediate',
  votes INTEGER DEFAULT 0,
  comments INTEGER DEFAULT 0,
  input_output_examples TEXT DEFAULT '[]',  -- NEW FIELD
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (category_id) REFERENCES categories(id),
  FOREIGN KEY (author_id) REFERENCES users(id)
);
```

**Indexes:**
- Primary key on `id`
- Index on `category_id` (foreign key performance)
- Index on `author_id` (foreign key performance)
- Index on `status` (filtering)

**Field Details:**
- `input_output_examples`: JSON text field storing array of objects with `input` and `output` string fields
- Default value: `'[]'` (empty array)
- Validated by database triggers

### 4. tags
Stores available tags for prompts.

```sql
CREATE TABLE tags (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  name TEXT UNIQUE NOT NULL,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

### 5. prompt_tags
Junction table for many-to-many relationship between prompts and tags.

```sql
CREATE TABLE prompt_tags (
  prompt_id INTEGER,
  tag_id INTEGER,
  PRIMARY KEY (prompt_id, tag_id),
  FOREIGN KEY (prompt_id) REFERENCES prompts(id) ON DELETE CASCADE,
  FOREIGN KEY (tag_id) REFERENCES tags(id) ON DELETE CASCADE
);
```

### 6. votes
Stores user votes on prompts.

```sql
CREATE TABLE votes (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  user_id INTEGER NOT NULL,
  prompt_id INTEGER NOT NULL,
  vote_type TEXT NOT NULL CHECK (vote_type IN ('up', 'down')),
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  UNIQUE(user_id, prompt_id),
  FOREIGN KEY (user_id) REFERENCES users(id),
  FOREIGN KEY (prompt_id) REFERENCES prompts(id) ON DELETE CASCADE
);
```

### 7. user_sessions
Stores user authentication sessions.

```sql
CREATE TABLE user_sessions (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  user_id INTEGER NOT NULL,
  session_token TEXT UNIQUE NOT NULL,
  expires_at DATETIME NOT NULL,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);
```

### 8. migrations
Tracks applied database migrations.

```sql
CREATE TABLE migrations (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  migration_name TEXT UNIQUE NOT NULL,
  applied_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

## Input/Output Examples Feature

### Schema Structure

The `input_output_examples` field stores JSON data with the following structure:

```json
[
  {
    "input": "string - example input text",
    "output": "string - expected output text"
  },
  {
    "input": "string - another example input",
    "output": "string - another expected output"
  }
]
```

### Validation Rules

1. **Field Type**: Must be valid JSON text
2. **Structure**: Must be an array of objects
3. **Object Fields**: Each object must have exactly `input` and `output` fields
4. **Field Types**: Both `input` and `output` must be strings
5. **Content**: Neither `input` nor `output` can be empty strings (after trimming)

### Database Constraints

- **JSON Validation**: Database triggers validate JSON format on INSERT/UPDATE
- **Default Value**: Empty array `'[]'` for new prompts
- **Storage**: Stored as TEXT in SQLite (JSON is not a native SQLite type)

## Migration Strategy

### Current Migration: `001_add_input_output_examples`

**Objective**: Add the `input_output_examples` field to existing prompts table.

**Actions**:
1. Add new column with default value `'[]'`
2. Create validation triggers for JSON format
3. Maintain backward compatibility

**Migration Path**:
```bash
# Run migration
node src/api/migrationRunner.js up

# Check status
node src/api/migrationRunner.js status

# Rollback if needed
node src/api/migrationRunner.js down
```

### Backward Compatibility

- **Existing Data**: All existing prompts get empty array `'[]'` as default
- **API Compatibility**: Existing API endpoints continue to work
- **Frontend**: UI gracefully handles prompts without examples

### Future Migrations

The migration system supports:
- **Sequential Migrations**: Applied in order
- **Rollback Support**: Can undo last migration
- **Status Tracking**: Shows applied/pending migrations
- **Error Handling**: Atomic operations with rollback on failure

## API Integration

### Database Helpers

New helper functions for input/output examples:

```javascript
// Database operations
dbHelpers.updatePromptExamples.run(jsonString, promptId)
dbHelpers.getPromptExamples.get(promptId)

// Utility functions
inputOutputHelpers.validateExamples(examples)
inputOutputHelpers.serializeExamples(examples)
inputOutputHelpers.parseExamples(jsonString)
inputOutputHelpers.addExample(promptId, input, output)
inputOutputHelpers.removeExample(promptId, index)
inputOutputHelpers.updateExample(promptId, index, input, output)
```

### Updated API Endpoints

**Create Prompt**:
```javascript
POST /api/prompts
{
  "title": "Example Prompt",
  "description": "Description here",
  "content": "Prompt content",
  "categoryId": 1,
  "difficulty": "Intermediate",
  "status": "Approved",
  "inputOutputExamples": [
    {
      "input": "Example input",
      "output": "Example output"
    }
  ]
}
```

**Update Prompt**:
```javascript
PUT /api/prompts/:id
{
  "inputOutputExamples": [
    {
      "input": "Updated input",
      "output": "Updated output"
    }
  ]
}
```

## Performance Considerations

### Indexing
- No additional indexes needed for JSON field (SQLite handles JSON queries efficiently)
- Existing indexes support filtering and joins

### Storage
- JSON stored as compressed TEXT
- Typical example size: 100-500 characters per example
- Estimated storage per prompt: 1-5KB for examples

### Query Performance
- JSON parsing happens in application layer
- Database triggers provide validation without query overhead
- Bulk operations use prepared statements

## Security Considerations

### Input Validation
- **Server-side**: All examples validated before database storage
- **Database-level**: Triggers prevent invalid JSON storage
- **Client-side**: UI validation for user experience

### Data Sanitization
- **HTML Escaping**: Examples escaped before display
- **XSS Prevention**: No executable content in examples
- **Length Limits**: Reasonable limits on example text length

## Development Workflow

### Adding New Migrations
1. Create migration file in `src/api/migrations/`
2. Implement `up()` and `down()` functions
3. Add to migration runner
4. Test migration and rollback
5. Update schema documentation

### Testing Strategy
1. **Unit Tests**: Test individual helper functions
2. **Integration Tests**: Test full CRUD operations
3. **Migration Tests**: Test migration up/down scenarios
4. **Performance Tests**: Validate query performance

## Troubleshooting

### Common Issues

**Migration Fails**:
- Check database permissions
- Verify SQL syntax
- Check for foreign key constraints

**JSON Validation Errors**:
- Validate JSON structure manually
- Check for empty input/output fields
- Verify array format

**Performance Issues**:
- Monitor query execution time
- Check for missing indexes
- Profile JSON parsing operations

### Recovery Procedures

**Corrupt Migration State**:
```bash
# Check migration status
node src/api/migrationRunner.js status

# Manual migration table cleanup if needed
sqlite3 apollo_prompts.db "DELETE FROM migrations WHERE migration_name = '001_add_input_output_examples';"
```

**Data Recovery**:
- Regular backups recommended
- Migration system provides rollback capability
- JSON validation prevents data corruption

## Future Enhancements

### Planned Features
1. **Example Templates**: Pre-defined example templates
2. **Batch Operations**: Bulk example import/export
3. **Example Validation**: Advanced validation rules
4. **Example Search**: Full-text search within examples

### Schema Evolution
- **Version Tracking**: Migration system supports schema versioning
- **Backward Compatibility**: Maintain API compatibility
- **Performance Optimization**: Monitor and optimize as data grows

---

*Last Updated: 2025-06-30*
*Schema Version: v1.1.0*