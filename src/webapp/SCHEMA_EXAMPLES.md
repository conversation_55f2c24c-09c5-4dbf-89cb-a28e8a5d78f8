# Apollo Prompt Library - Schema Examples

## Updated JSON Schema Structure

This document shows examples of how the new `inputOutputExamples` field integrates with the existing prompt structure.

## Complete Prompt Example with Input/Output Examples

```json
{
  "id": 1,
  "title": "Investment Thesis Development",
  "description": "Create comprehensive investment thesis for target company including market analysis, competitive positioning, and value creation opportunities.",
  "category": "Investment Analysis",
  "tags": [
    "Thesis",
    "Market Analysis", 
    "Competitive Analysis"
  ],
  "status": "Approved",
  "votes": 8,
  "author": "PE Team",
  "timeAgo": "2 hours ago",
  "statusCount": 45,
  "difficulty": "Advanced",
  "comments": 15,
  "userVote": null,
  "content": "Develop a comprehensive investment thesis for [Target Company] that will serve as the foundation for Apollo's investment decision and portfolio management strategy...",
  "inputOutputExamples": [
    {
      "input": "Target Company: TechFlow Solutions - A B2B SaaS company providing workflow automation tools for mid-market enterprises. Current revenue: $50M ARR, growing at 40% YoY. Market cap: $200M.",
      "output": "**Investment Thesis for TechFlow Solutions**\n\n**Executive Summary:**\nTechFlow represents a compelling growth investment in the rapidly expanding workflow automation market. With $50M ARR and 40% growth, the company is well-positioned to capture significant market share in the $15B+ TAM.\n\n**Key Investment Highlights:**\n- Strong product-market fit evidenced by 40% ARR growth and <5% churn\n- Expanding TAM driven by digital transformation initiatives\n- Experienced management team with prior successful exits\n- Clear path to $200M+ ARR within 5 years\n\n**Value Creation Plan:**\n1. Sales acceleration through channel partnerships (+$30M ARR)\n2. Product expansion into adjacent markets (+$25M ARR)\n3. International expansion targeting UK/EU (+$20M ARR)\n4. Operational efficiency improvements (+500bps EBITDA margin)\n\n**Expected Returns:** 3.5x - 4.5x over 4-5 year holding period"
    },
    {
      "input": "Target Company: MedDevice Corp - Medical device manufacturer specializing in surgical instruments. Revenue: $25M, EBITDA: $8M. Family-owned business looking for growth capital and succession planning.",
      "output": "**Investment Thesis for MedDevice Corp**\n\n**Executive Summary:**\nMedDevice Corp presents an attractive platform investment in the defensive healthcare sector with opportunities for both organic growth and add-on acquisitions.\n\n**Key Investment Highlights:**\n- Recession-resistant healthcare market with aging demographics tailwind\n- Strong cash generation (32% EBITDA margins) and recurring revenue base\n- Fragmented market ideal for consolidation strategy\n- Experienced management team ready to accelerate growth\n\n**Value Creation Plan:**\n1. Platform strategy: 3-4 add-on acquisitions targeting $75M+ revenue\n2. Commercial excellence: enhanced sales force and digital marketing\n3. Operational improvements: lean manufacturing and automation\n4. Product development: R&D investment in next-gen surgical tools\n\n**Market Opportunity:**\n- $2.5B TAM in surgical instruments with 5-7% annual growth\n- Consolidation opportunity in fragmented supplier landscape\n\n**Expected Returns:** 3.0x - 4.0x over 5-6 year holding period"
    }
  ]
}
```

## Backward Compatibility Example

### Existing Prompt (without examples)
```json
{
  "id": 2,
  "title": "Management Team Assessment", 
  "description": "Evaluate management team capabilities, track record, and alignment with investment objectives.",
  "category": "Due Diligence",
  "tags": ["Management", "Leadership", "Assessment"],
  "status": "Approved",
  "votes": 7,
  "author": "PE Team",
  "difficulty": "Intermediate",
  "content": "Conduct a comprehensive assessment of [Target Company]'s management team...",
  "inputOutputExamples": []
}
```

## Input/Output Examples Schema Validation

### Valid Example Structure
```json
{
  "inputOutputExamples": [
    {
      "input": "Company: RetailTech Inc. - E-commerce platform for small retailers. Revenue: $10M, growing 25% annually. Management team: CEO (former Amazon), CTO (ex-Shopify), CFO (Big 4 background).",
      "output": "**Management Assessment Summary:**\n\n**CEO - Sarah Johnson (Former Amazon VP):**\n- Strengths: 15 years e-commerce experience, proven scale-up track record\n- Development needs: PE partnership experience, board management\n- Retention: High - equity upside and growth opportunity alignment\n\n**CTO - Mike Chen (Ex-Shopify Lead):**\n- Strengths: Technical excellence, product vision, team building\n- Development needs: Enterprise sales support, infrastructure scaling\n- Retention: High - technical challenges and equity participation\n\n**CFO - Lisa Rodriguez (Big 4 Background):**\n- Strengths: Financial controls, reporting systems, compliance\n- Development needs: Growth company experience, investor relations\n- Retention: Medium - may need senior finance support\n\n**Overall Assessment:** Strong technical and operational leadership with proven track records. Recommend retaining all key executives with enhanced compensation packages and board support for scaling challenges."
    }
  ]
}
```

### Invalid Examples (will be rejected)

#### Missing required fields:
```json
{
  "inputOutputExamples": [
    {
      "input": "Some input text"
      // Missing "output" field
    }
  ]
}
```

#### Wrong data types:
```json
{
  "inputOutputExamples": [
    {
      "input": 123, // Should be string
      "output": "Some output"
    }
  ]
}
```

#### Empty content:
```json
{
  "inputOutputExamples": [
    {
      "input": "", // Empty string not allowed
      "output": "Some output"
    }
  ]
}
```

## API Request Examples

### Creating a new prompt with examples:
```javascript
POST /api/prompts
Content-Type: application/json

{
  "title": "Customer Concentration Risk Analysis",
  "description": "Analyze customer concentration risks, retention rates, and diversification opportunities.",
  "content": "Perform a detailed analysis of [Target Company]'s customer concentration risk...",
  "categoryId": 4,
  "difficulty": "Intermediate",
  "status": "Approved",
  "inputOutputExamples": [
    {
      "input": "Target Company: DataCloud Services - B2B cloud infrastructure provider. Top 5 customers represent 60% of revenue. Largest customer (TechGiant Corp) = 25% of total revenue. Contract terms: 3-year agreements with auto-renewal clauses.",
      "output": "**Customer Concentration Risk Assessment**\n\n**Risk Level: HIGH**\n\n**Key Findings:**\n- Critical dependency: 60% revenue from 5 customers\n- Single point of failure: TechGiant Corp (25% revenue exposure)\n- Moderate protection: 3-year contracts provide some stability\n\n**Risk Mitigation Strategy:**\n1. Immediate: Strengthen relationships with top 5 customers\n2. Short-term: Diversify customer base (target <40% from top 5)\n3. Long-term: Develop new market segments and service lines\n\n**Monitoring Framework:**\n- Monthly: Customer health scores and renewal probability\n- Quarterly: Concentration metrics and pipeline analysis\n- Annual: Contract renewal and pricing negotiations"
    }
  ]
}
```

### Updating examples for existing prompt:
```javascript
PUT /api/prompts/8/examples
Content-Type: application/json

{
  "inputOutputExamples": [
    {
      "input": "Updated example input",
      "output": "Updated example output"
    },
    {
      "input": "Additional example input", 
      "output": "Additional example output"
    }
  ]
}
```

### Adding single example:
```javascript
POST /api/prompts/8/examples
Content-Type: application/json

{
  "input": "New example input text",
  "output": "New example output text"
}
```

## Database Storage Format

### Internal Storage (SQLite TEXT field)
```sql
-- Stored in prompts.input_output_examples column as JSON text
'[{"input":"Example input","output":"Example output"}]'
```

### Query Examples
```sql
-- Get all prompts with examples
SELECT id, title, input_output_examples 
FROM prompts 
WHERE input_output_examples != '[]';

-- Count prompts with examples
SELECT COUNT(*) 
FROM prompts 
WHERE json_array_length(input_output_examples) > 0;

-- Search within examples (SQLite JSON functions)
SELECT id, title 
FROM prompts 
WHERE json_extract(input_output_examples, '$[*].input') LIKE '%customer%';
```

## Frontend Integration Examples

### React Component Usage
```jsx
function PromptExamples({ examples = [] }) {
  return (
    <div className="prompt-examples">
      <h3>Input/Output Examples</h3>
      {examples.length === 0 ? (
        <p>No examples available for this prompt.</p>
      ) : (
        examples.map((example, index) => (
          <div key={index} className="example-pair">
            <div className="example-input">
              <h4>Input:</h4>
              <pre>{example.input}</pre>
            </div>
            <div className="example-output">
              <h4>Output:</h4>
              <pre>{example.output}</pre>
            </div>
          </div>
        ))
      )}
    </div>
  );
}
```

### Example Editing Interface
```jsx
function ExampleEditor({ promptId, examples, onUpdate }) {
  const [editingIndex, setEditingIndex] = useState(-1);
  const [inputText, setInputText] = useState('');
  const [outputText, setOutputText] = useState('');

  const addExample = async () => {
    const newExample = { input: inputText, output: outputText };
    await api.addPromptExample(promptId, newExample);
    onUpdate();
    setInputText('');
    setOutputText('');
  };

  return (
    <div className="example-editor">
      {/* Example list and editing interface */}
    </div>
  );
}
```

## Migration Data Examples

### Before Migration (existing prompts.json structure)
```json
{
  "id": 1,
  "title": "Investment Thesis Development",
  "description": "Create comprehensive investment thesis...",
  "category": "Investment Analysis",
  "tags": ["Thesis", "Market Analysis"],
  "status": "Approved",
  "votes": 8,
  "author": "PE Team",
  "content": "Develop a comprehensive investment thesis..."
}
```

### After Migration (database structure)
```sql
-- Prompt record in database after migration
INSERT INTO prompts (
  id, title, description, content, category_id, author_id, 
  status, difficulty, votes, input_output_examples
) VALUES (
  1, 'Investment Thesis Development', 'Create comprehensive investment thesis...', 
  'Develop a comprehensive investment thesis...', 1, 2, 
  'Approved', 'Advanced', 8, '[]'
);
```

---

*This document provides comprehensive examples of the new input/output examples feature integration with the Apollo Prompt Library schema.*