# Database Toggle System

## Overview

The Apollo Prompt Library supports two data backend modes that can be toggled via configuration:

1. **JSON Backend** (`USE_DATABASE_INSTEAD_OF_JSON_FILE: false`)
   - Uses in-memory JSON data
   - Data resets on page refresh
   - Ideal for development and testing

2. **Browser Database Backend** (`USE_DATABASE_INSTEAD_OF_JSON_FILE: true`)
   - Uses localStorage for data persistence
   - Data persists across browser sessions
   - Supports full CRUD operations with vote tracking

## Configuration

Toggle between backends in `src/config.js`:

```javascript
const config = {
  USE_DATABASE_INSTEAD_OF_JSON_FILE: true, // true = localStorage, false = JSON
}
```

## Data Management

### Initial Data Load
- **JSON Mode**: Loads from `src/data/prompts.json` on each page load
- **Database Mode**: Initializes localStorage from `src/data/prompts.json` on first visit

### Data Persistence
- **JSON Mode**: No persistence, data resets on refresh
- **Database Mode**: All changes (votes, new prompts) persist in localStorage

### Clearing Data
To reset database mode data:
1. Open browser developer tools (F12)
2. Go to Application > Storage > localStorage
3. Clear Apollo-related keys or clear all localStorage
4. Refresh the page to reinitialize

## Technical Implementation

### Files
- `src/api/client.js` - JSON backend implementation
- `src/api/browserDatabase.js` - localStorage database implementation  
- `src/api/browserEndpoints.js` - API endpoints for browser database
- `src/App.jsx` - Dynamic API selection based on config

### API Compatibility
Both backends provide identical API interfaces:
- `promptsAPI.getAll()`, `getById()`, `create()`, `update()`, `delete()`, `vote()`
- `categoriesAPI.getAll()`
- `authAPI.login()`, `logout()`, `getCurrentUser()`

### Vote System
- **JSON Mode**: Vote counts are static from JSON file
- **Database Mode**: Vote counts are dynamic and persist user interactions

## Testing

The system includes regression tests that verify:
- Backend switching functionality
- Data persistence in localStorage
- Vote functionality with both backends
- API interface compatibility

Run tests with: `npm test src/__tests__/DatabaseToggle.test.jsx`

## Notes

- Database mode loads all 40 prompts from JSON file in production
- Test environment uses reduced dataset (3 prompts) for faster testing
- localStorage keys use `apollo_` prefix to avoid conflicts
- Both modes support the same UI features and functionality