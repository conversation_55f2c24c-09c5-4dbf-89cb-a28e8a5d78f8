/**
 * Regression Tests: Database Input/Output Examples Functionality
 * 
 * Tests the new input_output_examples field functionality:
 * - JSON validation and structure
 * - CRUD operations with examples
 * - Data integrity and constraints
 * - Helper function validation
 * 
 * These tests ensure that input/output examples feature works correctly
 * and prevents regressions during refactoring.
 */

import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import Database from 'better-sqlite3';
import { validateInputOutputExamples } from '../../src/api/migrations/001_add_input_output_examples.js';

describe('Input/Output Examples Database Functionality', () => {
  let db;

  beforeEach(() => {
    // Create in-memory test database
    db = new Database(':memory:');
    
    // Set up minimal test schema
    db.exec(`
      CREATE TABLE prompts (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        title TEXT NOT NULL,
        description TEXT NOT NULL,
        content TEXT NOT NULL,
        input_output_examples TEXT DEFAULT '[]',
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
      );
    `);

    // Add validation triggers
    db.exec(`
      CREATE TRIGGER validate_input_output_examples
      BEFORE INSERT ON prompts
      FOR EACH ROW
      WHEN NEW.input_output_examples IS NOT NULL AND NEW.input_output_examples != '[]'
      BEGIN
        SELECT CASE
          WHEN json_valid(NEW.input_output_examples) = 0 THEN
            RAISE(ABORT, 'input_output_examples must be valid JSON')
        END;
      END;
    `);
    
    db.exec(`
      CREATE TRIGGER validate_input_output_examples_update
      BEFORE UPDATE ON prompts
      FOR EACH ROW
      WHEN NEW.input_output_examples IS NOT NULL AND NEW.input_output_examples != '[]'
      BEGIN
        SELECT CASE
          WHEN json_valid(NEW.input_output_examples) = 0 THEN
            RAISE(ABORT, 'input_output_examples must be valid JSON')
        END;
      END;
    `);
  });

  afterEach(() => {
    db.close();
  });

  describe('JSON Validation', () => {
    it('should accept valid empty array as default', () => {
      const stmt = db.prepare(`
        INSERT INTO prompts (title, description, content) 
        VALUES (?, ?, ?)
      `);
      
      expect(() => {
        stmt.run('Test Title', 'Test Description', 'Test Content');
      }).not.toThrow();
      
      const result = db.prepare('SELECT input_output_examples FROM prompts WHERE id = 1').get();
      expect(result.input_output_examples).toBe('[]');
      
      console.log('✓ if default empty array works then not broken');
    });

    it('should accept valid JSON input/output examples', () => {
      const validExamples = JSON.stringify([
        { input: 'Example input 1', output: 'Example output 1' },
        { input: 'Example input 2', output: 'Example output 2' }
      ]);

      const stmt = db.prepare(`
        INSERT INTO prompts (title, description, content, input_output_examples) 
        VALUES (?, ?, ?, ?)
      `);
      
      expect(() => {
        stmt.run('Test Title', 'Test Description', 'Test Content', validExamples);
      }).not.toThrow();
      
      const result = db.prepare('SELECT input_output_examples FROM prompts WHERE id = 1').get();
      const parsed = JSON.parse(result.input_output_examples);
      expect(Array.isArray(parsed)).toBe(true);
      expect(parsed).toHaveLength(2);
      expect(parsed[0]).toHaveProperty('input');
      expect(parsed[0]).toHaveProperty('output');
      
      console.log('✓ if valid JSON examples are stored correctly then not broken');
    });

    it('should reject invalid JSON format', () => {
      const stmt = db.prepare(`
        INSERT INTO prompts (title, description, content, input_output_examples) 
        VALUES (?, ?, ?, ?)
      `);
      
      expect(() => {
        stmt.run('Test Title', 'Test Description', 'Test Content', 'invalid json');
      }).toThrow('input_output_examples must be valid JSON');
      
      console.log('✓ if invalid JSON is rejected then not broken');
    });

    it('should handle updates with valid JSON', () => {
      // Insert initial record
      const insertStmt = db.prepare(`
        INSERT INTO prompts (title, description, content) 
        VALUES (?, ?, ?)
      `);
      insertStmt.run('Test Title', 'Test Description', 'Test Content');

      // Update with valid examples
      const validExamples = JSON.stringify([
        { input: 'Updated input', output: 'Updated output' }
      ]);

      const updateStmt = db.prepare(`
        UPDATE prompts SET input_output_examples = ? WHERE id = 1
      `);
      
      expect(() => {
        updateStmt.run(validExamples);
      }).not.toThrow();
      
      const result = db.prepare('SELECT input_output_examples FROM prompts WHERE id = 1').get();
      const parsed = JSON.parse(result.input_output_examples);
      expect(parsed[0].input).toBe('Updated input');
      expect(parsed[0].output).toBe('Updated output');
      
      console.log('✓ if JSON updates work correctly then not broken');
    });

    it('should reject invalid JSON on updates', () => {
      // Insert initial record
      const insertStmt = db.prepare(`
        INSERT INTO prompts (title, description, content) 
        VALUES (?, ?, ?)
      `);
      insertStmt.run('Test Title', 'Test Description', 'Test Content');

      // Try to update with invalid JSON
      const updateStmt = db.prepare(`
        UPDATE prompts SET input_output_examples = ? WHERE id = 1
      `);
      
      expect(() => {
        updateStmt.run('{ invalid json }');
      }).toThrow('input_output_examples must be valid JSON');
      
      console.log('✓ if invalid JSON updates are rejected then not broken');
    });
  });

  describe('Data Structure Validation', () => {
    it('should validate correct input/output structure with helper function', () => {
      const validExamples = [
        { input: 'Test input 1', output: 'Test output 1' },
        { input: 'Test input 2', output: 'Test output 2' }
      ];

      expect(() => {
        validateInputOutputExamples(validExamples);
      }).not.toThrow();
      
      console.log('✓ if valid structure passes validation then not broken');
    });

    it('should reject non-array structures', () => {
      const invalidExamples = { input: 'Test', output: 'Test' };

      expect(() => {
        validateInputOutputExamples(invalidExamples);
      }).toThrow('inputOutputExamples must be an array');
      
      console.log('✓ if non-array structure is rejected then not broken');
    });

    it('should reject examples without input field', () => {
      const invalidExamples = [
        { output: 'Test output' }
      ];

      expect(() => {
        validateInputOutputExamples(invalidExamples);
      }).toThrow('Each example must have an input field of type string');
      
      console.log('✓ if missing input field is rejected then not broken');
    });

    it('should reject examples without output field', () => {
      const invalidExamples = [
        { input: 'Test input' }
      ];

      expect(() => {
        validateInputOutputExamples(invalidExamples);
      }).toThrow('Each example must have an output field of type string');
      
      console.log('✓ if missing output field is rejected then not broken');
    });

    it('should reject non-string input/output values', () => {
      const invalidExamples = [
        { input: 123, output: 'Test output' }
      ];

      expect(() => {
        validateInputOutputExamples(invalidExamples);
      }).toThrow('Each example must have an input field of type string');
      
      console.log('✓ if non-string input/output is rejected then not broken');
    });

    it('should handle empty arrays correctly', () => {
      const emptyExamples = [];

      expect(() => {
        validateInputOutputExamples(emptyExamples);
      }).not.toThrow();
      
      console.log('✓ if empty array validation passes then not broken');
    });
  });

  describe('CRUD Operations', () => {
    it('should create prompts with input/output examples', () => {
      const examples = JSON.stringify([
        { input: 'Create a financial model', output: 'Here is a comprehensive financial model...' }
      ]);

      const stmt = db.prepare(`
        INSERT INTO prompts (title, description, content, input_output_examples) 
        VALUES (?, ?, ?, ?)
      `);
      
      const result = stmt.run(
        'Financial Modeling Prompt',
        'Helps create financial models',
        'Act as a financial analyst...',
        examples
      );

      expect(result.lastInsertRowid).toBe(1);
      
      const prompt = db.prepare('SELECT * FROM prompts WHERE id = 1').get();
      expect(prompt.title).toBe('Financial Modeling Prompt');
      expect(JSON.parse(prompt.input_output_examples)).toHaveLength(1);
      
      console.log('✓ if prompt creation with examples works then not broken');
    });

    it('should read prompts with parsed examples', () => {
      // Insert test data
      const examples = JSON.stringify([
        { input: 'Test input', output: 'Test output' }
      ]);
      
      const stmt = db.prepare(`
        INSERT INTO prompts (title, description, content, input_output_examples) 
        VALUES (?, ?, ?, ?)
      `);
      stmt.run('Test Title', 'Test Description', 'Test Content', examples);

      // Read and parse
      const prompt = db.prepare('SELECT * FROM prompts WHERE id = 1').get();
      const parsedExamples = JSON.parse(prompt.input_output_examples);
      
      expect(parsedExamples).toHaveLength(1);
      expect(parsedExamples[0].input).toBe('Test input');
      expect(parsedExamples[0].output).toBe('Test output');
      
      console.log('✓ if prompt reading with examples works then not broken');
    });

    it('should update existing examples', () => {
      // Insert initial data
      const initialExamples = JSON.stringify([
        { input: 'Original input', output: 'Original output' }
      ]);
      
      const insertStmt = db.prepare(`
        INSERT INTO prompts (title, description, content, input_output_examples) 
        VALUES (?, ?, ?, ?)
      `);
      insertStmt.run('Test Title', 'Test Description', 'Test Content', initialExamples);

      // Update examples
      const updatedExamples = JSON.stringify([
        { input: 'Updated input 1', output: 'Updated output 1' },
        { input: 'Updated input 2', output: 'Updated output 2' }
      ]);
      
      const updateStmt = db.prepare(`
        UPDATE prompts SET input_output_examples = ? WHERE id = 1
      `);
      updateStmt.run(updatedExamples);

      // Verify update
      const prompt = db.prepare('SELECT input_output_examples FROM prompts WHERE id = 1').get();
      const parsedExamples = JSON.parse(prompt.input_output_examples);
      
      expect(parsedExamples).toHaveLength(2);
      expect(parsedExamples[0].input).toBe('Updated input 1');
      expect(parsedExamples[1].input).toBe('Updated input 2');
      
      console.log('✓ if example updates work correctly then not broken');
    });

    it('should delete prompts with examples without constraint violations', () => {
      // Insert test data
      const examples = JSON.stringify([
        { input: 'Test input', output: 'Test output' }
      ]);
      
      const stmt = db.prepare(`
        INSERT INTO prompts (title, description, content, input_output_examples) 
        VALUES (?, ?, ?, ?)
      `);
      stmt.run('Test Title', 'Test Description', 'Test Content', examples);

      // Delete
      const deleteStmt = db.prepare('DELETE FROM prompts WHERE id = 1');
      expect(() => {
        deleteStmt.run();
      }).not.toThrow();

      // Verify deletion
      const count = db.prepare('SELECT COUNT(*) as count FROM prompts').get();
      expect(count.count).toBe(0);
      
      console.log('✓ if prompt deletion with examples works then not broken');
    });
  });

  describe('Data Integrity', () => {
    it('should maintain referential integrity with complex examples', () => {
      const complexExamples = JSON.stringify([
        {
          input: 'Analyze quarterly earnings for AAPL',
          output: 'Based on Q3 2024 earnings: Revenue increased 6% YoY to $94.9B...'
        },
        {
          input: 'Create DCF model for Tesla',
          output: 'Tesla DCF Model Assumptions:\n- Revenue CAGR: 15%\n- Terminal Growth: 3%...'
        },
        {
          input: 'Risk assessment for portfolio',
          output: 'Portfolio Risk Analysis:\n- VaR (95%): -2.3%\n- Sharpe Ratio: 1.47...'
        }
      ]);

      const stmt = db.prepare(`
        INSERT INTO prompts (title, description, content, input_output_examples) 
        VALUES (?, ?, ?, ?)
      `);
      
      const result = stmt.run(
        'Investment Analysis Toolkit',
        'Comprehensive investment analysis prompts',
        'You are an expert investment analyst...',
        complexExamples
      );

      expect(result.lastInsertRowid).toBe(1);
      
      // Verify complex data integrity
      const prompt = db.prepare('SELECT * FROM prompts WHERE id = 1').get();
      const parsedExamples = JSON.parse(prompt.input_output_examples);
      
      expect(parsedExamples).toHaveLength(3);
      expect(parsedExamples[0].input).toContain('AAPL');
      expect(parsedExamples[1].output).toContain('DCF Model');
      expect(parsedExamples[2].input).toContain('Risk assessment');
      
      console.log('✓ if complex examples maintain data integrity then not broken');
    });

    it('should handle special characters and formatting in examples', () => {
      const specialExamples = JSON.stringify([
        {
          input: 'Format this data:\n"Company": "Apple Inc.",\n"Symbol": "AAPL"',
          output: '{\n  "company": "Apple Inc.",\n  "symbol": "AAPL",\n  "formatted": true\n}'
        },
        {
          input: 'Calculate returns: [100, 105, 98, 110]',
          output: 'Returns: [5%, -6.67%, 12.24%]\nAnnualized: 8.52%'
        }
      ]);

      const stmt = db.prepare(`
        INSERT INTO prompts (title, description, content, input_output_examples) 
        VALUES (?, ?, ?, ?)
      `);
      
      expect(() => {
        stmt.run('Data Formatting', 'Format financial data', 'Format data as...', specialExamples);
      }).not.toThrow();
      
      const prompt = db.prepare('SELECT input_output_examples FROM prompts WHERE id = 1').get();
      const parsedExamples = JSON.parse(prompt.input_output_examples);
      
      expect(parsedExamples[0].input).toContain('"Company"');
      expect(parsedExamples[0].output).toContain('{\n');
      expect(parsedExamples[1].input).toContain('[100, 105, 98, 110]');
      
      console.log('✓ if special characters in examples work then not broken');
    });
  });
});