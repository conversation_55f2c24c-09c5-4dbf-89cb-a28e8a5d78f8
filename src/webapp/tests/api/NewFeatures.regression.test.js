/**
 * Regression Tests: API Endpoint Changes for New Features
 * 
 * Tests the updated API endpoints that support new features:
 * - Input/output examples in prompt CRUD operations
 * - Enhanced prompt data structure
 * - Authentication with new user fields
 * - Category management with enhanced data
 * - Voting system integrity
 * - Error handling for new fields
 * 
 * These tests ensure that API endpoints work correctly with new features
 * and prevent regressions during refactoring.
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import Database from 'better-sqlite3';

describe('API Endpoint Changes for New Features', () => {
  let db;
  let mockDbHelpers;
  let promptsAPI;
  let authAPI;
  let categoriesAPI;

  beforeEach(() => {
    // Create in-memory test database
    db = new Database(':memory:');
    
    // Set up test schema with new fields
    db.exec(`
      CREATE TABLE users (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        username TEXT UNIQUE NOT NULL,
        email TEXT UNIQUE NOT NULL,
        full_name TEXT NOT NULL,
        role TEXT NOT NULL DEFAULT 'user',
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      );
    `);

    db.exec(`
      CREATE TABLE categories (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT UNIQUE NOT NULL,
        description TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
      );
    `);

    db.exec(`
      CREATE TABLE prompts (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        title TEXT NOT NULL,
        description TEXT NOT NULL,
        content TEXT NOT NULL,
        category_id INTEGER NOT NULL,
        author_id INTEGER NOT NULL,
        status TEXT NOT NULL DEFAULT 'In Development',
        difficulty TEXT NOT NULL DEFAULT 'Intermediate',
        votes INTEGER DEFAULT 0,
        comments INTEGER DEFAULT 0,
        input_output_examples TEXT DEFAULT '[]',
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (category_id) REFERENCES categories(id),
        FOREIGN KEY (author_id) REFERENCES users(id)
      );
    `);

    db.exec(`
      CREATE TABLE user_sessions (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER NOT NULL,
        session_token TEXT UNIQUE NOT NULL,
        expires_at DATETIME NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
      );
    `);

    db.exec(`
      CREATE TABLE votes (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER NOT NULL,
        prompt_id INTEGER NOT NULL,
        vote_type TEXT NOT NULL CHECK (vote_type IN ('up', 'down')),
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        UNIQUE(user_id, prompt_id),
        FOREIGN KEY (user_id) REFERENCES users(id),
        FOREIGN KEY (prompt_id) REFERENCES prompts(id) ON DELETE CASCADE
      );
    `);

    // Insert test data
    db.exec(`
      INSERT INTO users (id, username, email, full_name, role) VALUES 
      (1, 'testuser', '<EMAIL>', 'Test User', 'user'),
      (2, 'admin', '<EMAIL>', 'Admin User', 'admin');
    `);

    db.exec(`
      INSERT INTO categories (id, name, description) VALUES 
      (1, 'Investment Analysis', 'Investment and portfolio analysis prompts'),
      (2, 'Risk Management', 'Risk assessment and management prompts');
    `);

    // Mock database helpers
    mockDbHelpers = {
      getPromptsWithFilters: {
        all: () => db.prepare(`
          SELECT p.*, c.name as category_name, u.full_name as author_name, '' as tags
          FROM prompts p
          JOIN categories c ON p.category_id = c.id
          JOIN users u ON p.author_id = u.id
        `).all()
      },
      getPromptById: {
        get: (id) => db.prepare(`
          SELECT p.*, c.name as category_name, u.full_name as author_name, '' as tags
          FROM prompts p
          JOIN categories c ON p.category_id = c.id
          JOIN users u ON p.author_id = u.id
          WHERE p.id = ?
        `).get(id)
      },
      createPrompt: {
        run: (title, description, content, categoryId, authorId, status, difficulty, examples = '[]') => 
          db.prepare(`
            INSERT INTO prompts (title, description, content, category_id, author_id, status, difficulty, input_output_examples)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
          `).run(title, description, content, categoryId, authorId, status, difficulty, examples)
      },
      updatePrompt: {
        run: (title, description, content, categoryId, status, difficulty, examples, id) =>
          db.prepare(`
            UPDATE prompts 
            SET title = ?, description = ?, content = ?, category_id = ?, status = ?, difficulty = ?, input_output_examples = ?, updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
          `).run(title, description, content, categoryId, status, difficulty, examples, id)
      },
      getCategoryByName: {
        get: (name) => db.prepare('SELECT * FROM categories WHERE name = ?').get(name)
      },
      getUserByUsername: {
        get: (username) => db.prepare('SELECT * FROM users WHERE username = ?').get(username)
      },
      createSession: {
        run: (userId, token) => {
          const expiresAt = new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString();
          return db.prepare('INSERT INTO user_sessions (user_id, session_token, expires_at) VALUES (?, ?, ?)').run(userId, token, expiresAt);
        }
      }
    };

    // Mock API implementations
    promptsAPI = {
      async create(promptData, userId) {
        const category = mockDbHelpers.getCategoryByName.get(promptData.category);
        if (!category) {
          throw new Error('Invalid category');
        }

        const examples = promptData.inputOutputExamples ? JSON.stringify(promptData.inputOutputExamples) : '[]';
        
        const result = mockDbHelpers.createPrompt.run(
          promptData.title,
          promptData.description,
          promptData.content,
          category.id,
          userId,
          promptData.status || 'In Development',
          promptData.difficulty || 'Intermediate',
          examples
        );

        return await this.getById(result.lastInsertRowid);
      },

      async getById(id) {
        const prompt = mockDbHelpers.getPromptById.get(id);
        if (!prompt) {
          throw new Error('Prompt not found');
        }

        return {
          id: prompt.id,
          title: prompt.title,
          description: prompt.description,
          content: prompt.content,
          category: prompt.category_name,
          status: prompt.status,
          votes: prompt.votes,
          author: prompt.author_name,
          difficulty: prompt.difficulty,
          comments: prompt.comments,
          inputOutputExamples: JSON.parse(prompt.input_output_examples || '[]'),
          timeAgo: '1 hour ago'
        };
      },

      async update(id, promptData, userId) {
        const existingPrompt = mockDbHelpers.getPromptById.get(id);
        if (!existingPrompt) {
          throw new Error('Prompt not found');
        }

        const category = mockDbHelpers.getCategoryByName.get(promptData.category);
        if (!category) {
          throw new Error('Invalid category');
        }

        const examples = promptData.inputOutputExamples ? JSON.stringify(promptData.inputOutputExamples) : existingPrompt.input_output_examples;

        mockDbHelpers.updatePrompt.run(
          promptData.title,
          promptData.description,
          promptData.content,
          category.id,
          promptData.status,
          promptData.difficulty,
          examples,
          id
        );

        return await this.getById(id);
      }
    };

    authAPI = {
      async login(username, password) {
        const user = mockDbHelpers.getUserByUsername.get(username);
        if (!user) {
          throw new Error('Invalid username or password');
        }

        const sessionToken = 'test-session-' + Math.random().toString(36).substring(7);
        mockDbHelpers.createSession.run(user.id, sessionToken);

        return {
          user: {
            id: user.id,
            username: user.username,
            email: user.email,
            fullName: user.full_name,
            role: user.role
          },
          sessionToken
        };
      }
    };
  });

  afterEach(() => {
    db.close();
  });

  describe('Prompts API with Input/Output Examples', () => {
    it('should create prompt with input/output examples', async () => {
      const promptData = {
        title: 'Financial Analysis Framework',
        description: 'Comprehensive financial analysis prompt',
        content: 'Act as a financial analyst and...',
        category: 'Investment Analysis',
        status: 'Approved',
        difficulty: 'Advanced',
        inputOutputExamples: [
          {
            input: 'Analyze Apple Inc. Q3 2024 earnings',
            output: 'Apple Q3 2024 Analysis:\n- Revenue: $94.9B (+6% YoY)\n- EPS: $1.64 vs $1.60 expected...'
          },
          {
            input: 'What are the key financial ratios for MSFT?',
            output: 'Microsoft Key Ratios:\n- P/E: 28.5x\n- ROE: 42.3%\n- Debt/Equity: 0.35...'
          }
        ]
      };

      const result = await promptsAPI.create(promptData, 1);

      expect(result.id).toBeTruthy();
      expect(result.title).toBe('Financial Analysis Framework');
      expect(result.inputOutputExamples).toHaveLength(2);
      expect(result.inputOutputExamples[0].input).toContain('Apple Inc.');
      expect(result.inputOutputExamples[1].output).toContain('Microsoft Key Ratios');

      console.log('✓ if prompt creation with examples works then not broken');
    });

    it('should create prompt without input/output examples', async () => {
      const promptData = {
        title: 'Basic Risk Assessment',
        description: 'Simple risk assessment prompt',
        content: 'Evaluate the following risks...',
        category: 'Risk Management',
        status: 'In Development',
        difficulty: 'Beginner'
        // No inputOutputExamples provided
      };

      const result = await promptsAPI.create(promptData, 1);

      expect(result.id).toBeTruthy();
      expect(result.title).toBe('Basic Risk Assessment');
      expect(result.inputOutputExamples).toEqual([]);

      console.log('✓ if prompt creation without examples works then not broken');
    });

    it('should update prompt with new input/output examples', async () => {
      // First create a prompt
      const initialData = {
        title: 'Portfolio Analysis',
        description: 'Analyze investment portfolios',
        content: 'Review this portfolio...',
        category: 'Investment Analysis',
        inputOutputExamples: [
          { input: 'Original input', output: 'Original output' }
        ]
      };

      const created = await promptsAPI.create(initialData, 1);
      
      // Now update with new examples
      const updatedData = {
        title: 'Enhanced Portfolio Analysis',
        description: 'Comprehensive portfolio analysis',
        content: 'Thoroughly analyze this portfolio...',
        category: 'Investment Analysis',
        status: 'Approved',
        difficulty: 'Advanced',
        inputOutputExamples: [
          {
            input: 'Analyze this balanced portfolio: 60% stocks, 40% bonds',
            output: 'Portfolio Analysis:\n- Expected Return: 7.2%\n- Risk (Volatility): 11.5%\n- Sharpe Ratio: 0.63...'
          },
          {
            input: 'What is the optimal rebalancing frequency?',
            output: 'Optimal Rebalancing:\n- Quarterly for active portfolios\n- Annual for passive strategies...'
          }
        ]
      };

      const updated = await promptsAPI.update(created.id, updatedData, 1);

      expect(updated.title).toBe('Enhanced Portfolio Analysis');
      expect(updated.inputOutputExamples).toHaveLength(2);
      expect(updated.inputOutputExamples[0].input).toContain('balanced portfolio');
      expect(updated.inputOutputExamples[1].input).toContain('rebalancing frequency');

      console.log('✓ if prompt update with examples works then not broken');
    });

    it('should handle invalid input/output examples format', async () => {
      const promptData = {
        title: 'Test Prompt',
        description: 'Test description',
        content: 'Test content',
        category: 'Investment Analysis',
        inputOutputExamples: [
          { input: 'Valid input', output: 'Valid output' },
          { input: '', output: 'Invalid - empty input' }, // Invalid
          { wrongField: 'Wrong structure' } // Invalid structure
        ]
      };

      // Validate examples before API call
      const validateExamples = (examples) => {
        if (!Array.isArray(examples)) return false;
        
        return examples.every(example => 
          typeof example === 'object' &&
          typeof example.input === 'string' &&
          typeof example.output === 'string' &&
          example.input.trim().length > 0 &&
          example.output.trim().length > 0
        );
      };

      const isValid = validateExamples(promptData.inputOutputExamples);
      expect(isValid).toBe(false);

      // Clean examples before creation
      const validExamples = promptData.inputOutputExamples.filter(example =>
        example.input && example.output && 
        typeof example.input === 'string' && 
        typeof example.output === 'string'
      );

      const cleanedData = { ...promptData, inputOutputExamples: validExamples };
      const result = await promptsAPI.create(cleanedData, 1);

      expect(result.inputOutputExamples).toHaveLength(1);
      expect(result.inputOutputExamples[0].input).toBe('Valid input');

      console.log('✓ if invalid examples validation works then not broken');
    });

    it('should retrieve prompt with parsed input/output examples', async () => {
      // Create prompt with examples
      const promptData = {
        title: 'DCF Model Builder',
        description: 'Build discounted cash flow models',
        content: 'Create a DCF model for...',
        category: 'Investment Analysis',
        inputOutputExamples: [
          {
            input: 'Build DCF for Tesla with 15% revenue growth',
            output: 'Tesla DCF Model:\n- Revenue CAGR: 15%\n- Terminal Growth: 3%\n- WACC: 8.5%\n- Fair Value: $245/share'
          }
        ]
      };

      const created = await promptsAPI.create(promptData, 1);
      const retrieved = await promptsAPI.getById(created.id);

      expect(retrieved.inputOutputExamples).toBeInstanceOf(Array);
      expect(retrieved.inputOutputExamples).toHaveLength(1);
      expect(retrieved.inputOutputExamples[0]).toHaveProperty('input');
      expect(retrieved.inputOutputExamples[0]).toHaveProperty('output');
      expect(retrieved.inputOutputExamples[0].input).toContain('Tesla');

      console.log('✓ if prompt retrieval with examples works then not broken');
    });
  });

  describe('Authentication API with Enhanced User Data', () => {
    it('should login with complete user profile', async () => {
      const result = await authAPI.login('testuser', 'password');

      expect(result.user).toHaveProperty('id');
      expect(result.user).toHaveProperty('username');
      expect(result.user).toHaveProperty('email');
      expect(result.user).toHaveProperty('fullName');
      expect(result.user).toHaveProperty('role');
      expect(result.sessionToken).toBeTruthy();

      expect(result.user.username).toBe('testuser');
      expect(result.user.email).toBe('<EMAIL>');
      expect(result.user.fullName).toBe('Test User');
      expect(result.user.role).toBe('user');

      console.log('✓ if login with enhanced user data works then not broken');
    });

    it('should handle admin user login', async () => {
      const result = await authAPI.login('admin', 'password');

      expect(result.user.role).toBe('admin');
      expect(result.user.fullName).toBe('Admin User');
      expect(result.user.email).toBe('<EMAIL>');

      console.log('✓ if admin user login works then not broken');
    });

    it('should reject invalid credentials', async () => {
      await expect(authAPI.login('nonexistent', 'password'))
        .rejects.toThrow('Invalid username or password');

      console.log('✓ if invalid credential rejection works then not broken');
    });
  });

  describe('Enhanced Prompt Data Structure', () => {
    it('should handle all prompt fields correctly', async () => {
      const comprehensivePromptData = {
        title: 'Comprehensive Investment Analysis',
        description: 'Complete investment analysis framework with multiple methodologies',
        content: 'Act as a senior investment analyst with 15+ years of experience...',
        category: 'Investment Analysis',
        status: 'Approved',
        difficulty: 'Expert',
        inputOutputExamples: [
          {
            input: 'Perform comprehensive analysis of Amazon (AMZN)',
            output: 'Amazon Investment Analysis:\n\n1. Fundamental Analysis:\n- Revenue Growth: 12% CAGR\n- Operating Margin: 5.3%\n...'
          }
        ]
      };

      const result = await promptsAPI.create(comprehensivePromptData, 2);

      // Verify all fields are preserved
      expect(result.title).toBe(comprehensivePromptData.title);
      expect(result.description).toBe(comprehensivePromptData.description);
      expect(result.content).toBe(comprehensivePromptData.content);
      expect(result.category).toBe(comprehensivePromptData.category);
      expect(result.status).toBe(comprehensivePromptData.status);
      expect(result.difficulty).toBe(comprehensivePromptData.difficulty);
      expect(result.inputOutputExamples).toHaveLength(1);
      expect(result.author).toBe('Admin User');
      expect(result.votes).toBe(0);
      expect(result.comments).toBe(0);

      console.log('✓ if comprehensive prompt data handling works then not broken');
    });

    it('should handle default values appropriately', async () => {
      const minimalPromptData = {
        title: 'Basic Prompt',
        description: 'Simple description',
        content: 'Basic content',
        category: 'Risk Management'
        // No status, difficulty, or examples provided
      };

      const result = await promptsAPI.create(minimalPromptData, 1);

      expect(result.status).toBe('In Development'); // Default
      expect(result.difficulty).toBe('Intermediate'); // Default
      expect(result.inputOutputExamples).toEqual([]); // Default empty array
      expect(result.votes).toBe(0);
      expect(result.comments).toBe(0);

      console.log('✓ if default value handling works then not broken');
    });
  });

  describe('Error Handling for New Features', () => {
    it('should handle invalid category gracefully', async () => {
      const promptData = {
        title: 'Test Prompt',
        description: 'Test description',
        content: 'Test content',
        category: 'Non-existent Category'
      };

      await expect(promptsAPI.create(promptData, 1))
        .rejects.toThrow('Invalid category');

      console.log('✓ if invalid category error handling works then not broken');
    });

    it('should handle non-existent prompt updates', async () => {
      const updateData = {
        title: 'Updated Title',
        description: 'Updated description',
        content: 'Updated content',
        category: 'Investment Analysis'
      };

      await expect(promptsAPI.update(999, updateData, 1))
        .rejects.toThrow('Prompt not found');

      console.log('✓ if non-existent prompt update handling works then not broken');
    });

    it('should validate JSON structure for examples', () => {
      const testCases = [
        { examples: [], valid: true },
        { examples: [{ input: 'test', output: 'test' }], valid: true },
        { examples: [{ input: '', output: 'test' }], valid: false },
        { examples: [{ input: 'test' }], valid: false },
        { examples: [{ wrongField: 'test' }], valid: false },
        { examples: 'not an array', valid: false }
      ];

      const validateExamples = (examples) => {
        if (!Array.isArray(examples)) return false;
        return examples.every(ex => 
          ex && typeof ex === 'object' &&
          typeof ex.input === 'string' && ex.input.trim().length > 0 &&
          typeof ex.output === 'string' && ex.output.trim().length > 0
        );
      };

      testCases.forEach(({ examples, valid }) => {
        expect(validateExamples(examples)).toBe(valid);
      });

      console.log('✓ if examples JSON validation works then not broken');
    });
  });

  describe('API Response Format Consistency', () => {
    it('should return consistent response format for prompts', async () => {
      const promptData = {
        title: 'Consistent Format Test',
        description: 'Testing response format',
        content: 'Test content',
        category: 'Investment Analysis',
        inputOutputExamples: [
          { input: 'Test input', output: 'Test output' }
        ]
      };

      const result = await promptsAPI.create(promptData, 1);

      // Verify response has all expected fields
      const expectedFields = [
        'id', 'title', 'description', 'content', 'category',
        'status', 'votes', 'author', 'difficulty', 'comments',
        'inputOutputExamples', 'timeAgo'
      ];

      expectedFields.forEach(field => {
        expect(result).toHaveProperty(field);
      });

      // Verify field types
      expect(typeof result.id).toBe('number');
      expect(typeof result.title).toBe('string');
      expect(Array.isArray(result.inputOutputExamples)).toBe(true);
      expect(typeof result.votes).toBe('number');

      console.log('✓ if API response format consistency works then not broken');
    });

    it('should handle empty and null values consistently', async () => {
      const testData = {
        title: 'Empty Values Test',
        description: '',
        content: 'Test content',
        category: 'Investment Analysis'
      };

      const result = await promptsAPI.create(testData, 1);

      expect(result.description).toBe('');
      expect(result.inputOutputExamples).toEqual([]);
      expect(result.votes).toBe(0);
      expect(result.comments).toBe(0);

      console.log('✓ if empty/null value handling works then not broken');
    });
  });
});