#!/usr/bin/env node

/**
 * Regression Test Runner for New Database and AI Features
 * 
 * This script runs all regression tests for recently added features:
 * - Database input/output examples functionality
 * - Migration system integrity
 * - AI integration basic functionality (without requiring real OpenAI API)
 * - API endpoint changes for new features
 * - Data persistence of new fields
 * 
 * Usage:
 *   node tests/regression-runner.js [--suite=<suite-name>] [--verbose]
 * 
 * Suites:
 *   - database: Run database-related tests
 *   - ai: Run AI integration tests
 *   - api: Run API endpoint tests
 *   - all: Run all regression tests (default)
 */

import { spawn } from 'child_process';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Test suite definitions
const testSuites = {
  database: [
    'tests/database/InputOutputExamples.regression.test.js',
    'tests/database/MigrationSystem.regression.test.js',
    'tests/database/DataPersistence.regression.test.js'
  ],
  ai: [
    'tests/ai-query/AIIntegration.regression.test.js'
  ],
  api: [
    'tests/api/NewFeatures.regression.test.js'
  ]
};

// Color codes for output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function parseArgs() {
  const args = process.argv.slice(2);
  const options = {
    suite: 'all',
    verbose: false
  };

  args.forEach(arg => {
    if (arg.startsWith('--suite=')) {
      options.suite = arg.split('=')[1];
    } else if (arg === '--verbose') {
      options.verbose = true;
    } else if (arg === '--help' || arg === '-h') {
      printHelp();
      process.exit(0);
    }
  });

  return options;
}

function printHelp() {
  log('Regression Test Runner for New Database and AI Features', 'cyan');
  log('');
  log('Usage:', 'yellow');
  log('  node tests/regression-runner.js [options]', 'white');
  log('');
  log('Options:', 'yellow');
  log('  --suite=<name>  Run specific test suite (database, ai, api, all)', 'white');
  log('  --verbose       Enable verbose output', 'white');
  log('  --help, -h      Show this help message', 'white');
  log('');
  log('Test Suites:', 'yellow');
  log('  database  - Database functionality (examples, migrations, persistence)', 'white');
  log('  ai        - AI integration tests (without real OpenAI API)', 'white');
  log('  api       - API endpoint tests for new features', 'white');
  log('  all       - All regression tests (default)', 'white');
  log('');
  log('Examples:', 'yellow');
  log('  node tests/regression-runner.js', 'white');
  log('  node tests/regression-runner.js --suite=database', 'white');
  log('  node tests/regression-runner.js --suite=ai --verbose', 'white');
}

function getTestFiles(suite) {
  if (suite === 'all') {
    return Object.values(testSuites).flat();
  }
  
  if (!testSuites[suite]) {
    log(`Error: Unknown test suite '${suite}'`, 'red');
    log(`Available suites: ${Object.keys(testSuites).join(', ')}`, 'yellow');
    process.exit(1);
  }
  
  return testSuites[suite];
}

function runTests(testFiles, verbose = false) {
  return new Promise((resolve, reject) => {
    const vitestArgs = [
      'run',
      ...testFiles,
      '--reporter=verbose'
    ];

    if (verbose) {
      vitestArgs.push('--reporter=verbose');
    }

    log('Starting regression tests...', 'cyan');
    log(`Running ${testFiles.length} test files`, 'blue');
    
    if (verbose) {
      log('Test files:', 'yellow');
      testFiles.forEach(file => log(`  - ${file}`, 'white'));
      log('');
    }

    const vitest = spawn('npx', ['vitest', ...vitestArgs], {
      stdio: 'inherit',
      cwd: path.resolve(__dirname, '..')
    });

    vitest.on('close', (code) => {
      if (code === 0) {
        log('All regression tests passed! ✅', 'green');
        resolve(code);
      } else {
        log(`Tests failed with exit code ${code} ❌`, 'red');
        reject(new Error(`Tests failed with exit code ${code}`));
      }
    });

    vitest.on('error', (error) => {
      log(`Error running tests: ${error.message}`, 'red');
      reject(error);
    });
  });
}

function printSummary() {
  log('', 'reset');
  log('='.repeat(60), 'cyan');
  log('Regression Test Summary', 'cyan');
  log('='.repeat(60), 'cyan');
  log('');
  log('These tests verify the integrity of recently added features:', 'white');
  log('');
  log('✓ Database Input/Output Examples:', 'green');
  log('  - JSON validation and structure', 'white');
  log('  - CRUD operations with examples', 'white');
  log('  - Data integrity and constraints', 'white');
  log('');
  log('✓ Migration System Integrity:', 'green');
  log('  - Migration execution (up/down)', 'white');
  log('  - Migration tracking and state management', 'white');
  log('  - Rollback capabilities', 'white');
  log('');
  log('✓ AI Integration (Simulation Mode):', 'green');
  log('  - Service initialization without API key', 'white');
  log('  - Citation extraction and parsing', 'white');
  log('  - Fallback simulation functionality', 'white');
  log('');
  log('✓ API Endpoint Changes:', 'green');
  log('  - Enhanced prompt CRUD with examples', 'white');
  log('  - Authentication with new user fields', 'white');
  log('  - Error handling for new features', 'white');
  log('');
  log('✓ Data Persistence:', 'green');
  log('  - Complex JSON data storage', 'white');
  log('  - Transaction integrity', 'white');
  log('  - Performance with large datasets', 'white');
  log('');
  log('These tests prevent regressions during refactoring and ensure', 'yellow');
  log('that new features continue to work correctly.', 'yellow');
  log('');
}

async function main() {
  const options = parseArgs();
  
  try {
    const testFiles = getTestFiles(options.suite);
    await runTests(testFiles, options.verbose);
    printSummary();
    process.exit(0);
  } catch (error) {
    log(`Failed to run regression tests: ${error.message}`, 'red');
    process.exit(1);
  }
}

// Run if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(error => {
    console.error('Unhandled error:', error);
    process.exit(1);
  });
}

export { testSuites, runTests };