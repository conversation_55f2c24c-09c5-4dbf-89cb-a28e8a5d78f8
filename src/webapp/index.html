<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Apollo Prompt Library</title>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.jsx"></script>
    <!-- Database debug tools for development -->
    <script>
      // Only load debug tools in development
      if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
        fetch('/src/debug/dbCheck.js')
          .then(response => response.text())
          .then(script => {
            eval(script);
            console.log('%c🔧 Database debug tools loaded. Type dbCheck.fullReport() for diagnostics.', 
                       'color: #007B63; font-weight: bold;');
          })
          .catch(() => console.log('Debug tools not available'));
      }
    </script>
  </body>
</html>
