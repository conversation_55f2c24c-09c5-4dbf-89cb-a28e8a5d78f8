# Database Debug Tools

## Quick Diagnostic Commands

Open browser console and run these commands to check the localStorage database:

### Basic Commands

```javascript
// Load the debug tools (copy/paste the entire dbCheck.js content into console)

// Get full diagnostic report
dbCheck.fullReport()

// Count entries in each storage key
dbCheck.countEntries()

// List all prompts
dbCheck.listPrompts()

// List all categories  
dbCheck.listCategories()

// Show current environment config
dbCheck.showConfig()

// Clear all data and reinitialize
dbCheck.clearAndReinitialize()
```

### Manual localStorage Inspection

```javascript
// Check what's actually stored
Object.keys(localStorage).filter(k => k.startsWith('apollo_'))

// Check prompts specifically
JSON.parse(localStorage.getItem('apollo_prompts') || '[]').length

// Check if data exists
localStorage.getItem('apollo_prompts') !== null
```

### Expected Results

**Database Mode Working Correctly:**
- `apollo_prompts`: 40 entries
- `apollo_categories`: 15+ entries  
- `apollo_users`: 4 entries
- `apollo_votes`: Variable (based on user interactions)
- `apollo_sessions`: Variable

**If Only 3 Prompts:**
- Check if test environment detection is still triggering
- Look for environment variables or test flags
- Verify config.USE_DATABASE_INSTEAD_OF_JSON_FILE is true

## Troubleshooting Steps

1. **Check Current Data**: `dbCheck.countEntries()`
2. **Verify Environment**: `dbCheck.showConfig()`  
3. **Clear and Reset**: `dbCheck.clearAndReinitialize()`
4. **Check Network Tab**: Look for config.js to verify USE_DATABASE_INSTEAD_OF_JSON_FILE value
5. **Check Console**: Look for initialization messages about which backend is being used