// Database diagnostic scripts for localStorage
// Run these in browser console to check database state

// Check localStorage keys and data
window.dbCheck = {
  // List all Apollo localStorage keys
  listKeys() {
    const apolloKeys = Object.keys(localStorage).filter(key => key.startsWith('apollo_'));
    console.log('Apollo localStorage keys:', apolloKeys);
    return apolloKeys;
  },

  // Count entries in each storage key
  countEntries() {
    const counts = {};
    const apolloKeys = this.listKeys();
    
    apolloKeys.forEach(key => {
      try {
        const data = JSON.parse(localStorage.getItem(key));
        counts[key] = Array.isArray(data) ? data.length : 'Not an array';
      } catch (e) {
        counts[key] = 'Parse error';
      }
    });
    
    console.log('Entry counts:', counts);
    return counts;
  },

  // Show all prompts in database
  listPrompts() {
    try {
      const prompts = JSON.parse(localStorage.getItem('apollo_prompts') || '[]');
      console.log(`Found ${prompts.length} prompts:`);
      prompts.forEach((prompt, index) => {
        console.log(`${index + 1}. ${prompt.title} (ID: ${prompt.id})`);
      });
      return prompts;
    } catch (e) {
      console.error('Error reading prompts:', e);
      return [];
    }
  },

  // Show all categories
  listCategories() {
    try {
      const categories = JSON.parse(localStorage.getItem('apollo_categories') || '[]');
      console.log(`Found ${categories.length} categories:`);
      categories.forEach((cat, index) => {
        console.log(`${index + 1}. ${cat.name} (${cat.count} prompts)`);
      });
      return categories;
    } catch (e) {
      console.error('Error reading categories:', e);
      return [];
    }
  },

  // Clear all Apollo data and reinitialize
  clearAndReinitialize() {
    console.log('Clearing Apollo localStorage data...');
    const apolloKeys = this.listKeys();
    apolloKeys.forEach(key => localStorage.removeItem(key));
    
    console.log('Reloading page to reinitialize...');
    window.location.reload();
  },

  // Force clear prompts only to trigger reinitialize with full dataset
  forceFullDataset() {
    console.log('Forcing full 40-prompt dataset...');
    localStorage.removeItem('apollo_prompts');
    console.log('Reloading page to load full prompts...');
    window.location.reload();
  },

  // Show current config
  showConfig() {
    // Try to access the config from the app
    console.log('Current environment detection:');
    console.log('- hostname:', window.location.hostname);
    console.log('- NODE_ENV:', process?.env?.NODE_ENV || 'undefined');
    console.log('- vitest:', !!window.__vitest__);
    
    // Check if we can access the config module
    try {
      console.log('If you can see the config in the network tab, check USE_DATABASE_INSTEAD_OF_JSON_FILE value');
    } catch (e) {
      console.log('Cannot access config directly from console');
    }
  },

  // Full diagnostic report
  fullReport() {
    console.log('=== APOLLO DATABASE DIAGNOSTIC REPORT ===');
    console.log('');
    
    console.log('1. Environment:');
    this.showConfig();
    console.log('');
    
    console.log('2. localStorage Keys:');
    this.listKeys();
    console.log('');
    
    console.log('3. Entry Counts:');
    this.countEntries();
    console.log('');
    
    console.log('4. Prompts:');
    this.listPrompts();
    console.log('');
    
    console.log('5. Categories:');
    this.listCategories();
    console.log('');
    
    console.log('=== END REPORT ===');
    console.log('');
    console.log('To clear and reinitialize: dbCheck.clearAndReinitialize()');
    console.log('To force full 40 prompts: dbCheck.forceFullDataset()');
  }
};

// Auto-run basic check when script loads
console.log('Database check tools loaded. Run dbCheck.fullReport() for complete diagnostic.');
console.log('Quick check:');
window.dbCheck.countEntries();