import { useState, useEffect } from 'react'
import TwoTierSidebar from './components/TwoTierSidebar'
import MainContent from './components/MainContent'
import PromptDetail from './components/PromptDetail'
import LandingPage from './components/LandingPage'
import LoginModal from './auth/LoginModal'
import NewPromptModal from './components/NewPromptModal'
import ExportModal from './components/ExportModal'
import { HomeModal, ChatModal, PeopleModal, ContentModal, AppsModal } from './components/NavigationModal'
import { useAuth } from './auth/AuthContext'
import { useFilters } from './hooks/useFilters'
import { useEscapeKey } from './hooks/useKeyboard'
import config from './config.js'
import { statuses } from './mockData'
import StreamingAIQueryInterface from './ai-query/organisms/StreamingAIQueryInterface'
import './App.css'

// Dynamic API imports - will be resolved at runtime
let apiPromise

if (config.USE_DATABASE_INSTEAD_OF_JSON_FILE) {
  console.log('🗄️ Using browser database backend (localStorage)')
  apiPromise = import('./api/browserEndpoints.js').then(endpoints => ({
    promptsAPI: endpoints.promptsAPI,
    categoriesAPI: endpoints.categoriesAPI,
    migrateJsonToDatabase: endpoints.migrateJsonToDatabase
  }))
} else {
  console.log('📄 Using in-memory JSON backend')
  apiPromise = import('./api/client.js').then(client => ({
    promptsAPI: client.promptsAPI,
    categoriesAPI: client.categoriesAPI,
    migrateJsonToDatabase: client.migrateJsonToDatabase
  }))
}

function App() {
  const { user, loading: authLoading, isAuthenticated } = useAuth()
  const [prompts, setPrompts] = useState([])
  const [categories, setCategories] = useState([])
  const [showDetailPreview, setShowDetailPreview] = useState(false)
  const [selectedPromptId, setSelectedPromptId] = useState(null)
  const [scrollPosition, setScrollPosition] = useState(0)
  const [showLoginModal, setShowLoginModal] = useState(false)
  const [showNewPromptModal, setShowNewPromptModal] = useState(false)
  const [showExportModal, setShowExportModal] = useState(false)
  const [showHomeModal, setShowHomeModal] = useState(false)
  const [showChatModal, setShowChatModal] = useState(false)
  const [showPeopleModal, setShowPeopleModal] = useState(false)
  const [showContentModal, setShowContentModal] = useState(false)
  const [showAppsModal, setShowAppsModal] = useState(false)
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)
  
  // Initialize filters hook with prompts and categories
  const {
    filteredPrompts,
    filters,
    setFilter,
    resetFilters,
    toggleMultiFilter,
    filterCounts,
    activeFilterCount,
    hasActiveFilters
  } = useFilters(prompts, categories, {
    sortBy: 'trending'
  })
  
  // AI Chat state - shared across all views
  const [aiChatMessages, setAiChatMessages] = useState([])
  const [aiChatQuery, setAiChatQuery] = useState('')
  const [aiChatLoading, setAiChatLoading] = useState(false)
  const [aiChatExpanded, setAiChatExpanded] = useState(false)

  const handleVote = async (promptId, direction) => {
    if (!isAuthenticated) {
      setShowLoginModal(true)
      return
    }

    try {
      const { promptsAPI } = await apiPromise
      const updatedPrompt = await promptsAPI.vote(promptId, user.id, direction)
      setPrompts(prevPrompts => 
        prevPrompts.map(prompt => 
          prompt.id === promptId ? updatedPrompt : prompt
        )
      )
    } catch (error) {
      console.error('Failed to vote:', error)
      setError('Failed to vote. Please try again.')
    }
  }

  const handleCardClick = (promptId) => {
    console.log('Navigate to prompt:', promptId)
    // Save current scroll position before opening detail
    setScrollPosition(window.scrollY)
    setSelectedPromptId(promptId)
    setShowDetailPreview(true)
  }

  // Load data on component mount
  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true)
        
        // Wait for dynamic API to be ready
        const { promptsAPI, categoriesAPI, migrateJsonToDatabase } = await apiPromise
        
        // Run migration first
        await migrateJsonToDatabase()
        
        // Load prompts and categories
        const [promptsData, categoriesData] = await Promise.all([
          promptsAPI.getAll({ sortBy: filters.sortBy }, user?.id),
          categoriesAPI.getAll()
        ])
        
        setPrompts(promptsData)
        setCategories(categoriesData)
      } catch (error) {
        console.error('Failed to load data:', error)
        setError('Failed to load data. Please refresh the page.')
      } finally {
        setLoading(false)
      }
    }

    loadData()
  }, [filters.sortBy, user?.id])


  // ESC key to close detail preview
  useEscapeKey(() => {
    if (showDetailPreview) {
      setShowDetailPreview(false)
      setSelectedPromptId(null)
    }
  }, showDetailPreview, [showDetailPreview])

  // Show loading screen
  if (authLoading || loading) {
    return (
      <div className="app" style={{ display: 'flex', alignItems: 'center', justifyContent: 'center', height: '100vh' }}>
        <div style={{ textAlign: 'center' }}>
          <div style={{ fontSize: '24px', marginBottom: '16px' }}>Loading Apollo Prompt Library...</div>
          <div style={{ color: '#666' }}>Initializing database and loading prompts</div>
        </div>
      </div>
    )
  }

  // Show error state
  if (error) {
    return (
      <div className="app" style={{ display: 'flex', alignItems: 'center', justifyContent: 'center', height: '100vh' }}>
        <div style={{ textAlign: 'center', maxWidth: '400px' }}>
          <div style={{ fontSize: '24px', marginBottom: '16px', color: '#dc2626' }}>Error</div>
          <div style={{ color: '#666', marginBottom: '20px' }}>{error}</div>
          <button onClick={() => window.location.reload()} style={{ padding: '8px 16px', background: '#007acc', color: 'white', border: 'none', borderRadius: '4px', cursor: 'pointer' }}>
            Reload Page
          </button>
        </div>
      </div>
    )
  }

  if (showDetailPreview) {
    return (
      <div className="app">
        <div style={{ position: 'fixed', top: '10px', right: '10px', zIndex: 1000, background: '#333', color: 'white', padding: '10px', borderRadius: '5px' }}>
          Press 'Shift+/' or 'ESC' to return to main view
        </div>
        <PromptDetail 
          promptId={selectedPromptId} 
          onBack={() => {
            setShowDetailPreview(false)
            setSelectedPromptId(null)
            // Restore scroll position when going back
            setTimeout(() => {
              window.scrollTo(0, scrollPosition)
            }, 0)
          }}
          onVote={handleVote}
        />
        <div className="ai-query-fixed">
          <StreamingAIQueryInterface
            prompts={prompts}
            categories={categories}
            onCitationClick={(promptId) => {
              const prompt = prompts.find(p => p.id === promptId)
              if (prompt) {
                setSelectedPromptId(promptId)
                setShowDetailPreview(true)
              }
            }}
            messages={aiChatMessages}
            setMessages={setAiChatMessages}
            query={aiChatQuery}
            setQuery={setAiChatQuery}
            loading={aiChatLoading}
            setLoading={setAiChatLoading}
            isExpanded={aiChatExpanded}
            setIsExpanded={setAiChatExpanded}
          />
        </div>
      </div>
    )
  }

  // Show landing page for unauthenticated users
  if (!isAuthenticated) {
    return (
      <>
        <LandingPage onLoginClick={() => setShowLoginModal(true)} />
        <LoginModal 
          isOpen={showLoginModal} 
          onClose={() => setShowLoginModal(false)} 
        />
      </>
    )
  }

  return (
    <div className="app">
      
      <LoginModal 
        isOpen={showLoginModal} 
        onClose={() => setShowLoginModal(false)} 
      />
      
      <NewPromptModal 
        isOpen={showNewPromptModal} 
        onClose={() => setShowNewPromptModal(false)}
        onPromptCreated={(newPrompt) => {
          setPrompts(prev => [newPrompt, ...prev])
          setShowNewPromptModal(false)
        }}
      />
      
      <ExportModal 
        isOpen={showExportModal} 
        onClose={() => setShowExportModal(false)}
        prompts={prompts}
      />
      
      <HomeModal 
        isOpen={showHomeModal} 
        onClose={() => setShowHomeModal(false)}
      />
      
      <ChatModal 
        isOpen={showChatModal} 
        onClose={() => setShowChatModal(false)}
      />
      
      <PeopleModal 
        isOpen={showPeopleModal} 
        onClose={() => setShowPeopleModal(false)}
      />
      
      <ContentModal 
        isOpen={showContentModal} 
        onClose={() => setShowContentModal(false)}
      />
      
      <AppsModal 
        isOpen={showAppsModal} 
        onClose={() => setShowAppsModal(false)}
      />
      
      <div className="app-body">
        <TwoTierSidebar
          statuses={statuses}
          categories={categories}
          selectedStatus={Array.isArray(filters.status) ? filters.status[0] || '' : filters.status}
          setSelectedStatus={(status) => setFilter('status', status ? [status] : [])}
          selectedCategory={filters.category}
          setSelectedCategory={(category) => setFilter('category', category)}
          onLoginClick={() => setShowLoginModal(true)}
          onNewPromptClick={() => setShowNewPromptModal(true)}
          onExportClick={() => setShowExportModal(true)}
          onClearCache={() => {
            localStorage.clear()
            window.location.reload()
          }}
          onHomeClick={() => setShowHomeModal(true)}
          onChatClick={() => setShowChatModal(true)}
          onPeopleClick={() => setShowPeopleModal(true)}
          onContentClick={() => setShowContentModal(true)}
          onAppsClick={() => setShowAppsModal(true)}
          isMobileOpen={isMobileMenuOpen}
          setIsMobileOpen={setIsMobileMenuOpen}
          // Pass filter utilities for enhanced functionality
          filterCounts={filterCounts}
          activeFilterCount={activeFilterCount}
          hasActiveFilters={hasActiveFilters}
          onResetFilters={resetFilters}
        />
        <MainContent
          prompts={filteredPrompts}
          categories={categories}
          sortBy={filters.sortBy}
          setSortBy={(sortBy) => setFilter('sortBy', sortBy)}
          searchTerm={filters.searchQuery}
          setSearchTerm={(searchTerm) => setFilter('searchQuery', searchTerm)}
          onVote={handleVote}
          onCardClick={handleCardClick}
          onNewPromptClick={() => setShowNewPromptModal(true)}
          onCitationClick={(promptId) => {
            const prompt = prompts.find(p => p.id === promptId)
            if (prompt) {
              setSelectedPromptId(promptId)
              setShowDetailPreview(true)
            }
          }}
          messages={aiChatMessages}
          setMessages={setAiChatMessages}
          query={aiChatQuery}
          setQuery={setAiChatQuery}
          loading={aiChatLoading}
          setLoading={setAiChatLoading}
          isExpanded={aiChatExpanded}
          setIsExpanded={setAiChatExpanded}
          onMobileMenuToggle={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
          // Pass additional filter utilities
          filters={filters}
          setFilter={setFilter}
          activeFilterCount={activeFilterCount}
          hasActiveFilters={hasActiveFilters}
          onResetFilters={resetFilters}
        />
      </div>
    </div>
  )
}

export default App
