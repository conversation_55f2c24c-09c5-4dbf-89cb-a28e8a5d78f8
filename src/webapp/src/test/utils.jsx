import React from 'react'
import { render } from '@testing-library/react'
import { vi } from 'vitest'

// Note: API client mocking is done in individual test files to allow for specific test data

// Mock user data
export const mockUser = {
  id: 1,
  username: 'testuser',
  email: '<EMAIL>',
  fullName: 'Test User',
  role: 'user'
}

// Mock the useAuth hook
vi.mock('../auth/AuthContext', () => ({
  useAuth: () => ({
    user: mockUser,
    loading: false,
    sessionToken: 'mock-session-token',
    login: vi.fn().mockResolvedValue(mockUser),
    logout: vi.fn().mockResolvedValue(),
    isAuthenticated: true
  }),
  AuthProvider: ({ children }) => children
}))

// Custom render function that wraps components with common providers
export function renderWithProviders(ui, options = {}) {
  const {
    initialProps = {},
    ...renderOptions
  } = options

  function Wrapper({ children }) {
    // Since we're mocking AuthContext, just return children directly
    return children
  }

  return {
    ...render(ui, { wrapper: Wrapper, ...renderOptions }),
  }
}

// Mock data generators
export const mockPromptData = {
  id: 1,
  title: 'Test Prompt',
  description: 'A test prompt description',
  category: 'Test Category',
  tags: ['tag1', 'tag2'],
  status: 'Approved',
  votes: 10,
  author: 'Test Author',
  timeAgo: '1 hour ago',
  statusCount: 5,
  difficulty: 'Beginner',
  comments: 3,
  userVote: null,
  content: 'This is test content for the prompt.'
}

export const mockPromptsArray = [
  mockPromptData,
  {
    ...mockPromptData,
    id: 2,
    title: 'Second Test Prompt',
    category: 'Another Category',
    votes: 15,
    difficulty: 'Intermediate'
  }
]

// Common test utilities
export const createMockEvent = (overrides = {}) => ({
  preventDefault: vi.fn(),
  stopPropagation: vi.fn(),
  target: { value: '' },
  ...overrides
})

// Helper to wait for async operations
export const waitForLoadingToFinish = () => 
  new Promise(resolve => setTimeout(resolve, 0))

// Helper function to wait for app to finish loading
export const waitForAppToLoad = async () => {
  const { waitFor } = await import('@testing-library/react')
  const { screen } = await import('@testing-library/react')
  
  await waitFor(() => {
    expect(screen.queryByText('Loading Apollo Prompt Library...')).not.toBeInTheDocument()
  }, { timeout: 3000 })
}

// Mock intersection observer entry
export const createMockIntersectionEntry = (isIntersecting = true) => ({
  isIntersecting,
  target: document.createElement('div'),
  intersectionRatio: isIntersecting ? 1 : 0,
  boundingClientRect: {},
  intersectionRect: {},
  rootBounds: {},
  time: Date.now()
})

// Mock localStorage for tests
const createMockStorage = () => {
  let store = {}
  return {
    getItem: vi.fn((key) => store[key] || null),
    setItem: vi.fn((key, value) => { store[key] = value.toString() }),
    removeItem: vi.fn((key) => { delete store[key] }),
    clear: vi.fn(() => { store = {} })
  }
}

// Set up localStorage mock globally
Object.defineProperty(global, 'localStorage', {
  value: createMockStorage(),
  writable: true
})

// Mock localStorage globally
Object.defineProperty(window, 'localStorage', {
  value: createMockStorage()
})

// Mock window.scrollTo for tests
Object.defineProperty(window, 'scrollTo', {
  value: vi.fn()
})

// Re-export everything from testing-library
export * from '@testing-library/react'
export { vi } from 'vitest'