# Test Setup Documentation

This directory contains the test configuration and utilities for the React application.

## Files Structure

- `setup.js` - Test environment setup with mocks and globals
- `utils.jsx` - Custom render functions and test utilities
- `example.test.jsx` - Example test patterns and usage demonstrations

## Test Configuration

The project uses:
- **Vitest** as the test runner
- **React Testing Library** for component testing
- **jsdom** for DOM simulation
- **@testing-library/jest-dom** for additional matchers

## Available Test Scripts

```bash
npm test          # Run tests in watch mode
npm run test:run  # Run tests once
npm run test:ui   # Run tests with UI interface
npm run test:coverage  # Run tests with coverage report
npm run test:watch     # Run tests in watch mode (explicit)
```

## Key Features

### Global Mocks
- IntersectionObserver
- ResizeObserver
- matchMedia
- scrollIntoView

### Test Utilities
- `renderWithProviders()` - Custom render function for components
- Mock data generators for prompts
- Common event creators
- CSS class testing helpers

### Example Patterns
The `example.test.jsx` file demonstrates:
- Basic component rendering
- Event handling
- Async testing with waitFor
- Using mock data
- CSS class testing

## Writing Tests

Place test files in `__tests__` directories next to the components they test, following the pattern:
```
src/components/
├── ComponentName.jsx
└── __tests__/
    └── ComponentName.test.jsx
```

Import utilities from the test utils:
```javascript
import { renderWithProviders, mockPromptData } from '../test/utils'
```