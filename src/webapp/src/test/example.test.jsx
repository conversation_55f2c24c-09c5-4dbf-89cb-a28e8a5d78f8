import { describe, it, expect, vi } from 'vitest'
import { screen, fireEvent, waitFor } from '@testing-library/react'
import React from 'react'
import { renderWithProviders, mockPromptData, createMockEvent } from './utils'

// Example test demonstrating common testing patterns
describe('Example Test Patterns', () => {
  it('demonstrates basic component rendering', () => {
    // Simple component test
    const SimpleComponent = ({ title, onClick }) => (
      <div onClick={onClick}>
        <h1>{title}</h1>
        <button>Click me</button>
      </div>
    )

    const mockClick = vi.fn()
    renderWithProviders(<SimpleComponent title="Test Title" onClick={mockClick} />)

    expect(screen.getByText('Test Title')).toBeInTheDocument()
    expect(screen.getByText('Click me')).toBeInTheDocument()
  })

  it('demonstrates event handling', () => {
    const ButtonComponent = ({ onClick }) => (
      <div>
        <input name="test" defaultValue="test value" />
        <button onClick={onClick}>Submit</button>
      </div>
    )

    const mockClick = vi.fn()
    renderWithProviders(<ButtonComponent onClick={mockClick} />)

    const submitButton = screen.getByText('Submit')
    fireEvent.click(submitButton)

    expect(mockClick).toHaveBeenCalledTimes(1)
  })

  it('demonstrates async testing', async () => {
    const AsyncComponent = () => {
      const [loading, setLoading] = React.useState(true)
      const [data, setData] = React.useState(null)

      React.useEffect(() => {
        setTimeout(() => {
          setData('Loaded data')
          setLoading(false)
        }, 100)
      }, [])

      if (loading) return <div>Loading...</div>
      return <div>{data}</div>
    }

    renderWithProviders(<AsyncComponent />)

    expect(screen.getByText('Loading...')).toBeInTheDocument()
    
    await waitFor(() => {
      expect(screen.getByText('Loaded data')).toBeInTheDocument()
    })
  })

  it('demonstrates using mock data', () => {
    const DataComponent = ({ prompt }) => (
      <div>
        <h2>{prompt.title}</h2>
        <p>{prompt.description}</p>
        <span>By {prompt.author}</span>
      </div>
    )

    renderWithProviders(<DataComponent prompt={mockPromptData} />)

    expect(screen.getByText('Test Prompt')).toBeInTheDocument()
    expect(screen.getByText('A test prompt description')).toBeInTheDocument()
    expect(screen.getByText('By Test Author')).toBeInTheDocument()
  })

  it('demonstrates CSS class testing', () => {
    const StyledComponent = ({ isActive }) => (
      <div className={`component ${isActive ? 'active' : ''}`}>
        Content
      </div>
    )

    const { rerender } = renderWithProviders(<StyledComponent isActive={false} />)
    
    let element = document.querySelector('.component')
    expect(element).toBeInTheDocument()
    expect(element).not.toHaveClass('active')

    rerender(<StyledComponent isActive={true} />)
    
    element = document.querySelector('.component')
    expect(element).toHaveClass('active')
  })
})