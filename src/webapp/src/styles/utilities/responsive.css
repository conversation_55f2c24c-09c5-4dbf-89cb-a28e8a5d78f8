/* Responsive Utilities - Centralized breakpoint logic and responsive helpers */

/* Apollo Breakpoints (using CSS custom properties for consistency) */
:root {
  --apollo-breakpoint-xs: 480px;
  --apollo-breakpoint-sm: 640px;
  --apollo-breakpoint-md: 768px;
  --apollo-breakpoint-lg: 1024px;
  --apollo-breakpoint-xl: 1280px;
  --apollo-breakpoint-2xl: 1536px;
}

/* Container utilities with Apollo breakpoints */
.apollo-container {
  width: 100%;
  margin-left: auto;
  margin-right: auto;
  padding-left: var(--apollo-space-lg);
  padding-right: var(--apollo-space-lg);
}

.apollo-container--xs {
  max-width: var(--apollo-breakpoint-xs);
}

.apollo-container--sm {
  max-width: var(--apollo-breakpoint-sm);
}

.apollo-container--md {
  max-width: var(--apollo-breakpoint-md);
}

.apollo-container--lg {
  max-width: var(--apollo-breakpoint-lg);
}

.apollo-container--xl {
  max-width: var(--apollo-breakpoint-xl);
}

.apollo-container--2xl {
  max-width: var(--apollo-breakpoint-2xl);
}

.apollo-container--fluid {
  max-width: 100%;
}

/* Responsive breakpoint mixins as CSS classes */
/* These can be used with CSS-in-JS or as utility classes */

/* Mobile-first responsive utilities */
.apollo-mobile-only {
  display: block;
}

.apollo-tablet-up {
  display: none;
}

.apollo-desktop-up {
  display: none;
}

.apollo-wide-up {
  display: none;
}

/* Breakpoint-specific display utilities */
@media (min-width: 640px) {
  .apollo-mobile-only {
    display: none !important;
  }
  
  .apollo-tablet-up {
    display: block;
  }
}

@media (min-width: 768px) {
  .apollo-tablet-only {
    display: none !important;
  }
}

@media (min-width: 1024px) {
  .apollo-tablet-up {
    display: none;
  }
  
  .apollo-desktop-up {
    display: block;
  }
}

@media (min-width: 1280px) {
  .apollo-desktop-only {
    display: none !important;
  }
  
  .apollo-wide-up {
    display: block;
  }
}

/* Grid utilities for different screen sizes */
.apollo-grid {
  display: grid;
  gap: var(--apollo-space-lg);
}

.apollo-grid--1 {
  grid-template-columns: 1fr;
}

.apollo-grid--2 {
  grid-template-columns: repeat(2, 1fr);
}

.apollo-grid--3 {
  grid-template-columns: repeat(3, 1fr);
}

.apollo-grid--4 {
  grid-template-columns: repeat(4, 1fr);
}

.apollo-grid--auto-fit {
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

.apollo-grid--auto-fill {
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
}

/* Responsive grid utilities */
@media (max-width: 767px) {
  .apollo-grid--2,
  .apollo-grid--3,
  .apollo-grid--4 {
    grid-template-columns: 1fr;
  }
  
  .apollo-grid--auto-fit,
  .apollo-grid--auto-fill {
    grid-template-columns: 1fr;
  }
}

@media (min-width: 768px) and (max-width: 1023px) {
  .apollo-grid--3,
  .apollo-grid--4 {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* Flexbox utilities for responsive layouts */
.apollo-flex {
  display: flex;
}

.apollo-flex--column {
  flex-direction: column;
}

.apollo-flex--row {
  flex-direction: row;
}

.apollo-flex--wrap {
  flex-wrap: wrap;
}

.apollo-flex--nowrap {
  flex-wrap: nowrap;
}

.apollo-flex--center {
  align-items: center;
  justify-content: center;
}

.apollo-flex--between {
  justify-content: space-between;
}

.apollo-flex--around {
  justify-content: space-around;
}

.apollo-flex--evenly {
  justify-content: space-evenly;
}

/* Responsive flexbox utilities */
@media (max-width: 767px) {
  .apollo-flex--mobile-column {
    flex-direction: column;
  }
  
  .apollo-flex--mobile-wrap {
    flex-wrap: wrap;
  }
  
  .apollo-flex--mobile-center {
    align-items: center;
    justify-content: center;
  }
}

@media (min-width: 768px) {
  .apollo-flex--tablet-row {
    flex-direction: row;
  }
  
  .apollo-flex--tablet-nowrap {
    flex-wrap: nowrap;
  }
}

/* Spacing utilities for responsive design */
.apollo-spacing--responsive {
  padding: var(--apollo-space-sm);
}

@media (min-width: 640px) {
  .apollo-spacing--responsive {
    padding: var(--apollo-space-lg);
  }
}

@media (min-width: 1024px) {
  .apollo-spacing--responsive {
    padding: var(--apollo-space-xl);
  }
}

.apollo-spacing--responsive-x {
  padding-left: var(--apollo-space-sm);
  padding-right: var(--apollo-space-sm);
}

@media (min-width: 640px) {
  .apollo-spacing--responsive-x {
    padding-left: var(--apollo-space-lg);
    padding-right: var(--apollo-space-lg);
  }
}

@media (min-width: 1024px) {
  .apollo-spacing--responsive-x {
    padding-left: var(--apollo-space-xl);
    padding-right: var(--apollo-space-xl);
  }
}

.apollo-spacing--responsive-y {
  padding-top: var(--apollo-space-sm);
  padding-bottom: var(--apollo-space-sm);
}

@media (min-width: 640px) {
  .apollo-spacing--responsive-y {
    padding-top: var(--apollo-space-lg);
    padding-bottom: var(--apollo-space-lg);
  }
}

@media (min-width: 1024px) {
  .apollo-spacing--responsive-y {
    padding-top: var(--apollo-space-xl);
    padding-bottom: var(--apollo-space-xl);
  }
}

/* Typography responsive utilities */
.apollo-text--responsive {
  font-size: var(--apollo-text-sm);
  line-height: 1.5;
}

@media (min-width: 640px) {
  .apollo-text--responsive {
    font-size: var(--apollo-text-base);
  }
}

@media (min-width: 1024px) {
  .apollo-text--responsive {
    font-size: var(--apollo-text-lg);
  }
}

.apollo-heading--responsive {
  font-size: var(--apollo-text-lg);
  font-weight: var(--apollo-font-semibold);
  line-height: 1.25;
}

@media (min-width: 640px) {
  .apollo-heading--responsive {
    font-size: var(--apollo-text-xl);
  }
}

@media (min-width: 1024px) {
  .apollo-heading--responsive {
    font-size: var(--apollo-text-2xl);
  }
}

/* Width utilities for responsive design */
.apollo-width--full {
  width: 100%;
}

.apollo-width--auto {
  width: auto;
}

.apollo-width--fit {
  width: fit-content;
}

.apollo-width--min {
  width: min-content;
}

.apollo-width--max {
  width: max-content;
}

/* Responsive width utilities */
@media (max-width: 767px) {
  .apollo-width--mobile-full {
    width: 100%;
  }
  
  .apollo-width--mobile-auto {
    width: auto;
  }
}

@media (min-width: 768px) {
  .apollo-width--tablet-half {
    width: 50%;
  }
  
  .apollo-width--tablet-third {
    width: 33.333333%;
  }
  
  .apollo-width--tablet-quarter {
    width: 25%;
  }
}

/* Height utilities for responsive design */
.apollo-height--screen {
  height: 100vh;
  height: 100dvh; /* Dynamic viewport height for mobile */
}

.apollo-height--full {
  height: 100%;
}

.apollo-height--auto {
  height: auto;
}

.apollo-height--fit {
  height: fit-content;
}

/* Responsive height utilities */
@media (max-width: 767px) {
  .apollo-height--mobile-auto {
    height: auto;
  }
  
  .apollo-height--mobile-fit {
    height: fit-content;
  }
}

/* Aspect ratio utilities */
.apollo-aspect--square {
  aspect-ratio: 1 / 1;
}

.apollo-aspect--video {
  aspect-ratio: 16 / 9;
}

.apollo-aspect--photo {
  aspect-ratio: 4 / 3;
}

.apollo-aspect--portrait {
  aspect-ratio: 3 / 4;
}

/* Position utilities for responsive design */
.apollo-position--relative {
  position: relative;
}

.apollo-position--absolute {
  position: absolute;
}

.apollo-position--fixed {
  position: fixed;
}

.apollo-position--sticky {
  position: sticky;
}

/* Responsive position utilities */
@media (max-width: 767px) {
  .apollo-position--mobile-static {
    position: static;
  }
  
  .apollo-position--mobile-relative {
    position: relative;
  }
}

@media (min-width: 768px) {
  .apollo-position--tablet-sticky {
    position: sticky;
  }
  
  .apollo-position--tablet-fixed {
    position: fixed;
  }
}

/* Z-index utilities */
.apollo-z--auto {
  z-index: auto;
}

.apollo-z--0 {
  z-index: 0;
}

.apollo-z--10 {
  z-index: 10;
}

.apollo-z--20 {
  z-index: 20;
}

.apollo-z--30 {
  z-index: 30;
}

.apollo-z--40 {
  z-index: 40;
}

.apollo-z--50 {
  z-index: 50;
}

.apollo-z--modal {
  z-index: 1000;
}

.apollo-z--overlay {
  z-index: 1001;
}

.apollo-z--tooltip {
  z-index: 1002;
}

/* Overflow utilities for responsive design */
.apollo-overflow--hidden {
  overflow: hidden;
}

.apollo-overflow--auto {
  overflow: auto;
}

.apollo-overflow--scroll {
  overflow: scroll;
}

.apollo-overflow--visible {
  overflow: visible;
}

.apollo-overflow-x--hidden {
  overflow-x: hidden;
}

.apollo-overflow-x--auto {
  overflow-x: auto;
}

.apollo-overflow-y--hidden {
  overflow-y: hidden;
}

.apollo-overflow-y--auto {
  overflow-y: auto;
}

/* Responsive overflow utilities */
@media (max-width: 767px) {
  .apollo-overflow--mobile-hidden {
    overflow: hidden;
  }
  
  .apollo-overflow-x--mobile-auto {
    overflow-x: auto;
  }
}

/* Custom media query helpers for JavaScript */
/* These can be used with matchMedia in JavaScript */
@custom-media --apollo-mobile (max-width: 767px);
@custom-media --apollo-tablet (min-width: 768px) and (max-width: 1023px);
@custom-media --apollo-desktop (min-width: 1024px);
@custom-media --apollo-wide (min-width: 1280px);

/* Print styles */
@media print {
  .apollo-print--hidden {
    display: none !important;
  }
  
  .apollo-print--visible {
    display: block !important;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .apollo-motion--respect {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}