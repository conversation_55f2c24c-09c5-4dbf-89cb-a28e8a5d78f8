/* Animation Utilities - Standardized transitions, hover effects, and animations */

/* Base animation variables using Apollo design tokens */
:root {
  --apollo-transition-fast: 0.15s;
  --apollo-transition-normal: 0.2s;
  --apollo-transition-slow: 0.3s;
  --apollo-transition-slower: 0.5s;
  
  --apollo-easing-linear: linear;
  --apollo-easing-ease: ease;
  --apollo-easing-ease-in: ease-in;
  --apollo-easing-ease-out: ease-out;
  --apollo-easing-ease-in-out: ease-in-out;
  --apollo-easing-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);
  --apollo-easing-smooth: cubic-bezier(0.4, 0, 0.2, 1);
}

/* Standard transition utilities */
.apollo-transition {
  transition-duration: var(--apollo-transition-normal);
  transition-timing-function: var(--apollo-easing-smooth);
  transition-property: all;
}

.apollo-transition--fast {
  transition-duration: var(--apollo-transition-fast);
}

.apollo-transition--slow {
  transition-duration: var(--apollo-transition-slow);
}

.apollo-transition--slower {
  transition-duration: var(--apollo-transition-slower);
}

/* Specific property transitions */
.apollo-transition--colors {
  transition-property: color, background-color, border-color;
  transition-duration: var(--apollo-transition-normal);
  transition-timing-function: var(--apollo-easing-smooth);
}

.apollo-transition--opacity {
  transition-property: opacity;
  transition-duration: var(--apollo-transition-normal);
  transition-timing-function: var(--apollo-easing-smooth);
}

.apollo-transition--transform {
  transition-property: transform;
  transition-duration: var(--apollo-transition-normal);
  transition-timing-function: var(--apollo-easing-smooth);
}

.apollo-transition--size {
  transition-property: width, height;
  transition-duration: var(--apollo-transition-normal);
  transition-timing-function: var(--apollo-easing-smooth);
}

.apollo-transition--shadow {
  transition-property: box-shadow;
  transition-duration: var(--apollo-transition-normal);
  transition-timing-function: var(--apollo-easing-smooth);
}

/* Easing utilities */
.apollo-ease--linear {
  transition-timing-function: var(--apollo-easing-linear);
}

.apollo-ease--in {
  transition-timing-function: var(--apollo-easing-ease-in);
}

.apollo-ease--out {
  transition-timing-function: var(--apollo-easing-ease-out);
}

.apollo-ease--in-out {
  transition-timing-function: var(--apollo-easing-ease-in-out);
}

.apollo-ease--bounce {
  transition-timing-function: var(--apollo-easing-bounce);
}

.apollo-ease--smooth {
  transition-timing-function: var(--apollo-easing-smooth);
}

/* Hover effects */
.apollo-hover--lift {
  transition: transform var(--apollo-transition-normal) var(--apollo-easing-smooth),
              box-shadow var(--apollo-transition-normal) var(--apollo-easing-smooth);
}

.apollo-hover--lift:hover {
  transform: translateY(-2px);
  box-shadow: var(--apollo-shadow-md);
}

.apollo-hover--lift-sm {
  transition: transform var(--apollo-transition-fast) var(--apollo-easing-smooth);
}

.apollo-hover--lift-sm:hover {
  transform: translateY(-1px);
}

.apollo-hover--lift-lg {
  transition: transform var(--apollo-transition-normal) var(--apollo-easing-smooth),
              box-shadow var(--apollo-transition-normal) var(--apollo-easing-smooth);
}

.apollo-hover--lift-lg:hover {
  transform: translateY(-4px);
  box-shadow: var(--apollo-shadow-lg);
}

/* Scale hover effects */
.apollo-hover--scale {
  transition: transform var(--apollo-transition-normal) var(--apollo-easing-smooth);
}

.apollo-hover--scale:hover {
  transform: scale(1.05);
}

.apollo-hover--scale-sm {
  transition: transform var(--apollo-transition-fast) var(--apollo-easing-smooth);
}

.apollo-hover--scale-sm:hover {
  transform: scale(1.02);
}

.apollo-hover--scale-lg {
  transition: transform var(--apollo-transition-normal) var(--apollo-easing-smooth);
}

.apollo-hover--scale-lg:hover {
  transform: scale(1.1);
}

/* Brightness/opacity hover effects */
.apollo-hover--brighten {
  transition: filter var(--apollo-transition-normal) var(--apollo-easing-smooth);
}

.apollo-hover--brighten:hover {
  filter: brightness(1.1);
}

.apollo-hover--dim {
  transition: opacity var(--apollo-transition-normal) var(--apollo-easing-smooth);
}

.apollo-hover--dim:hover {
  opacity: 0.8;
}

.apollo-hover--fade {
  transition: opacity var(--apollo-transition-normal) var(--apollo-easing-smooth);
}

.apollo-hover--fade:hover {
  opacity: 0.6;
}

/* Color hover effects */
.apollo-hover--primary {
  transition: color var(--apollo-transition-normal) var(--apollo-easing-smooth),
              background-color var(--apollo-transition-normal) var(--apollo-easing-smooth);
}

.apollo-hover--primary:hover {
  color: var(--apollo-primary-hover);
  background-color: var(--apollo-primary-light);
}

.apollo-hover--secondary {
  transition: color var(--apollo-transition-normal) var(--apollo-easing-smooth),
              background-color var(--apollo-transition-normal) var(--apollo-easing-smooth);
}

.apollo-hover--secondary:hover {
  color: var(--apollo-text-primary);
  background-color: var(--apollo-bg-card-hover);
}

/* Focus effects */
.apollo-focus--ring {
  transition: box-shadow var(--apollo-transition-fast) var(--apollo-easing-smooth);
}

.apollo-focus--ring:focus,
.apollo-focus--ring:focus-visible {
  outline: none;
  box-shadow: 0 0 0 2px var(--apollo-primary);
}

.apollo-focus--ring-subtle {
  transition: box-shadow var(--apollo-transition-fast) var(--apollo-easing-smooth);
}

.apollo-focus--ring-subtle:focus,
.apollo-focus--ring-subtle:focus-visible {
  outline: none;
  box-shadow: 0 0 0 2px rgba(0, 123, 99, 0.25);
}

/* Loading animations */
@keyframes apollo-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes apollo-pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes apollo-bounce {
  0%, 100% {
    transform: translateY(0);
    animation-timing-function: cubic-bezier(0.8, 0, 1, 1);
  }
  50% {
    transform: translateY(-25%);
    animation-timing-function: cubic-bezier(0, 0, 0.2, 1);
  }
}

@keyframes apollo-fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes apollo-fade-out {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}

@keyframes apollo-slide-in-up {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes apollo-slide-in-down {
  from {
    transform: translateY(-100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes apollo-slide-in-left {
  from {
    transform: translateX(-100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes apollo-slide-in-right {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes apollo-scale-in {
  from {
    transform: scale(0);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes apollo-scale-out {
  from {
    transform: scale(1);
    opacity: 1;
  }
  to {
    transform: scale(0);
    opacity: 0;
  }
}

/* Animation utility classes */
.apollo-animate--spin {
  animation: apollo-spin 1s linear infinite;
}

.apollo-animate--pulse {
  animation: apollo-pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.apollo-animate--bounce {
  animation: apollo-bounce 1s infinite;
}

.apollo-animate--fade-in {
  animation: apollo-fade-in var(--apollo-transition-normal) var(--apollo-easing-smooth);
}

.apollo-animate--fade-out {
  animation: apollo-fade-out var(--apollo-transition-normal) var(--apollo-easing-smooth);
}

.apollo-animate--slide-in-up {
  animation: apollo-slide-in-up var(--apollo-transition-slow) var(--apollo-easing-smooth);
}

.apollo-animate--slide-in-down {
  animation: apollo-slide-in-down var(--apollo-transition-slow) var(--apollo-easing-smooth);
}

.apollo-animate--slide-in-left {
  animation: apollo-slide-in-left var(--apollo-transition-slow) var(--apollo-easing-smooth);
}

.apollo-animate--slide-in-right {
  animation: apollo-slide-in-right var(--apollo-transition-slow) var(--apollo-easing-smooth);
}

.apollo-animate--scale-in {
  animation: apollo-scale-in var(--apollo-transition-normal) var(--apollo-easing-bounce);
}

.apollo-animate--scale-out {
  animation: apollo-scale-out var(--apollo-transition-normal) var(--apollo-easing-smooth);
}

/* Animation delay utilities */
.apollo-delay--0 {
  animation-delay: 0s;
}

.apollo-delay--75 {
  animation-delay: 75ms;
}

.apollo-delay--100 {
  animation-delay: 100ms;
}

.apollo-delay--150 {
  animation-delay: 150ms;
}

.apollo-delay--200 {
  animation-delay: 200ms;
}

.apollo-delay--300 {
  animation-delay: 300ms;
}

.apollo-delay--500 {
  animation-delay: 500ms;
}

.apollo-delay--700 {
  animation-delay: 700ms;
}

.apollo-delay--1000 {
  animation-delay: 1000ms;
}

/* Animation duration utilities */
.apollo-duration--75 {
  animation-duration: 75ms;
}

.apollo-duration--100 {
  animation-duration: 100ms;
}

.apollo-duration--150 {
  animation-duration: 150ms;
}

.apollo-duration--200 {
  animation-duration: 200ms;
}

.apollo-duration--300 {
  animation-duration: 300ms;
}

.apollo-duration--500 {
  animation-duration: 500ms;
}

.apollo-duration--700 {
  animation-duration: 700ms;
}

.apollo-duration--1000 {
  animation-duration: 1000ms;
}

/* Animation iteration utilities */
.apollo-repeat--1 {
  animation-iteration-count: 1;
}

.apollo-repeat--2 {
  animation-iteration-count: 2;
}

.apollo-repeat--3 {
  animation-iteration-count: 3;
}

.apollo-repeat--infinite {
  animation-iteration-count: infinite;
}

/* Animation direction utilities */
.apollo-direction--normal {
  animation-direction: normal;
}

.apollo-direction--reverse {
  animation-direction: reverse;
}

.apollo-direction--alternate {
  animation-direction: alternate;
}

.apollo-direction--alternate-reverse {
  animation-direction: alternate-reverse;
}

/* Animation fill mode utilities */
.apollo-fill--none {
  animation-fill-mode: none;
}

.apollo-fill--forwards {
  animation-fill-mode: forwards;
}

.apollo-fill--backwards {
  animation-fill-mode: backwards;
}

.apollo-fill--both {
  animation-fill-mode: both;
}

/* Animation play state utilities */
.apollo-play--paused {
  animation-play-state: paused;
}

.apollo-play--running {
  animation-play-state: running;
}

/* Component-specific animations */
.apollo-modal-enter {
  animation: apollo-fade-in var(--apollo-transition-normal) var(--apollo-easing-smooth),
             apollo-scale-in var(--apollo-transition-normal) var(--apollo-easing-bounce);
}

.apollo-modal-exit {
  animation: apollo-fade-out var(--apollo-transition-normal) var(--apollo-easing-smooth),
             apollo-scale-out var(--apollo-transition-normal) var(--apollo-easing-smooth);
}

.apollo-drawer-enter-left {
  animation: apollo-slide-in-left var(--apollo-transition-slow) var(--apollo-easing-smooth);
}

.apollo-drawer-enter-right {
  animation: apollo-slide-in-right var(--apollo-transition-slow) var(--apollo-easing-smooth);
}

.apollo-toast-enter {
  animation: apollo-slide-in-right var(--apollo-transition-slow) var(--apollo-easing-bounce);
}

.apollo-tooltip-enter {
  animation: apollo-fade-in var(--apollo-transition-fast) var(--apollo-easing-smooth);
}

/* Interactive element animations */
.apollo-button-press {
  transition: transform var(--apollo-transition-fast) var(--apollo-easing-smooth);
}

.apollo-button-press:active {
  transform: scale(0.98);
}

.apollo-card-hover {
  transition: transform var(--apollo-transition-normal) var(--apollo-easing-smooth),
              box-shadow var(--apollo-transition-normal) var(--apollo-easing-smooth);
}

.apollo-card-hover:hover {
  transform: translateY(-2px);
  box-shadow: var(--apollo-shadow-lg);
}

/* Accessibility and reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .apollo-transition,
  .apollo-transition--fast,
  .apollo-transition--slow,
  .apollo-transition--slower,
  .apollo-transition--colors,
  .apollo-transition--opacity,
  .apollo-transition--transform,
  .apollo-transition--size,
  .apollo-transition--shadow {
    transition-duration: 0.01ms !important;
  }
  
  .apollo-animate--spin,
  .apollo-animate--pulse,
  .apollo-animate--bounce,
  .apollo-animate--fade-in,
  .apollo-animate--fade-out,
  .apollo-animate--slide-in-up,
  .apollo-animate--slide-in-down,
  .apollo-animate--slide-in-left,
  .apollo-animate--slide-in-right,
  .apollo-animate--scale-in,
  .apollo-animate--scale-out {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
  }
  
  .apollo-hover--lift,
  .apollo-hover--lift-sm,
  .apollo-hover--lift-lg,
  .apollo-hover--scale,
  .apollo-hover--scale-sm,
  .apollo-hover--scale-lg {
    transform: none !important;
  }
}