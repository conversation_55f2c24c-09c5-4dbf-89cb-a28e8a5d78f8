/* Apollo Utilities - Centralized utility classes and mixins */

/* Import all utility modules */
@import './scrollbar.css';
@import './responsive.css';
@import './animations.css';

/* Utility classes for common patterns */

/* Layout utilities */
.apollo-layout--full-height {
  min-height: 100vh;
  min-height: 100dvh; /* Dynamic viewport height */
}

.apollo-layout--centered {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  min-height: 100dvh;
}

.apollo-layout--sidebar {
  display: flex;
  min-height: 100vh;
  min-height: 100dvh;
}

.apollo-layout--sidebar-content {
  flex: 1;
  min-width: 0; /* Prevent flex item overflow */
}

/* Visual utilities */
.apollo-visually-hidden {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.apollo-sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.apollo-not-sr-only {
  position: static;
  width: auto;
  height: auto;
  padding: 0;
  margin: 0;
  overflow: visible;
  clip: auto;
  white-space: normal;
}

/* Focus utilities */
.apollo-focus-within {
  transition: box-shadow var(--apollo-transition-fast) var(--apollo-easing-smooth);
}

.apollo-focus-within:focus-within {
  box-shadow: 0 0 0 2px var(--apollo-primary);
}

/* Truncation utilities */
.apollo-truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.apollo-truncate--2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.apollo-truncate--3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.apollo-truncate--4 {
  display: -webkit-box;
  -webkit-line-clamp: 4;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Loading state utilities */
.apollo-loading {
  pointer-events: none;
  user-select: none;
}

.apollo-loading--overlay {
  position: relative;
}

.apollo-loading--overlay::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(1px);
  z-index: 10;
}

/* Skeleton loading utilities */
.apollo-skeleton {
  background: linear-gradient(
    90deg,
    var(--apollo-bg-input) 25%,
    var(--apollo-bg-card) 50%,
    var(--apollo-bg-input) 75%
  );
  background-size: 200% 100%;
  animation: apollo-skeleton-shimmer 1.5s ease-in-out infinite;
}

@keyframes apollo-skeleton-shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

.apollo-skeleton--text {
  height: 1em;
  border-radius: var(--apollo-radius-xs);
}

.apollo-skeleton--avatar {
  width: 40px;
  height: 40px;
  border-radius: var(--apollo-radius-full);
}

.apollo-skeleton--button {
  height: 36px;
  border-radius: var(--apollo-radius-sm);
}

.apollo-skeleton--card {
  height: 200px;
  border-radius: var(--apollo-radius-lg);
}

/* Interactive utilities */
.apollo-interactive {
  cursor: pointer;
  user-select: none;
  transition: opacity var(--apollo-transition-fast) var(--apollo-easing-smooth);
}

.apollo-interactive:hover {
  opacity: 0.8;
}

.apollo-interactive:active {
  opacity: 0.6;
}

.apollo-interactive--disabled,
.apollo-interactive:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

/* Drag and drop utilities */
.apollo-draggable {
  cursor: grab;
  user-select: none;
}

.apollo-draggable:active {
  cursor: grabbing;
}

.apollo-drop-zone {
  transition: background-color var(--apollo-transition-normal) var(--apollo-easing-smooth),
              border-color var(--apollo-transition-normal) var(--apollo-easing-smooth);
}

.apollo-drop-zone--active {
  background-color: var(--apollo-primary-light);
  border-color: var(--apollo-primary);
}

/* Content utilities */
.apollo-content {
  line-height: 1.6;
  color: var(--apollo-text-primary);
}

.apollo-content h1,
.apollo-content h2,
.apollo-content h3,
.apollo-content h4,
.apollo-content h5,
.apollo-content h6 {
  margin-top: 1.5em;
  margin-bottom: 0.5em;
  font-weight: var(--apollo-font-semibold);
  line-height: 1.25;
}

.apollo-content h1:first-child,
.apollo-content h2:first-child,
.apollo-content h3:first-child,
.apollo-content h4:first-child,
.apollo-content h5:first-child,
.apollo-content h6:first-child {
  margin-top: 0;
}

.apollo-content p {
  margin-bottom: 1em;
}

.apollo-content p:last-child {
  margin-bottom: 0;
}

.apollo-content ul,
.apollo-content ol {
  margin-bottom: 1em;
  padding-left: 2em;
}

.apollo-content li {
  margin-bottom: 0.25em;
}

.apollo-content code {
  font-family: var(--apollo-font-mono);
  font-size: 0.875em;
  background: var(--apollo-bg-input);
  padding: 0.125em 0.25em;
  border-radius: var(--apollo-radius-xs);
}

.apollo-content pre {
  background: var(--apollo-bg-input);
  padding: 1em;
  border-radius: var(--apollo-radius-sm);
  overflow-x: auto;
  margin-bottom: 1em;
}

.apollo-content pre code {
  background: none;
  padding: 0;
}

.apollo-content blockquote {
  border-left: 4px solid var(--apollo-primary);
  padding-left: 1em;
  margin: 1em 0;
  font-style: italic;
  color: var(--apollo-text-secondary);
}

/* Form utilities */
.apollo-form-group {
  margin-bottom: var(--apollo-space-lg);
}

.apollo-form-label {
  display: block;
  margin-bottom: var(--apollo-space-sm);
  font-weight: var(--apollo-font-medium);
  color: var(--apollo-text-primary);
}

.apollo-form-input {
  width: 100%;
  padding: var(--apollo-space-sm) var(--apollo-space-md);
  border: 1px solid var(--apollo-border-light);
  border-radius: var(--apollo-radius-sm);
  background: var(--apollo-bg-input);
  color: var(--apollo-text-primary);
  font-size: var(--apollo-text-base);
  transition: border-color var(--apollo-transition-normal) var(--apollo-easing-smooth),
              box-shadow var(--apollo-transition-normal) var(--apollo-easing-smooth);
}

.apollo-form-input:focus {
  outline: none;
  border-color: var(--apollo-primary);
  box-shadow: 0 0 0 2px rgba(0, 123, 99, 0.25);
}

.apollo-form-input::placeholder {
  color: var(--apollo-text-muted);
}

.apollo-form-input--error {
  border-color: var(--apollo-error);
}

.apollo-form-input--error:focus {
  box-shadow: 0 0 0 2px rgba(220, 38, 38, 0.25);
}

.apollo-form-error {
  margin-top: var(--apollo-space-xs);
  font-size: var(--apollo-text-sm);
  color: var(--apollo-error);
}

.apollo-form-help {
  margin-top: var(--apollo-space-xs);
  font-size: var(--apollo-text-sm);
  color: var(--apollo-text-muted);
}

/* Card utilities */
.apollo-card {
  background: var(--apollo-bg-card);
  border: 1px solid var(--apollo-border-subtle);
  border-radius: var(--apollo-radius-lg);
  padding: var(--apollo-space-lg);
  transition: border-color var(--apollo-transition-normal) var(--apollo-easing-smooth),
              box-shadow var(--apollo-transition-normal) var(--apollo-easing-smooth);
}

.apollo-card:hover {
  border-color: var(--apollo-border-light);
  box-shadow: var(--apollo-shadow-sm);
}

.apollo-card--interactive {
  cursor: pointer;
}

.apollo-card--interactive:hover {
  border-color: var(--apollo-primary);
  box-shadow: var(--apollo-shadow-md);
}

.apollo-card--selected {
  border-color: var(--apollo-primary);
  background: var(--apollo-primary-light);
}

/* Badge utilities */
.apollo-badge {
  display: inline-flex;
  align-items: center;
  padding: var(--apollo-space-xs) var(--apollo-space-sm);
  border-radius: var(--apollo-radius-full);
  font-size: var(--apollo-text-xs);
  font-weight: var(--apollo-font-medium);
  line-height: 1;
}

.apollo-badge--primary {
  background: var(--apollo-primary-light);
  color: var(--apollo-primary);
}

.apollo-badge--secondary {
  background: var(--apollo-bg-input);
  color: var(--apollo-text-secondary);
}

.apollo-badge--success {
  background: rgba(16, 185, 129, 0.1);
  color: #10b981;
}

.apollo-badge--warning {
  background: rgba(245, 158, 11, 0.1);
  color: #f59e0b;
}

.apollo-badge--error {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
}

/* Divider utilities */
.apollo-divider {
  border: 0;
  height: 1px;
  background: var(--apollo-border-subtle);
  margin: var(--apollo-space-lg) 0;
}

.apollo-divider--vertical {
  width: 1px;
  height: auto;
  margin: 0 var(--apollo-space-lg);
}

/* Shadow utilities */
.apollo-shadow--none {
  box-shadow: none;
}

.apollo-shadow--sm {
  box-shadow: var(--apollo-shadow-sm);
}

.apollo-shadow--md {
  box-shadow: var(--apollo-shadow-md);
}

.apollo-shadow--lg {
  box-shadow: var(--apollo-shadow-lg);
}

.apollo-shadow--xl {
  box-shadow: var(--apollo-shadow-xl);
}