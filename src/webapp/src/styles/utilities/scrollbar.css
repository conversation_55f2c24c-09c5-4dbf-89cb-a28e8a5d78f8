/* Scrollbar Utilities - Standardized scrollbar styling across the application */

/* Base scrollbar styles using Apollo design tokens */
.apollo-scrollbar,
.apollo-scrollbar::-webkit-scrollbar {
  width: var(--apollo-space-sm);
  height: var(--apollo-space-sm);
}

.apollo-scrollbar::-webkit-scrollbar-track {
  background: var(--apollo-scrollbar-track);
  border-radius: var(--apollo-radius-xs);
}

.apollo-scrollbar::-webkit-scrollbar-thumb {
  background: var(--apollo-scrollbar-thumb);
  border-radius: var(--apollo-radius-xs);
  transition: background-color var(--apollo-transition-slow);
}

.apollo-scrollbar::-webkit-scrollbar-thumb:hover {
  background: var(--apollo-scrollbar-thumb-hover);
}

.apollo-scrollbar::-webkit-scrollbar-corner {
  background: var(--apollo-scrollbar-track);
}

/* Thin scrollbar variant */
.apollo-scrollbar--thin,
.apollo-scrollbar--thin::-webkit-scrollbar {
  width: var(--apollo-space-xs);
  height: var(--apollo-space-xs);
}

/* Thick scrollbar variant */
.apollo-scrollbar--thick,
.apollo-scrollbar--thick::-webkit-scrollbar {
  width: var(--apollo-space-md);
  height: var(--apollo-space-md);
}

/* Hidden scrollbar (for overlay scrollbars) */
.apollo-scrollbar--hidden {
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* Internet Explorer 10+ */
}

.apollo-scrollbar--hidden::-webkit-scrollbar {
  display: none; /* WebKit */
}

/* Overlay scrollbar (shows on hover/scroll) */
.apollo-scrollbar--overlay {
  overflow: overlay;
}

.apollo-scrollbar--overlay::-webkit-scrollbar {
  width: var(--apollo-space-sm);
  height: var(--apollo-space-sm);
  background: transparent;
}

.apollo-scrollbar--overlay::-webkit-scrollbar-track {
  background: transparent;
}

.apollo-scrollbar--overlay::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: var(--apollo-radius-xs);
  transition: background-color var(--apollo-transition-slow);
}

.apollo-scrollbar--overlay::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

/* Auto-hide scrollbar (only shows when scrolling) */
.apollo-scrollbar--auto-hide {
  overflow: auto;
  scrollbar-width: thin;
  scrollbar-color: transparent transparent;
  transition: scrollbar-color var(--apollo-transition-slow);
}

.apollo-scrollbar--auto-hide:hover,
.apollo-scrollbar--auto-hide:focus-within {
  scrollbar-color: var(--apollo-scrollbar-thumb) var(--apollo-scrollbar-track);
}

.apollo-scrollbar--auto-hide::-webkit-scrollbar {
  width: var(--apollo-space-sm);
  height: var(--apollo-space-sm);
}

.apollo-scrollbar--auto-hide::-webkit-scrollbar-track {
  background: transparent;
  transition: background-color var(--apollo-transition-slow);
}

.apollo-scrollbar--auto-hide::-webkit-scrollbar-thumb {
  background: transparent;
  border-radius: var(--apollo-radius-xs);
  transition: background-color var(--apollo-transition-slow);
}

.apollo-scrollbar--auto-hide:hover::-webkit-scrollbar-track {
  background: var(--apollo-scrollbar-track);
}

.apollo-scrollbar--auto-hide:hover::-webkit-scrollbar-thumb {
  background: var(--apollo-scrollbar-thumb);
}

.apollo-scrollbar--auto-hide:hover::-webkit-scrollbar-thumb:hover {
  background: var(--apollo-scrollbar-thumb-hover);
}

/* Content-specific scrollbar styles */
.apollo-scrollbar--content {
  overflow-y: auto;
  overflow-x: hidden;
}

.apollo-scrollbar--content::-webkit-scrollbar {
  width: var(--apollo-space-sm);
}

.apollo-scrollbar--content::-webkit-scrollbar-track {
  background: var(--apollo-bg-primary);
  border-radius: var(--apollo-radius-xs);
}

.apollo-scrollbar--content::-webkit-scrollbar-thumb {
  background: var(--apollo-border-light);
  border-radius: var(--apollo-radius-xs);
}

.apollo-scrollbar--content::-webkit-scrollbar-thumb:hover {
  background: var(--apollo-border-medium);
}

/* Modal/dialog scrollbar styles */
.apollo-scrollbar--modal {
  overflow-y: auto;
  max-height: 80vh;
}

.apollo-scrollbar--modal::-webkit-scrollbar {
  width: var(--apollo-space-xs);
}

.apollo-scrollbar--modal::-webkit-scrollbar-track {
  background: var(--apollo-bg-input);
  border-radius: var(--apollo-radius-xs);
}

.apollo-scrollbar--modal::-webkit-scrollbar-thumb {
  background: var(--apollo-border-subtle);
  border-radius: var(--apollo-radius-xs);
}

.apollo-scrollbar--modal::-webkit-scrollbar-thumb:hover {
  background: var(--apollo-border-light);
}

/* Code/pre scrollbar styles */
.apollo-scrollbar--code {
  overflow: auto;
  font-family: var(--apollo-font-mono);
}

.apollo-scrollbar--code::-webkit-scrollbar {
  width: var(--apollo-space-xs);
  height: var(--apollo-space-xs);
}

.apollo-scrollbar--code::-webkit-scrollbar-track {
  background: var(--apollo-bg-tertiary);
  border-radius: var(--apollo-radius-xs);
}

.apollo-scrollbar--code::-webkit-scrollbar-thumb {
  background: var(--apollo-text-muted);
  border-radius: var(--apollo-radius-xs);
}

.apollo-scrollbar--code::-webkit-scrollbar-thumb:hover {
  background: var(--apollo-text-secondary);
}

/* Utility classes for common scenarios */
.scroll-smooth {
  scroll-behavior: smooth;
}

.scroll-auto {
  scroll-behavior: auto;
}

.overscroll-contain {
  overscroll-behavior: contain;
}

.overscroll-none {
  overscroll-behavior: none;
}

/* Responsive scrollbar utilities */
@media (max-width: 768px) {
  .apollo-scrollbar--mobile-hidden,
  .apollo-scrollbar--mobile-hidden::-webkit-scrollbar {
    display: none;
  }
  
  .apollo-scrollbar--mobile-thin,
  .apollo-scrollbar--mobile-thin::-webkit-scrollbar {
    width: var(--apollo-space-xs);
    height: var(--apollo-space-xs);
  }
}

/* Firefox fallback styles */
@supports (scrollbar-width: thin) {
  .apollo-scrollbar {
    scrollbar-width: thin;
    scrollbar-color: var(--apollo-scrollbar-thumb) var(--apollo-scrollbar-track);
  }
  
  .apollo-scrollbar--thin {
    scrollbar-width: thin;
  }
  
  .apollo-scrollbar--hidden {
    scrollbar-width: none;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .apollo-scrollbar::-webkit-scrollbar-thumb {
    background: var(--apollo-text-primary);
    border: 1px solid var(--apollo-bg-primary);
  }
  
  .apollo-scrollbar::-webkit-scrollbar-track {
    background: var(--apollo-bg-secondary);
    border: 1px solid var(--apollo-text-primary);
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .apollo-scrollbar::-webkit-scrollbar-thumb {
    transition: none;
  }
  
  .apollo-scrollbar--auto-hide::-webkit-scrollbar-track,
  .apollo-scrollbar--auto-hide::-webkit-scrollbar-thumb {
    transition: none;
  }
}