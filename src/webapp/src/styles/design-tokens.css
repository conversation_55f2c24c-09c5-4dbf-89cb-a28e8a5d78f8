/* Apollo Prompt Library Design Tokens */

:root {
  /* === PRIMARY COLORS === */
  --apollo-primary: #007B63;
  --apollo-primary-hover: #006653;
  --apollo-primary-light: rgba(0, 123, 99, 0.1);
  --apollo-primary-border: rgba(0, 123, 99, 0.3);
  --apollo-primary-shadow: rgba(0, 123, 99, 0.2);
  --apollo-primary-glow: rgba(0, 123, 99, 0.25);

  /* === BACKGROUND COLORS === */
  --apollo-bg-primary: #1F2025;
  --apollo-bg-secondary: #282A2C;
  --apollo-bg-tertiary: #2F3135;
  --apollo-bg-card: #282A2C;
  --apollo-bg-card-hover: #333538;
  --apollo-bg-input: #282A2C;

  /* === TEXT COLORS === */
  --apollo-text-primary: #e0e0e0;
  --apollo-text-secondary: #b0b0b0;
  --apollo-text-tertiary: #808080;
  --apollo-text-muted: #606060;
  --apollo-text-white: #ffffff;

  /* === BORDER COLORS === */
  --apollo-border-subtle: rgba(255, 255, 255, 0.05);
  --apollo-border-light: rgba(255, 255, 255, 0.1);
  --apollo-border-medium: rgba(255, 255, 255, 0.2);

  /* === STATUS COLORS === */
  --apollo-success: #10b981;
  --apollo-success-bg: rgba(16, 185, 129, 0.1);
  --apollo-warning: #f59e0b;
  --apollo-warning-bg: rgba(245, 158, 11, 0.1);
  --apollo-error: #ff6b6b;
  --apollo-error-hover: #ff5252;
  --apollo-error-bg: rgba(255, 107, 107, 0.1);
  --apollo-info: #3b82f6;
  --apollo-info-bg: rgba(59, 130, 246, 0.1);
  --apollo-purple: #8b5cf6;
  --apollo-purple-bg: rgba(139, 92, 246, 0.1);
  --apollo-neutral: #9ca3af;
  --apollo-neutral-bg: rgba(156, 163, 175, 0.1);

  /* === SPACING SCALE === */
  --apollo-space-xs: 4px;
  --apollo-space-sm: 8px;
  --apollo-space-md: 12px;
  --apollo-space-lg: 16px;
  --apollo-space-xl: 20px;
  --apollo-space-2xl: 24px;
  --apollo-space-3xl: 32px;
  --apollo-space-4xl: 40px;
  --apollo-space-5xl: 48px;
  --apollo-space-6xl: 64px;

  /* === BORDER RADIUS === */
  --apollo-radius-xs: 4px;
  --apollo-radius-sm: 6px;
  --apollo-radius-md: 8px;
  --apollo-radius-lg: 12px;

  /* === FONT SIZES === */
  --apollo-text-xs: 11px;
  --apollo-text-sm: 12px;
  --apollo-text-base: 14px;
  --apollo-text-lg: 16px;
  --apollo-text-xl: 18px;
  --apollo-text-2xl: 24px;

  /* === FONT WEIGHTS === */
  --apollo-font-normal: 400;
  --apollo-font-medium: 500;
  --apollo-font-semibold: 600;

  /* === LINE HEIGHTS === */
  --apollo-leading-tight: 1.2;
  --apollo-leading-snug: 1.3;
  --apollo-leading-normal: 1.4;
  --apollo-leading-relaxed: 1.5;

  /* === FONT FAMILIES === */
  --apollo-font-ui: 'Segoe UI', -apple-system, BlinkMacSystemFont, sans-serif;
  --apollo-font-mono: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;

  /* === SHADOWS === */
  --apollo-shadow-sm: 0 2px 8px rgba(0, 123, 99, 0.3);
  --apollo-shadow-md: 0 4px 8px rgba(0, 123, 99, 0.2);
  --apollo-shadow-lg: 0 4px 12px rgba(0, 0, 0, 0.3);
  --apollo-shadow-xl: 0 8px 24px rgba(0, 0, 0, 0.4);
  --apollo-shadow-focus: 0 0 0 2px var(--apollo-primary-glow);

  /* === SCROLLBAR COLORS === */
  --apollo-scrollbar-track: var(--apollo-bg-primary);
  --apollo-scrollbar-thumb: #4a4a4a;
  --apollo-scrollbar-thumb-hover: #5a5a5a;

  /* === TRANSITIONS === */
  --apollo-transition-fast: 0.1s ease;
  --apollo-transition-normal: 0.2s ease;
  --apollo-transition-slow: 0.3s ease;

  /* === Z-INDEX SCALE === */
  --apollo-z-dropdown: 1000;
  --apollo-z-overlay: 998;
  --apollo-z-modal: 999;

  /* === WIDTHS === */
  --apollo-sidebar-icon-width: 80px;
  --apollo-sidebar-main-width: 280px;
  --apollo-sidebar-icon-width-tablet: 60px;
  --apollo-sidebar-main-width-tablet: 240px;
  --apollo-sidebar-mobile-width: 360px;

  /* === COMPONENT-SPECIFIC TOKENS === */
  /* Vote Button */
  --apollo-vote-bg: rgba(255, 255, 255, 0.05);
  --apollo-vote-border: rgba(255, 255, 255, 0.1);
  --apollo-vote-hover-bg: var(--apollo-primary-light);
  --apollo-vote-hover-border: var(--apollo-primary-border);
  --apollo-vote-voted-bg: rgba(0, 123, 99, 0.15);

  /* Input Focus */
  --apollo-input-focus-border: var(--apollo-primary);
  --apollo-input-focus-shadow: var(--apollo-shadow-focus);

  /* Category Badge */
  --apollo-badge-bg: rgba(255, 255, 255, 0.05);
  --apollo-badge-border: rgba(255, 255, 255, 0.1);
  --apollo-badge-text: var(--apollo-text-secondary);

  /* Icon Colors */
  --apollo-icon-primary: var(--apollo-text-tertiary);
  --apollo-icon-hover: var(--apollo-text-primary);
  --apollo-icon-active: var(--apollo-primary);

  /* Mobile Menu */
  --apollo-mobile-menu-bg: var(--apollo-bg-secondary);
  --apollo-mobile-menu-border: var(--apollo-border-light);
}

/* === SEMANTIC COLOR MAPPING === */
:root {
  /* Primary Actions */
  --color-primary: var(--apollo-primary);
  --color-primary-hover: var(--apollo-primary-hover);
  --color-primary-light: var(--apollo-primary-light);
  
  /* Backgrounds */
  --bg-primary: var(--apollo-bg-primary);
  --bg-secondary: var(--apollo-bg-secondary);
  --bg-card: var(--apollo-bg-card);
  --bg-card-hover: var(--apollo-bg-card-hover);
  
  /* Text */
  --text-primary: var(--apollo-text-primary);
  --text-secondary: var(--apollo-text-secondary);
  --text-muted: var(--apollo-text-muted);
  
  /* Borders */
  --border-subtle: var(--apollo-border-subtle);
  --border-light: var(--apollo-border-light);
  
  /* Spacing shortcuts */
  --space-xs: var(--apollo-space-xs);
  --space-sm: var(--apollo-space-sm);
  --space-md: var(--apollo-space-md);
  --space-lg: var(--apollo-space-lg);
  --space-xl: var(--apollo-space-xl);
  --space-2xl: var(--apollo-space-2xl);
  --space-3xl: var(--apollo-space-3xl);
}