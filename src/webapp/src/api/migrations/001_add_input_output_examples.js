import { db } from '../database.js';

/**
 * Migration: Add inputOutputExamples field to prompts table
 * 
 * This migration adds a new column to store input/output examples for prompts.
 * The field stores JSON data as TEXT with validation.
 */

export function up() {
  console.log('Running migration: Add inputOutputExamples field...');
  
  try {
    // Check if column already exists
    const tableInfo = db.prepare("PRAGMA table_info(prompts)").all();
    const columnExists = tableInfo.some(col => col.name === 'input_output_examples');
    
    if (columnExists) {
      console.log('Column input_output_examples already exists, skipping column creation...');
    } else {
      // Add the new column to the prompts table
      db.exec(`
        ALTER TABLE prompts ADD COLUMN input_output_examples TEXT DEFAULT '[]';
      `);
      console.log('Added input_output_examples column to prompts table');
    }
    
    // Create a trigger to validate JSON format
    db.exec(`
      CREATE TRIGGER IF NOT EXISTS validate_input_output_examples
      BEFORE INSERT ON prompts
      FOR EACH ROW
      WHEN NEW.input_output_examples IS NOT NULL AND NEW.input_output_examples != '[]'
      BEGIN
        SELECT CASE
          WHEN json_valid(NEW.input_output_examples) = 0 THEN
            RAISE(ABORT, 'input_output_examples must be valid JSON')
        END;
      END;
    `);
    
    db.exec(`
      CREATE TRIGGER IF NOT EXISTS validate_input_output_examples_update
      BEFORE UPDATE ON prompts
      FOR EACH ROW
      WHEN NEW.input_output_examples IS NOT NULL AND NEW.input_output_examples != '[]'
      BEGIN
        SELECT CASE
          WHEN json_valid(NEW.input_output_examples) = 0 THEN
            RAISE(ABORT, 'input_output_examples must be valid JSON')
        END;
      END;
    `);
    
    console.log('Successfully added inputOutputExamples field to prompts table');
    return true;
    
  } catch (error) {
    console.error('Migration failed:', error.message);
    throw error;
  }
}

export function down() {
  console.log('Rolling back migration: Remove inputOutputExamples field...');
  
  try {
    // Drop the validation triggers
    db.exec(`DROP TRIGGER IF EXISTS validate_input_output_examples;`);
    db.exec(`DROP TRIGGER IF EXISTS validate_input_output_examples_update;`);
    
    // SQLite doesn't support dropping columns directly, so we need to recreate the table
    // First, create a backup table
    db.exec(`
      CREATE TABLE prompts_backup AS 
      SELECT id, title, description, content, category_id, author_id, status, difficulty, votes, comments, created_at, updated_at
      FROM prompts;
    `);
    
    // Drop the original table
    db.exec(`DROP TABLE prompts;`);
    
    // Recreate the original table without the new column
    db.exec(`
      CREATE TABLE prompts (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        title TEXT NOT NULL,
        description TEXT NOT NULL,
        content TEXT NOT NULL,
        category_id INTEGER NOT NULL,
        author_id INTEGER NOT NULL,
        status TEXT NOT NULL DEFAULT 'In Development',
        difficulty TEXT NOT NULL DEFAULT 'Intermediate',
        votes INTEGER DEFAULT 0,
        comments INTEGER DEFAULT 0,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (category_id) REFERENCES categories(id),
        FOREIGN KEY (author_id) REFERENCES users(id)
      );
    `);
    
    // Restore the data
    db.exec(`
      INSERT INTO prompts (id, title, description, content, category_id, author_id, status, difficulty, votes, comments, created_at, updated_at)
      SELECT id, title, description, content, category_id, author_id, status, difficulty, votes, comments, created_at, updated_at
      FROM prompts_backup;
    `);
    
    // Drop the backup table
    db.exec(`DROP TABLE prompts_backup;`);
    
    // Recreate indexes
    db.exec(`
      CREATE INDEX IF NOT EXISTS idx_prompts_category ON prompts(category_id);
      CREATE INDEX IF NOT EXISTS idx_prompts_author ON prompts(author_id);
      CREATE INDEX IF NOT EXISTS idx_prompts_status ON prompts(status);
    `);
    
    console.log('Successfully rolled back inputOutputExamples field migration');
    return true;
    
  } catch (error) {
    console.error('Rollback failed:', error.message);
    throw error;
  }
}

// Helper function to validate inputOutputExamples structure
export function validateInputOutputExamples(examples) {
  if (!Array.isArray(examples)) {
    throw new Error('inputOutputExamples must be an array');
  }
  
  for (const example of examples) {
    if (typeof example !== 'object' || example === null) {
      throw new Error('Each example must be an object');
    }
    
    if (typeof example.input !== 'string') {
      throw new Error('Each example must have an input field of type string');
    }
    
    if (typeof example.output !== 'string') {
      throw new Error('Each example must have an output field of type string');
    }
    
    // Optional: check for additional allowed fields
    const allowedFields = ['input', 'output'];
    const extraFields = Object.keys(example).filter(key => !allowedFields.includes(key));
    if (extraFields.length > 0) {
      console.warn(`Example contains unexpected fields: ${extraFields.join(', ')}`);
    }
  }
  
  return true;
}

// Run migration if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  try {
    up();
    console.log('Migration completed successfully');
    process.exit(0);
  } catch (error) {
    console.error('Migration failed:', error);
    process.exit(1);
  }
}