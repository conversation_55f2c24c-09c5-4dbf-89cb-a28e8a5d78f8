// Browser-compatible API client that falls back to mock data
import { mockPrompts, categories as mockCategories, statuses } from '../mockData';

// Simple in-memory storage for demo purposes
let sessionData = {
  users: [
    { 
      id: 1, 
      username: 'alexf', 
      email: '<EMAIL>', 
      fullName: '<PERSON>', 
      role: 'admin',
      permissions: ['read', 'write', 'delete', 'admin', 'user_management', 'system_settings']
    },
    { 
      id: 2, 
      username: 'peteam', 
      email: '<EMAIL>', 
      fullName: 'PE Team', 
      role: 'editor',
      permissions: ['read', 'write', 'delete']
    },
    { 
      id: 3, 
      username: 'quantteam', 
      email: '<EMAIL>', 
      fullName: 'Quantitative Team', 
      role: 'editor',
      permissions: ['read', 'write', 'delete']
    },
    { 
      id: 4, 
      username: 'research', 
      email: '<EMAIL>', 
      fullName: 'Research Team', 
      role: 'user',
      permissions: ['read', 'write']
    }
  ],
  currentUser: null,
  sessionToken: null,
  prompts: [...mockPrompts] // Create a copy we can modify
};

// Helper function to simulate API delay
const delay = (ms = 100) => new Promise(resolve => setTimeout(resolve, ms));

// Permission checking helper
const hasPermission = (permission) => {
  if (!sessionData.currentUser) return false;
  return sessionData.currentUser.permissions.includes(permission);
};

// Authentication API
export const authAPI = {
  async login(username, password = 'mock-password') {
    await delay();
    
    const user = sessionData.users.find(u => u.username === username);
    if (!user) {
      throw new Error('Invalid username');
    }
    
    // Generate simple session token
    const sessionToken = `session_${Date.now()}_${Math.random().toString(36).substring(2)}`;
    
    sessionData.currentUser = user;
    sessionData.sessionToken = sessionToken;
    
    return {
      user: {
        id: user.id,
        username: user.username,
        email: user.email,
        fullName: user.fullName,
        role: user.role,
        permissions: user.permissions
      },
      sessionToken
    };
  },

  async logout(sessionToken) {
    await delay();
    sessionData.currentUser = null;
    sessionData.sessionToken = null;
    return { success: true };
  },

  async getCurrentUser(sessionToken) {
    await delay();
    
    if (!sessionToken || sessionToken !== sessionData.sessionToken) {
      return null;
    }
    
    return sessionData.currentUser;
  }
};

// Prompts API
export const promptsAPI = {
  async getAll(filters = {}) {
    await delay();
    
    let prompts = [...sessionData.prompts];
    
    // Apply filters
    if (filters.status) {
      prompts = prompts.filter(p => p.status.toLowerCase().includes(filters.status.toLowerCase()));
    }
    
    if (filters.category) {
      prompts = prompts.filter(p => p.category.toLowerCase().includes(filters.category.toLowerCase()));
    }
    
    if (filters.search) {
      const searchTerm = filters.search.toLowerCase();
      prompts = prompts.filter(p => 
        p.title.toLowerCase().includes(searchTerm) ||
        p.description.toLowerCase().includes(searchTerm) ||
        (p.tags && p.tags.some(tag => tag.toLowerCase().includes(searchTerm)))
      );
    }
    
    // Sort by votes, created date, etc.
    if (filters.sortBy === 'votes') {
      prompts.sort((a, b) => b.votes - a.votes);
    } else if (filters.sortBy === 'recent') {
      prompts.sort((a, b) => new Date(b.timeAgo || 0) - new Date(a.timeAgo || 0));
    } else {
      // Default: trending (combination of votes and recency)
      prompts.sort((a, b) => {
        const aScore = a.votes + Math.random(); // Add some randomness for demo
        const bScore = b.votes + Math.random();
        return bScore - aScore;
      });
    }
    
    return prompts;
  },

  async getById(id) {
    await delay();
    
    const prompt = sessionData.prompts.find(p => p.id === parseInt(id));
    if (!prompt) {
      throw new Error('Prompt not found');
    }
    
    return prompt;
  },

  async create(promptData, userId) {
    await delay();
    
    if (!hasPermission('write')) {
      throw new Error('Insufficient permissions to create prompts');
    }
    
    const newPrompt = {
      id: Math.max(...sessionData.prompts.map(p => p.id)) + 1,
      title: promptData.title,
      description: promptData.description,
      content: promptData.content || promptData.description,
      category: promptData.category,
      tags: promptData.tags || [],
      status: promptData.status || 'In Development',
      votes: 0,
      author: sessionData.currentUser?.fullName || 'Unknown',
      timeAgo: '0 minutes ago',
      statusCount: 1,
      difficulty: promptData.difficulty || 'Intermediate',
      comments: 0,
      userVote: null,
      createdBy: userId
    };
    
    sessionData.prompts.push(newPrompt);
    return newPrompt;
  },

  async update(id, promptData, userId) {
    await delay();
    
    if (!hasPermission('write')) {
      throw new Error('Insufficient permissions to update prompts');
    }
    
    const prompt = sessionData.prompts.find(p => p.id === parseInt(id));
    if (!prompt) {
      throw new Error('Prompt not found');
    }
    
    // Only allow editing own prompts unless admin
    if (prompt.createdBy !== userId && !hasPermission('admin')) {
      throw new Error('Insufficient permissions to edit this prompt');
    }
    
    const promptIndex = sessionData.prompts.findIndex(p => p.id === parseInt(id));
    sessionData.prompts[promptIndex] = {
      ...sessionData.prompts[promptIndex],
      title: promptData.title,
      description: promptData.description,
      content: promptData.content,
      category: promptData.category,
      tags: promptData.tags || [],
      status: promptData.status,
      difficulty: promptData.difficulty
    };
    
    return sessionData.prompts[promptIndex];
  },

  async delete(id, userId) {
    await delay();
    
    if (!hasPermission('delete')) {
      throw new Error('Insufficient permissions to delete prompts');
    }
    
    const prompt = sessionData.prompts.find(p => p.id === parseInt(id));
    if (!prompt) {
      throw new Error('Prompt not found');
    }
    
    // Only allow deleting own prompts unless admin
    if (prompt.createdBy !== userId && !hasPermission('admin')) {
      throw new Error('Insufficient permissions to delete this prompt');
    }
    
    const promptIndex = sessionData.prompts.findIndex(p => p.id === parseInt(id));
    sessionData.prompts.splice(promptIndex, 1);
    return { success: true };
  },

  async vote(promptId, userId, voteType = 'toggle') {
    await delay();
    
    const prompt = sessionData.prompts.find(p => p.id === parseInt(promptId));
    if (!prompt) {
      throw new Error('Prompt not found');
    }
    
    if (voteType === 'toggle') {
      if (prompt.userVote) {
        // Remove vote
        prompt.votes -= 1;
        prompt.userVote = null;
      } else {
        // Add upvote
        prompt.votes += 1;
        prompt.userVote = 'up';
      }
    }
    
    return prompt;
  }
};

// Categories API
export const categoriesAPI = {
  async getAll() {
    await delay();
    
    // Count prompts per category
    const categoriesWithCounts = mockCategories.map(category => {
      const count = sessionData.prompts.filter(p => p.category === category.name).length;
      return {
        name: category.name,
        count: count
      };
    });
    
    return categoriesWithCounts.sort((a, b) => b.count - a.count);
  }
};

// Mock migration function
export const migrateJsonToDatabase = async () => {
  console.log('Using in-memory data store (browser environment)');
  // No-op in browser environment
  return Promise.resolve();
};