import Database from 'better-sqlite3';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Database file path
const dbPath = join(__dirname, '..', '..', '..', 'apollo_prompts.db');

// Initialize database
export const db = new Database(dbPath);

// Enable foreign keys
db.pragma('foreign_keys = ON');

// Create tables
export function initializeDatabase() {
  // Users table
  db.exec(`
    CREATE TABLE IF NOT EXISTS users (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      username TEXT UNIQUE NOT NULL,
      email TEXT UNIQUE NOT NULL,
      full_name TEXT NOT NULL,
      role TEXT NOT NULL DEFAULT 'user',
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    );
  `);

  // Categories table
  db.exec(`
    CREATE TABLE IF NOT EXISTS categories (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      name TEXT UNIQUE NOT NULL,
      description TEXT,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP
    );
  `);

  // Prompts table
  db.exec(`
    CREATE TABLE IF NOT EXISTS prompts (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      title TEXT NOT NULL,
      description TEXT NOT NULL,
      content TEXT NOT NULL,
      category_id INTEGER NOT NULL,
      author_id INTEGER NOT NULL,
      status TEXT NOT NULL DEFAULT 'In Development',
      difficulty TEXT NOT NULL DEFAULT 'Intermediate',
      votes INTEGER DEFAULT 0,
      comments INTEGER DEFAULT 0,
      input_output_examples TEXT DEFAULT '[]',
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (category_id) REFERENCES categories(id),
      FOREIGN KEY (author_id) REFERENCES users(id)
    );
  `);

  // Tags table
  db.exec(`
    CREATE TABLE IF NOT EXISTS tags (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      name TEXT UNIQUE NOT NULL,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP
    );
  `);

  // Prompt tags junction table
  db.exec(`
    CREATE TABLE IF NOT EXISTS prompt_tags (
      prompt_id INTEGER,
      tag_id INTEGER,
      PRIMARY KEY (prompt_id, tag_id),
      FOREIGN KEY (prompt_id) REFERENCES prompts(id) ON DELETE CASCADE,
      FOREIGN KEY (tag_id) REFERENCES tags(id) ON DELETE CASCADE
    );
  `);

  // Votes table
  db.exec(`
    CREATE TABLE IF NOT EXISTS votes (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      user_id INTEGER NOT NULL,
      prompt_id INTEGER NOT NULL,
      vote_type TEXT NOT NULL CHECK (vote_type IN ('up', 'down')),
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      UNIQUE(user_id, prompt_id),
      FOREIGN KEY (user_id) REFERENCES users(id),
      FOREIGN KEY (prompt_id) REFERENCES prompts(id) ON DELETE CASCADE
    );
  `);

  // User sessions table
  db.exec(`
    CREATE TABLE IF NOT EXISTS user_sessions (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      user_id INTEGER NOT NULL,
      session_token TEXT UNIQUE NOT NULL,
      expires_at DATETIME NOT NULL,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
    );
  `);

  // Create indexes for performance
  db.exec(`
    CREATE INDEX IF NOT EXISTS idx_prompts_category ON prompts(category_id);
    CREATE INDEX IF NOT EXISTS idx_prompts_author ON prompts(author_id);
    CREATE INDEX IF NOT EXISTS idx_prompts_status ON prompts(status);
    CREATE INDEX IF NOT EXISTS idx_votes_prompt ON votes(prompt_id);
    CREATE INDEX IF NOT EXISTS idx_votes_user ON votes(user_id);
    CREATE INDEX IF NOT EXISTS idx_sessions_token ON user_sessions(session_token);
    CREATE INDEX IF NOT EXISTS idx_sessions_expires ON user_sessions(expires_at);
  `);

  console.log('Database initialized successfully');
}

// Seed initial data
export function seedDatabase() {
  // Check if data already exists
  const userCount = db.prepare('SELECT COUNT(*) as count FROM users').get().count;
  if (userCount > 0) {
    console.log('Database already seeded, skipping...');
    return;
  }

  // Create sample users
  const insertUser = db.prepare(`
    INSERT INTO users (username, email, full_name, role)
    VALUES (?, ?, ?, ?)
  `);

  insertUser.run('alexf', '<EMAIL>', 'Alex Foster', 'admin');
  insertUser.run('peteam', '<EMAIL>', 'PE Team', 'user');
  insertUser.run('quantteam', '<EMAIL>', 'Quantitative Team', 'user');
  insertUser.run('research', '<EMAIL>', 'Research Team', 'user');

  // Create categories
  const insertCategory = db.prepare(`
    INSERT INTO categories (name, description)
    VALUES (?, ?)
  `);

  insertCategory.run('Investment Analysis', 'Investment thesis and market analysis prompts');
  insertCategory.run('Due Diligence', 'Due diligence and risk assessment prompts');
  insertCategory.run('Financial Analysis', 'Financial modeling and analysis prompts');
  insertCategory.run('Market Research', 'Market research and competitive analysis prompts');
  insertCategory.run('Value Creation', 'Value creation and operational improvement prompts');
  insertCategory.run('ESG Analysis', 'Environmental, social, and governance prompts');
  insertCategory.run('Risk Analysis', 'Risk assessment and mitigation prompts');
  insertCategory.run('Technology', 'Technology assessment and digital transformation prompts');
  insertCategory.run('Operations', 'Operational efficiency and process optimization prompts');
  insertCategory.run('Financing', 'Debt financing and capital structure prompts');
  insertCategory.run('Marketing', 'Customer acquisition and marketing strategy prompts');
  insertCategory.run('Strategic Analysis', 'Strategic planning and market entry prompts');
  insertCategory.run('Benchmarking', 'Performance benchmarking and comparison prompts');
  insertCategory.run('Growth Strategy', 'Revenue growth and expansion strategy prompts');
  insertCategory.run('Legal', 'Legal due diligence and compliance prompts');
  insertCategory.run('Cost Management', 'Cost reduction and efficiency optimization prompts');
  insertCategory.run('M&A', 'Mergers and acquisitions analysis prompts');
  insertCategory.run('Portfolio Management', 'Portfolio company management and improvement prompts');
  insertCategory.run('Procurement', 'Vendor management and procurement optimization prompts');
  insertCategory.run('Reporting', 'Board reporting and investment committee presentation prompts');

  console.log('Database seeded successfully');
}

// Helper functions for database operations
// Note: These will be initialized after table creation
export let dbHelpers = {};

function initializeHelpers() {
  dbHelpers = {
    // Get user by username
    getUserByUsername: db.prepare('SELECT * FROM users WHERE username = ?'),
  
  // Get user by session token
  getUserBySessionToken: db.prepare(`
    SELECT u.* FROM users u
    JOIN user_sessions s ON u.id = s.user_id
    WHERE s.session_token = ? AND s.expires_at > CURRENT_TIMESTAMP
  `),
  
  // Create session
  createSession: db.prepare(`
    INSERT INTO user_sessions (user_id, session_token, expires_at)
    VALUES (?, ?, datetime('now', '+7 days'))
  `),
  
  // Delete session
  deleteSession: db.prepare('DELETE FROM user_sessions WHERE session_token = ?'),
  
  // Get all categories
  getCategories: db.prepare('SELECT * FROM categories ORDER BY name'),
  
  // Get category by name
  getCategoryByName: db.prepare('SELECT * FROM categories WHERE name = ?'),
  
  // Get all tags
  getTags: db.prepare('SELECT * FROM tags ORDER BY name'),
  
  // Get or create tag
  getOrCreateTag: db.prepare('INSERT OR IGNORE INTO tags (name) VALUES (?)'),
  getTagByName: db.prepare('SELECT * FROM tags WHERE name = ?'),
  
  // Get prompts with filters
  getPromptsWithFilters: db.prepare(`
    SELECT 
      p.*,
      c.name as category_name,
      u.full_name as author_name,
      GROUP_CONCAT(t.name) as tags
    FROM prompts p
    JOIN categories c ON p.category_id = c.id
    JOIN users u ON p.author_id = u.id
    LEFT JOIN prompt_tags pt ON p.id = pt.prompt_id
    LEFT JOIN tags t ON pt.tag_id = t.id
    GROUP BY p.id
    ORDER BY p.created_at DESC
  `),
  
  // Get single prompt
  getPromptById: db.prepare(`
    SELECT 
      p.*,
      c.name as category_name,
      u.full_name as author_name,
      GROUP_CONCAT(t.name) as tags
    FROM prompts p
    JOIN categories c ON p.category_id = c.id
    JOIN users u ON p.author_id = u.id
    LEFT JOIN prompt_tags pt ON p.id = pt.prompt_id
    LEFT JOIN tags t ON pt.tag_id = t.id
    WHERE p.id = ?
    GROUP BY p.id
  `),
  
  // Create prompt
  createPrompt: db.prepare(`
    INSERT INTO prompts (title, description, content, category_id, author_id, status, difficulty, input_output_examples)
    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
  `),
  
  // Update prompt
  updatePrompt: db.prepare(`
    UPDATE prompts 
    SET title = ?, description = ?, content = ?, category_id = ?, status = ?, difficulty = ?, input_output_examples = ?, updated_at = CURRENT_TIMESTAMP
    WHERE id = ?
  `),
  
  // Delete prompt
  deletePrompt: db.prepare('DELETE FROM prompts WHERE id = ?'),
  
  // Vote operations
  getUserVote: db.prepare('SELECT * FROM votes WHERE user_id = ? AND prompt_id = ?'),
  createVote: db.prepare('INSERT INTO votes (user_id, prompt_id, vote_type) VALUES (?, ?, ?)'),
  updateVote: db.prepare('UPDATE votes SET vote_type = ? WHERE user_id = ? AND prompt_id = ?'),
  deleteVote: db.prepare('DELETE FROM votes WHERE user_id = ? AND prompt_id = ?'),
  
  // Update prompt vote count
  updatePromptVoteCount: db.prepare(`
    UPDATE prompts 
    SET votes = (
      SELECT COUNT(*) FROM votes WHERE prompt_id = ? AND vote_type = 'up'
    ) - (
      SELECT COUNT(*) FROM votes WHERE prompt_id = ? AND vote_type = 'down'
    )
    WHERE id = ?
  `),
  
  // Link tags to prompt
  linkPromptTag: db.prepare('INSERT OR IGNORE INTO prompt_tags (prompt_id, tag_id) VALUES (?, ?)'),
  clearPromptTags: db.prepare('DELETE FROM prompt_tags WHERE prompt_id = ?'),
  
  // Input/Output examples helpers
  updatePromptExamples: db.prepare(`
    UPDATE prompts 
    SET input_output_examples = ?, updated_at = CURRENT_TIMESTAMP
    WHERE id = ?
  `),
  
  getPromptExamples: db.prepare('SELECT input_output_examples FROM prompts WHERE id = ?')
  };
}

// Utility functions for input/output examples
export const inputOutputHelpers = {
  // Validate examples structure
  validateExamples(examples) {
    if (!Array.isArray(examples)) {
      throw new Error('inputOutputExamples must be an array');
    }
    
    for (const [index, example] of examples.entries()) {
      if (typeof example !== 'object' || example === null) {
        throw new Error(`Example at index ${index} must be an object`);
      }
      
      if (typeof example.input !== 'string') {
        throw new Error(`Example at index ${index} must have an input field of type string`);
      }
      
      if (typeof example.output !== 'string') {
        throw new Error(`Example at index ${index} must have an output field of type string`);
      }
      
      // Validate input and output are not empty
      if (example.input.trim().length === 0) {
        throw new Error(`Example at index ${index} has empty input`);
      }
      
      if (example.output.trim().length === 0) {
        throw new Error(`Example at index ${index} has empty output`);
      }
    }
    
    return true;
  },
  
  // Serialize examples to JSON string
  serializeExamples(examples) {
    this.validateExamples(examples);
    return JSON.stringify(examples);
  },
  
  // Parse examples from JSON string
  parseExamples(jsonString) {
    if (!jsonString || jsonString === '[]') {
      return [];
    }
    
    try {
      const examples = JSON.parse(jsonString);
      this.validateExamples(examples);
      return examples;
    } catch (error) {
      throw new Error(`Invalid input/output examples JSON: ${error.message}`);
    }
  },
  
  // Add example to existing examples
  addExample(promptId, input, output) {
    const currentExamples = dbHelpers.getPromptExamples.get(promptId);
    const examples = this.parseExamples(currentExamples?.input_output_examples || '[]');
    
    examples.push({ input: input.trim(), output: output.trim() });
    
    const serialized = this.serializeExamples(examples);
    dbHelpers.updatePromptExamples.run(serialized, promptId);
    
    return examples;
  },
  
  // Remove example at specific index
  removeExample(promptId, index) {
    const currentExamples = dbHelpers.getPromptExamples.get(promptId);
    const examples = this.parseExamples(currentExamples?.input_output_examples || '[]');
    
    if (index < 0 || index >= examples.length) {
      throw new Error(`Invalid example index: ${index}`);
    }
    
    examples.splice(index, 1);
    
    const serialized = this.serializeExamples(examples);
    dbHelpers.updatePromptExamples.run(serialized, promptId);
    
    return examples;
  },
  
  // Update example at specific index
  updateExample(promptId, index, input, output) {
    const currentExamples = dbHelpers.getPromptExamples.get(promptId);
    const examples = this.parseExamples(currentExamples?.input_output_examples || '[]');
    
    if (index < 0 || index >= examples.length) {
      throw new Error(`Invalid example index: ${index}`);
    }
    
    examples[index] = { input: input.trim(), output: output.trim() };
    
    const serialized = this.serializeExamples(examples);
    dbHelpers.updatePromptExamples.run(serialized, promptId);
    
    return examples;
  }
};

// Initialize database on import
initializeDatabase();
initializeHelpers();