import { db } from './database.js';
import { up as migration001Up, down as migration001Down } from './migrations/001_add_input_output_examples.js';

/**
 * Migration Runner
 * 
 * Handles running database migrations in order and tracking which migrations
 * have been applied.
 */

// Create migrations tracking table
function initMigrationsTable() {
  db.exec(`
    CREATE TABLE IF NOT EXISTS migrations (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      migration_name TEXT UNIQUE NOT NULL,
      applied_at DATETIME DEFAULT CURRENT_TIMESTAMP
    );
  `);
}

// Get list of applied migrations
function getAppliedMigrations() {
  const stmt = db.prepare('SELECT migration_name FROM migrations ORDER BY id ASC');
  return stmt.all().map(row => row.migration_name);
}

// Mark migration as applied
function markMigrationApplied(migrationName) {
  const stmt = db.prepare('INSERT INTO migrations (migration_name) VALUES (?)');
  stmt.run(migrationName);
}

// Remove migration record
function removeMigrationRecord(migrationName) {
  const stmt = db.prepare('DELETE FROM migrations WHERE migration_name = ?');
  stmt.run(migrationName);
}

// Available migrations in order
const migrations = [
  {
    name: '001_add_input_output_examples',
    up: migration001Up,
    down: migration001Down
  }
];

export async function runMigrations() {
  console.log('Initializing migrations system...');
  initMigrationsTable();
  
  const appliedMigrations = getAppliedMigrations();
  console.log(`Found ${appliedMigrations.length} applied migrations:`, appliedMigrations);
  
  let migrationsRun = 0;
  
  for (const migration of migrations) {
    if (!appliedMigrations.includes(migration.name)) {
      console.log(`Running migration: ${migration.name}`);
      
      try {
        await migration.up();
        markMigrationApplied(migration.name);
        migrationsRun++;
        console.log(`✓ Migration ${migration.name} completed successfully`);
      } catch (error) {
        console.error(`✗ Migration ${migration.name} failed:`, error.message);
        throw error;
      }
    } else {
      console.log(`⊙ Migration ${migration.name} already applied, skipping`);
    }
  }
  
  if (migrationsRun > 0) {
    console.log(`\n${migrationsRun} migration(s) applied successfully!`);
  } else {
    console.log('\nNo new migrations to apply.');
  }
  
  return migrationsRun;
}

export async function rollbackLastMigration() {
  console.log('Rolling back last migration...');
  initMigrationsTable();
  
  const appliedMigrations = getAppliedMigrations();
  
  if (appliedMigrations.length === 0) {
    console.log('No migrations to rollback.');
    return false;
  }
  
  const lastMigration = appliedMigrations[appliedMigrations.length - 1];
  const migration = migrations.find(m => m.name === lastMigration);
  
  if (!migration) {
    throw new Error(`Migration ${lastMigration} not found in available migrations`);
  }
  
  console.log(`Rolling back migration: ${migration.name}`);
  
  try {
    await migration.down();
    removeMigrationRecord(migration.name);
    console.log(`✓ Migration ${migration.name} rolled back successfully`);
    return true;
  } catch (error) {
    console.error(`✗ Rollback of ${migration.name} failed:`, error.message);
    throw error;
  }
}

export async function getMigrationStatus() {
  initMigrationsTable();
  
  const appliedMigrations = getAppliedMigrations();
  const status = migrations.map(migration => ({
    name: migration.name,
    applied: appliedMigrations.includes(migration.name)
  }));
  
  return {
    totalMigrations: migrations.length,
    appliedCount: appliedMigrations.length,
    pendingCount: migrations.length - appliedMigrations.length,
    migrations: status
  };
}

// CLI interface
if (import.meta.url === `file://${process.argv[1]}`) {
  const command = process.argv[2];
  
  switch (command) {
    case 'up':
    case 'migrate':
      runMigrations()
        .then(() => process.exit(0))
        .catch(error => {
          console.error('Migration failed:', error);
          process.exit(1);
        });
      break;
      
    case 'down':
    case 'rollback':
      rollbackLastMigration()
        .then(() => process.exit(0))
        .catch(error => {
          console.error('Rollback failed:', error);
          process.exit(1);
        });
      break;
      
    case 'status':
      getMigrationStatus()
        .then(status => {
          console.log('\nMigration Status:');
          console.log(`Total migrations: ${status.totalMigrations}`);
          console.log(`Applied: ${status.appliedCount}`);
          console.log(`Pending: ${status.pendingCount}`);
          console.log('\nMigrations:');
          status.migrations.forEach(m => {
            const icon = m.applied ? '✓' : '⊙';
            const statusText = m.applied ? 'applied' : 'pending';
            console.log(`  ${icon} ${m.name} (${statusText})`);
          });
          process.exit(0);
        })
        .catch(error => {
          console.error('Status check failed:', error);
          process.exit(1);
        });
      break;
      
    default:
      console.log('Usage: node migrationRunner.js [up|down|status]');
      console.log('  up/migrate - Run pending migrations');
      console.log('  down/rollback - Rollback last migration');
      console.log('  status - Show migration status');
      process.exit(1);
  }
}