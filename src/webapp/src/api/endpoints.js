import { browserDbHelpers, initializeBrowserDatabase } from './browserDatabase.js';

// Initialize browser database
initializeBrowserDatabase();

// Mock API simulation with promises for realistic async behavior
const delay = (ms = 100) => new Promise(resolve => setTimeout(resolve, ms));

// Authentication endpoints
export const authAPI = {
  async login(username, password) {
    await delay();
    
    const user = browserDbHelpers.getUserByUsername(username);
    if (!user) {
      throw new Error('Invalid username or password');
    }
    
    // Generate session token
    const sessionToken = Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
    
    try {
      browserDbHelpers.createSession(user.id, sessionToken);
      
      return {
        user: {
          id: user.id,
          username: user.username,
          email: user.email,
          fullName: user.full_name,
          role: user.role
        },
        sessionToken
      };
    } catch (error) {
      throw new Error('Failed to create session');
    }
  },

  async logout(sessionToken) {
    await delay();
    browserDbHelpers.deleteSession(sessionToken);
    return { success: true };
  },

  async getCurrentUser(sessionToken) {
    await delay();
    
    if (!sessionToken) {
      return null;
    }
    
    const user = browserDbHelpers.getUserBySessionToken(sessionToken);
    if (!user) {
      return null;
    }
    
    return {
      id: user.id,
      username: user.username,
      email: user.email,
      fullName: user.full_name,
      role: user.role
    };
  }
};

// Prompts API
export const promptsAPI = {
  async getAll(filters = {}) {
    await delay();
    
    let prompts = browserDbHelpers.getPromptsWithFilters();
    
    // Apply filters
    if (filters.status) {
      prompts = prompts.filter(p => p.status.toLowerCase().includes(filters.status.toLowerCase()));
    }
    
    if (filters.category) {
      prompts = prompts.filter(p => p.category_name.toLowerCase().includes(filters.category.toLowerCase()));
    }
    
    if (filters.search) {
      const searchTerm = filters.search.toLowerCase();
      prompts = prompts.filter(p => 
        p.title.toLowerCase().includes(searchTerm) ||
        p.description.toLowerCase().includes(searchTerm) ||
        (p.tags && p.tags.toLowerCase().includes(searchTerm))
      );
    }
    
    // Sort by votes, created date, etc.
    if (filters.sortBy === 'votes') {
      prompts.sort((a, b) => b.votes - a.votes);
    } else if (filters.sortBy === 'recent') {
      prompts.sort((a, b) => new Date(b.created_at) - new Date(a.created_at));
    } else {
      // Default: trending (combination of votes and recency)
      prompts.sort((a, b) => {
        const aScore = a.votes + (new Date(a.created_at).getTime() / 1000000000);
        const bScore = b.votes + (new Date(b.created_at).getTime() / 1000000000);
        return bScore - aScore;
      });
    }
    
    // Transform to frontend format
    return prompts.map(p => ({
      id: p.id,
      title: p.title,
      description: p.description,
      content: p.content,
      category: p.category_name,
      tags: p.tags ? p.tags.split(',') : [],
      status: p.status,
      votes: p.votes,
      author: p.author_name,
      timeAgo: this.getTimeAgo(p.created_at),
      statusCount: p.votes + 10, // Mock additional metric
      difficulty: p.difficulty,
      comments: p.comments,
      userVote: null // Will be set when user context is available
    }));
  },

  async getById(id) {
    await delay();
    
    const prompt = browserDbHelpers.getPromptById(id);
    if (!prompt) {
      throw new Error('Prompt not found');
    }
    
    return {
      id: prompt.id,
      title: prompt.title,
      description: prompt.description,
      content: prompt.content,
      category: prompt.category_name,
      tags: prompt.tags ? prompt.tags.split(',') : [],
      status: prompt.status,
      votes: prompt.votes,
      author: prompt.author_name,
      timeAgo: this.getTimeAgo(prompt.created_at),
      statusCount: prompt.votes + 10,
      difficulty: prompt.difficulty,
      comments: prompt.comments,
      userVote: null
    };
  },

  async create(promptData, userId) {
    await delay();
    
    // Get category ID
    const category = browserDbHelpers.getCategoryByName(promptData.category);
    if (!category) {
      throw new Error('Invalid category');
    }
    
    try {
      const result = browserDbHelpers.createPrompt(
        promptData.title,
        promptData.description,
        promptData.content,
        category.name,
        userId,
        promptData.status || 'Suggested',
        promptData.difficulty || 'Intermediate'
      );
      
      const promptId = result.lastInsertRowid;
      
      // Tags are handled in browserDbHelpers.createPrompt
      
      return await this.getById(promptId);
    } catch (error) {
      throw new Error('Failed to create prompt: ' + error.message);
    }
  },

  async update(id, promptData, userId) {
    await delay();
    
    const existingPrompt = browserDbHelpers.getPromptById(id);
    if (!existingPrompt) {
      throw new Error('Prompt not found');
    }
    
    // Get category ID
    const category = browserDbHelpers.getCategoryByName(promptData.category);
    if (!category) {
      throw new Error('Invalid category');
    }
    
    try {
      browserDbHelpers.updatePrompt(
        promptData.title,
        promptData.description,
        promptData.content,
        category.name,
        promptData.status,
        promptData.difficulty,
        id
      );
      
      // Tags are handled in browserDbHelpers.updatePrompt
      
      return await this.getById(id);
    } catch (error) {
      throw new Error('Failed to update prompt: ' + error.message);
    }
  },

  async delete(id, userId) {
    await delay();
    
    const prompt = browserDbHelpers.getPromptById(id);
    if (!prompt) {
      throw new Error('Prompt not found');
    }
    
    try {
      browserDbHelpers.deletePrompt(id);
      return { success: true };
    } catch (error) {
      throw new Error('Failed to delete prompt: ' + error.message);
    }
  },

  async vote(promptId, userId, voteType = 'toggle') {
    await delay();
    
    const prompt = browserDbHelpers.getPromptById(promptId);
    if (!prompt) {
      throw new Error('Prompt not found');
    }
    
    const existingVote = browserDbHelpers.getUserVote(userId, promptId);
    
    try {
      if (voteType === 'toggle') {
        if (existingVote) {
          // Remove existing vote
          browserDbHelpers.deleteVote(userId, promptId);
        } else {
          // Add upvote
          browserDbHelpers.createVote(userId, promptId, 'up');
        }
      } else {
        // Specific vote type
        if (existingVote) {
          if (existingVote.voteType === voteType) {
            // Remove vote if same type
            browserDbHelpers.deleteVote(userId, promptId);
          } else {
            // Update vote type
            browserDbHelpers.updateVote(voteType, userId, promptId);
          }
        } else {
          // Create new vote
          browserDbHelpers.createVote(userId, promptId, voteType);
        }
      }
      
      // Vote count is automatically updated in browserDbHelpers
      
      return await this.getById(promptId);
    } catch (error) {
      throw new Error('Failed to vote: ' + error.message);
    }
  },

  getTimeAgo(dateString) {
    const date = new Date(dateString);
    const now = new Date();
    const diffInMinutes = Math.floor((now - date) / (1000 * 60));
    
    if (diffInMinutes < 60) {
      return `${diffInMinutes} minutes ago`;
    } else if (diffInMinutes < 1440) {
      const hours = Math.floor(diffInMinutes / 60);
      return `${hours} hour${hours > 1 ? 's' : ''} ago`;
    } else {
      const days = Math.floor(diffInMinutes / 1440);
      return `${days} day${days > 1 ? 's' : ''} ago`;
    }
  }
};

// Categories API
export const categoriesAPI = {
  async getAll() {
    await delay();
    const categories = browserDbHelpers.getCategories();
    
    return categories.sort((a, b) => b.count - a.count);
  }
};

// Export utility functions
export const apiUtils = {
  formatResponse(data, success = true, message = '') {
    return {
      success,
      message,
      data
    };
  },

  handleError(error) {
    console.error('API Error:', error);
    return {
      success: false,
      message: error.message || 'An error occurred',
      data: null
    };
  }
};