import { dbHelpers } from './database.js';
import promptsData from '../data/prompts.json' with { type: 'json' };

export async function migrateJsonToDatabase() {
  console.log('Starting migration of JSON data to database...');
  
  try {
    // Check if prompts already exist
    const existingPromptsCount = dbHelpers.getPromptsWithFilters.all().length;
    if (existingPromptsCount > 0) {
      console.log(`Database already contains ${existingPromptsCount} prompts, skipping migration.`);
      return;
    }

    let migratedCount = 0;
    
    for (const prompt of promptsData) {
      try {
        // Get category ID
        const category = dbHelpers.getCategoryByName.get(prompt.category);
        if (!category) {
          console.warn(`Category not found: ${prompt.category}, skipping prompt: ${prompt.title}`);
          continue;
        }

        // Get author ID (default to PE Team if not found)
        let author = dbHelpers.getUserByUsername.get('peteam');
        if (prompt.author && prompt.author !== 'PE Team') {
          // Try to match author by name
          if (prompt.author === 'Quantitative Team') {
            author = dbHelpers.getUserByUsername.get('quantteam');
          } else if (prompt.author === 'Research Team') {
            author = dbHelpers.getUserByUsername.get('research');
          }
        }

        if (!author) {
          console.warn(`Author not found for prompt: ${prompt.title}, using default`);
          author = dbHelpers.getUserByUsername.get('peteam');
        }

        // Prepare input/output examples
        const inputOutputExamples = prompt.inputOutputExamples || [];
        const examplesJson = JSON.stringify(inputOutputExamples);

        // Create prompt
        const result = dbHelpers.createPrompt.run(
          prompt.title,
          prompt.description,
          prompt.content || prompt.description, // Use description as content if no content
          category.id,
          author.id,
          prompt.status || 'Approved',
          prompt.difficulty || 'Intermediate',
          examplesJson
        );

        const promptId = result.lastInsertRowid;

        // Add tags
        if (prompt.tags && prompt.tags.length > 0) {
          for (const tagName of prompt.tags) {
            // Create tag if it doesn't exist
            dbHelpers.getOrCreateTag.run(tagName);
            const tag = dbHelpers.getTagByName.get(tagName);
            if (tag) {
              dbHelpers.linkPromptTag.run(promptId, tag.id);
            }
          }
        }

        // Set initial vote count
        if (prompt.votes && prompt.votes > 0) {
          // Create some mock votes to match the vote count
          // We'll use different users to create the votes
          const users = [
            dbHelpers.getUserByUsername.get('alexf'),
            dbHelpers.getUserByUsername.get('peteam'),
            dbHelpers.getUserByUsername.get('quantteam'),
            dbHelpers.getUserByUsername.get('research')
          ].filter(Boolean);

          let votesToCreate = Math.min(prompt.votes, users.length);
          for (let i = 0; i < votesToCreate; i++) {
            try {
              dbHelpers.createVote.run(users[i % users.length].id, promptId, 'up');
            } catch (error) {
              // Ignore duplicate vote errors
            }
          }

          // Update the vote count
          dbHelpers.updatePromptVoteCount.run(promptId, promptId, promptId);
        }

        migratedCount++;
        console.log(`Migrated prompt ${migratedCount}: ${prompt.title}`);
        
      } catch (error) {
        console.error(`Error migrating prompt "${prompt.title}":`, error);
      }
    }

    console.log(`Migration completed. Migrated ${migratedCount} prompts.`);
    
    // Verify migration
    const finalCount = dbHelpers.getPromptsWithFilters.all().length;
    console.log(`Database now contains ${finalCount} prompts.`);
    
  } catch (error) {
    console.error('Migration failed:', error);
    throw error;
  }
}

// Run migration if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  migrateJsonToDatabase()
    .then(() => process.exit(0))
    .catch((error) => {
      console.error('Migration failed:', error);
      process.exit(1);
    });
}