/* Apollo Official Fonts - Complete Typography System */

/* ===== SEGOE UI FONT FAMILY ===== */

@font-face {
  font-family: 'Segoe UI';
  src: url('/fonts/SegoeUI-Regular.ttf') format('truetype');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Segoe UI';
  src: url('/fonts/SegoeUI-Italic.ttf') format('truetype');
  font-weight: 400;
  font-style: italic;
  font-display: swap;
}

@font-face {
  font-family: 'Segoe UI';
  src: url('/fonts/SegoeUI-Light.ttf') format('truetype');
  font-weight: 300;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Segoe UI';
  src: url('/fonts/SegoeUI-LightItalic.ttf') format('truetype');
  font-weight: 300;
  font-style: italic;
  font-display: swap;
}

@font-face {
  font-family: 'Segoe UI';
  src: url('/fonts/SegoeUI-Semilight.ttf') format('truetype');
  font-weight: 350;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Segoe UI';
  src: url('/fonts/SegoeUI-SemilightItalic.ttf') format('truetype');
  font-weight: 350;
  font-style: italic;
  font-display: swap;
}

@font-face {
  font-family: 'Segoe UI';
  src: url('/fonts/SegoeUI-Semibold.ttf') format('truetype');
  font-weight: 600;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Segoe UI';
  src: url('/fonts/SegoeUI-SemiboldItalic.ttf') format('truetype');
  font-weight: 600;
  font-style: italic;
  font-display: swap;
}

@font-face {
  font-family: 'Segoe UI';
  src: url('/fonts/SegoeUI-Bold.ttf') format('truetype');
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Segoe UI';
  src: url('/fonts/SegoeUI-BoldItalic.ttf') format('truetype');
  font-weight: 700;
  font-style: italic;
  font-display: swap;
}

@font-face {
  font-family: 'Segoe UI';
  src: url('/fonts/SegoeUI-Black.ttf') format('truetype');
  font-weight: 900;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Segoe UI';
  src: url('/fonts/SegoeUI-BlackItalic.ttf') format('truetype');
  font-weight: 900;
  font-style: italic;
  font-display: swap;
}

/* ===== ADOBE GARAMOND PRO FONT FAMILY ===== */

@font-face {
  font-family: 'Adobe Garamond Pro';
  src: url('/fonts/AGaramondPro-Regular.otf') format('opentype');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Adobe Garamond Pro';
  src: url('/fonts/AGaramondPro-Italic.otf') format('opentype');
  font-weight: 400;
  font-style: italic;
  font-display: swap;
}

@font-face {
  font-family: 'Adobe Garamond Pro';
  src: url('/fonts/AGaramondPro-Bold.otf') format('opentype');
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Adobe Garamond Pro';
  src: url('/fonts/AGaramondPro-BoldItalic.otf') format('opentype');
  font-weight: 700;
  font-style: italic;
  font-display: swap;
}

/* ===== GRAPHIK FONT FAMILY ===== */

@font-face {
  font-family: 'Graphik';
  src: url('/fonts/Graphik-Thin-Web.woff2') format('woff2'),
       url('/fonts/Graphik-Thin-Web.woff') format('woff');
  font-weight: 100;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Graphik';
  src: url('/fonts/Graphik-ThinItalic-Web.woff2') format('woff2'),
       url('/fonts/Graphik-ThinItalic-Web.woff') format('woff');
  font-weight: 100;
  font-style: italic;
  font-display: swap;
}

@font-face {
  font-family: 'Graphik';
  src: url('/fonts/Graphik-Extralight-Web.woff2') format('woff2'),
       url('/fonts/Graphik-Extralight-Web.woff') format('woff');
  font-weight: 200;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Graphik';
  src: url('/fonts/Graphik-ExtralightItalic-Web.woff2') format('woff2'),
       url('/fonts/Graphik-ExtralightItalic-Web.woff') format('woff');
  font-weight: 200;
  font-style: italic;
  font-display: swap;
}

@font-face {
  font-family: 'Graphik';
  src: url('/fonts/Graphik-Light-Web.woff2') format('woff2'),
       url('/fonts/Graphik-Light-Web.woff') format('woff');
  font-weight: 300;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Graphik';
  src: url('/fonts/Graphik-LightItalic-Web.woff2') format('woff2'),
       url('/fonts/Graphik-LightItalic-Web.woff') format('woff');
  font-weight: 300;
  font-style: italic;
  font-display: swap;
}

@font-face {
  font-family: 'Graphik';
  src: url('/fonts/Graphik-Regular-Web.woff2') format('woff2'),
       url('/fonts/Graphik-Regular-Web.woff') format('woff');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Graphik';
  src: url('/fonts/Graphik-RegularItalic-Web.woff2') format('woff2'),
       url('/fonts/Graphik-RegularItalic-Web.woff') format('woff');
  font-weight: 400;
  font-style: italic;
  font-display: swap;
}

@font-face {
  font-family: 'Graphik';
  src: url('/fonts/Graphik-Medium-Web.woff2') format('woff2'),
       url('/fonts/Graphik-Medium-Web.woff') format('woff');
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Graphik';
  src: url('/fonts/Graphik-MediumItalic-Web.woff2') format('woff2'),
       url('/fonts/Graphik-MediumItalic-Web.woff') format('woff');
  font-weight: 500;
  font-style: italic;
  font-display: swap;
}

@font-face {
  font-family: 'Graphik';
  src: url('/fonts/Graphik-Semibold-Web.woff2') format('woff2'),
       url('/fonts/Graphik-Semibold-Web.woff') format('woff');
  font-weight: 600;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Graphik';
  src: url('/fonts/Graphik-SemiboldItalic-Web.woff2') format('woff2'),
       url('/fonts/Graphik-SemiboldItalic-Web.woff') format('woff');
  font-weight: 600;
  font-style: italic;
  font-display: swap;
}

@font-face {
  font-family: 'Graphik';
  src: url('/fonts/Graphik-Bold-Web.woff2') format('woff2'),
       url('/fonts/Graphik-Bold-Web.woff') format('woff');
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Graphik';
  src: url('/fonts/Graphik-BoldItalic-Web.woff2') format('woff2'),
       url('/fonts/Graphik-BoldItalic-Web.woff') format('woff');
  font-weight: 700;
  font-style: italic;
  font-display: swap;
}

@font-face {
  font-family: 'Graphik';
  src: url('/fonts/Graphik-Super-Web.woff2') format('woff2'),
       url('/fonts/Graphik-Super-Web.woff') format('woff');
  font-weight: 800;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Graphik';
  src: url('/fonts/Graphik-SuperItalic-Web.woff2') format('woff2'),
       url('/fonts/Graphik-SuperItalic-Web.woff') format('woff');
  font-weight: 800;
  font-style: italic;
  font-display: swap;
}

@font-face {
  font-family: 'Graphik';
  src: url('/fonts/Graphik-Black-Web.woff2') format('woff2'),
       url('/fonts/Graphik-Black-Web.woff') format('woff');
  font-weight: 900;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Graphik';
  src: url('/fonts/Graphik-BlackItalic-Web.woff2') format('woff2'),
       url('/fonts/Graphik-BlackItalic-Web.woff') format('woff');
  font-weight: 900;
  font-style: italic;
  font-display: swap;
}