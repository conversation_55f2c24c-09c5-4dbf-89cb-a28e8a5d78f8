// Responsive breakpoints utility
export const breakpoints = {
  mobile: '480px',
  tablet: '768px',
  desktop: '1024px',
  large: '1200px'
}

export const useMediaQuery = (query) => {
  if (typeof window === 'undefined') return false
  
  return window.matchMedia(query).matches
}

export const isMobile = () => useMediaQuery(`(max-width: ${breakpoints.tablet})`)
export const isTablet = () => useMediaQuery(`(min-width: ${breakpoints.tablet}) and (max-width: ${breakpoints.desktop})`)
export const isDesktop = () => useMediaQuery(`(min-width: ${breakpoints.desktop})`)

// Media query strings for CSS-in-JS
export const mediaQueries = {
  mobile: `@media (max-width: ${breakpoints.mobile})`,
  tablet: `@media (max-width: ${breakpoints.tablet})`,
  desktop: `@media (max-width: ${breakpoints.desktop})`,
  large: `@media (max-width: ${breakpoints.large})`
}