// Screenshot utility for debugging UI issues
export async function takeScreenshot(filename = 'debug-screenshot') {
  try {
    const canvas = await html2canvas(document.body, {
      allowTaint: true,
      useCORS: true,
      scale: 1,
      width: window.innerWidth,
      height: window.innerHeight
    });
    
    // Create download link
    const link = document.createElement('a');
    link.download = `${filename}-${Date.now()}.png`;
    link.href = canvas.toDataURL();
    link.click();
    
    console.log(`Screenshot saved as ${link.download}`);
    return canvas.toDataURL();
  } catch (error) {
    console.error('Screenshot failed:', error);
    // Fallback: try to use browser screenshot API if available
    if (navigator.mediaDevices && navigator.mediaDevices.getDisplayMedia) {
      try {
        const stream = await navigator.mediaDevices.getDisplayMedia({
          video: { mediaSource: 'screen' }
        });
        console.log('Use browser screenshot tools instead');
        stream.getTracks().forEach(track => track.stop());
      } catch (e) {
        console.log('Manual screenshot needed - use browser dev tools or external tool');
      }
    }
  }
}

// Quick debug function to check current layout
export function debugLayout() {
  console.log('=== LAYOUT DEBUG ===');
  console.log('Window size:', window.innerWidth, 'x', window.innerHeight);
  console.log('Body classes:', document.body.className);
  console.log('App element:', document.querySelector('.app'));
  
  const sidebar = document.querySelector('.sidebar-container');
  if (sidebar) {
    console.log('Sidebar found:', sidebar.getBoundingClientRect());
    console.log('Sidebar classes:', sidebar.className);
  } else {
    console.log('No sidebar found');
  }
  
  const landing = document.querySelector('.landing-page');
  if (landing) {
    console.log('Landing page found:', landing.getBoundingClientRect());
    console.log('Landing classes:', landing.className);
  }
  
  const main = document.querySelector('.main-content');
  if (main) {
    console.log('Main content found:', main.getBoundingClientRect());
  }
}

// Add to window for easy access in console
if (typeof window !== 'undefined') {
  window.takeScreenshot = takeScreenshot;
  window.debugLayout = debugLayout;
}