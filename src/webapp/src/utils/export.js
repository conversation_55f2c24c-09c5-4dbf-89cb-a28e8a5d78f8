// Export utilities for Apollo prompt library

export const exportFormats = {
  JSON: 'json',
  CSV: 'csv',
  MARKDOWN: 'markdown',
  TXT: 'txt'
};

// Export prompts as JSON
export function exportAsJSON(prompts, filename = 'apollo-prompts') {
  const data = {
    exported_at: new Date().toISOString(),
    total_prompts: prompts.length,
    prompts: prompts.map(prompt => ({
      id: prompt.id,
      title: prompt.title,
      description: prompt.description,
      content: prompt.content,
      category: prompt.category,
      tags: prompt.tags,
      status: prompt.status,
      difficulty: prompt.difficulty,
      votes: prompt.votes,
      author: prompt.author,
      comments: prompt.comments
    }))
  };

  const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
  downloadFile(blob, `${filename}.json`);
}

// Export prompts as CSV
export function exportAsCSV(prompts, filename = 'apollo-prompts') {
  const headers = ['ID', 'Title', 'Description', 'Category', 'Tags', 'Status', 'Difficulty', 'Votes', 'Author'];
  
  const csvContent = [
    headers.join(','),
    ...prompts.map(prompt => [
      prompt.id,
      `"${escapeCSV(prompt.title)}"`,
      `"${escapeCSV(prompt.description)}"`,
      `"${prompt.category}"`,
      `"${prompt.tags ? prompt.tags.join('; ') : ''}"`,
      `"${prompt.status}"`,
      `"${prompt.difficulty}"`,
      prompt.votes,
      `"${prompt.author}"`
    ].join(','))
  ].join('\n');

  const blob = new Blob([csvContent], { type: 'text/csv' });
  downloadFile(blob, `${filename}.csv`);
}

// Export prompts as Markdown
export function exportAsMarkdown(prompts, filename = 'apollo-prompts') {
  const markdown = [
    '# Apollo Prompt Library Export',
    '',
    `Generated on: ${new Date().toLocaleDateString()}`,
    `Total prompts: ${prompts.length}`,
    '',
    '---',
    ''
  ];

  prompts.forEach((prompt, index) => {
    markdown.push(
      `## ${index + 1}. ${prompt.title}`,
      '',
      `**Category:** ${prompt.category}`,
      `**Status:** ${prompt.status}`,
      `**Difficulty:** ${prompt.difficulty}`,
      `**Tags:** ${prompt.tags ? prompt.tags.join(', ') : 'None'}`,
      `**Votes:** ${prompt.votes}`,
      `**Author:** ${prompt.author}`,
      '',
      '### Description',
      prompt.description,
      ''
    );

    if (prompt.content && prompt.content !== prompt.description) {
      markdown.push(
        '### Content',
        prompt.content,
        ''
      );
    }

    markdown.push('---', '');
  });

  const blob = new Blob([markdown.join('\n')], { type: 'text/markdown' });
  downloadFile(blob, `${filename}.md`);
}

// Export prompts as plain text
export function exportAsText(prompts, filename = 'apollo-prompts') {
  const textContent = [
    'APOLLO PROMPT LIBRARY EXPORT',
    '='.repeat(50),
    '',
    `Generated: ${new Date().toLocaleString()}`,
    `Total prompts: ${prompts.length}`,
    '',
    '='.repeat(50),
    ''
  ];

  prompts.forEach((prompt, index) => {
    textContent.push(
      `${index + 1}. ${prompt.title.toUpperCase()}`,
      '-'.repeat(50),
      '',
      `Category: ${prompt.category}`,
      `Status: ${prompt.status}`,
      `Difficulty: ${prompt.difficulty}`,
      `Tags: ${prompt.tags ? prompt.tags.join(', ') : 'None'}`,
      `Votes: ${prompt.votes}`,
      `Author: ${prompt.author}`,
      '',
      'DESCRIPTION:',
      prompt.description,
      ''
    );

    if (prompt.content && prompt.content !== prompt.description) {
      textContent.push(
        'CONTENT:',
        prompt.content,
        ''
      );
    }

    textContent.push('='.repeat(50), '');
  });

  const blob = new Blob([textContent.join('\n')], { type: 'text/plain' });
  downloadFile(blob, `${filename}.txt`);
}

// Export single prompt
export function exportSinglePrompt(prompt, format = 'json') {
  const filename = `apollo-prompt-${prompt.id}-${prompt.title.toLowerCase().replace(/[^a-z0-9]/g, '-')}`;
  
  switch (format) {
    case exportFormats.JSON:
      exportAsJSON([prompt], filename);
      break;
    case exportFormats.CSV:
      exportAsCSV([prompt], filename);
      break;
    case exportFormats.MARKDOWN:
      exportAsMarkdown([prompt], filename);
      break;
    case exportFormats.TXT:
      exportAsText([prompt], filename);
      break;
    default:
      console.error('Unsupported export format:', format);
  }
}

// Helper function to download file
function downloadFile(blob, filename) {
  const url = URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.href = url;
  link.download = filename;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  URL.revokeObjectURL(url);
}

// Helper function to escape CSV values
function escapeCSV(value) {
  if (typeof value !== 'string') return value;
  return value.replace(/"/g, '""');
}

// Generate export summary
export function getExportSummary(prompts) {
  const categories = {};
  const statuses = {};
  const difficulties = {};

  prompts.forEach(prompt => {
    categories[prompt.category] = (categories[prompt.category] || 0) + 1;
    statuses[prompt.status] = (statuses[prompt.status] || 0) + 1;
    difficulties[prompt.difficulty] = (difficulties[prompt.difficulty] || 0) + 1;
  });

  return {
    total: prompts.length,
    categories,
    statuses,
    difficulties,
    totalVotes: prompts.reduce((sum, p) => sum + p.votes, 0),
    averageVotes: prompts.length > 0 ? (prompts.reduce((sum, p) => sum + p.votes, 0) / prompts.length).toFixed(1) : 0
  };
}