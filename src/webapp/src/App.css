* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Segoe UI', -apple-system, BlinkMacSystemFont, Roboto, sans-serif;
  background-color: #1F2025;
  color: #e0e0e0;
  height: 100vh;
}

.app {
  display: flex;
  flex-direction: column;
  height: 100vh;
  position: relative;
}

.ai-query-fixed {
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 1000;
  max-width: 400px;
  width: calc(100vw - 40px);
}

@media (max-width: 768px) {
  .ai-query-fixed {
    bottom: 10px;
    right: 10px;
    left: 10px;
    max-width: none;
    width: auto;
  }
}

.app-body {
  display: flex;
  flex: 1;
  overflow: hidden;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  body {
    overflow: auto; /* Allow scrolling on mobile */
  }
  
  .app {
    min-height: 100vh;
    height: auto;
  }
  
  .app-body {
    flex-direction: column;
    overflow: visible;
  }
}
