import promptsData from './data/prompts.json' with { type: 'json' };

export const mockPrompts = promptsData;

// Generate categories dynamically from prompts data
export const categories = (() => {
  const categoryCount = {};
  promptsData.forEach(prompt => {
    const category = prompt.category;
    categoryCount[category] = (categoryCount[category] || 0) + 1;
  });
  
  return Object.entries(categoryCount)
    .map(([name, count]) => ({ name, count }))
    .sort((a, b) => b.count - a.count);
})();

export const statuses = [
  "Suggested",
  "Approved", 
  "In Development",
  "Under Review",
  "Published"
];