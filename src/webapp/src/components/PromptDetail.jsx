import React, { useState } from 'react'
import { mockPrompts } from '../mockData'
import { Badge, ExampleCard } from './atoms'
import { useVote } from '../hooks/useVote'
import './PromptDetail.css'

function PromptDetail({ promptId, onBack, onVote }) {
  const [selectedVersion, setSelectedVersion] = useState('v2')
  
  // Get actual prompt data or use mock data
  const actualPrompt = promptId ? mockPrompts.find(p => p.id === promptId) : null
  
  // Create prompt data from actual prompt or fallback to mock
  const displayPrompt = actualPrompt ? {
    id: actualPrompt.id,
    title: actualPrompt.title,
    description: actualPrompt.description,
    author: actualPrompt.author || "Apollo Team",
    timeAgo: actualPrompt.timeAgo || "Recently",
    category: actualPrompt.category,
    difficulty: actualPrompt.difficulty || "Intermediate",
    tags: actualPrompt.tags || [],
    status: actualPrompt.status,
    statusCount: actualPrompt.statusCount || 0,
    votes: actualPrompt.votes || 0,
    userVote: actualPrompt.userVote || null,
    platform: "Apollo Intelligence App",
    urls: ["https://www.google.com"],
    goal: "Optimize investment analysis and decision-making processes for Apollo portfolio companies.",
    context: "Apollo portfolio companies require structured analysis frameworks to support strategic decision-making and value creation initiatives.",
    problem: "Ad-hoc analysis approaches can lead to inconsistent outcomes and missed opportunities for value creation.",
    solution: "Provide standardized, comprehensive analysis framework that ensures thorough evaluation of all key factors.",
    content: actualPrompt.content || "No content available for this prompt.",
    inputOutputExamples: actualPrompt.inputOutputExamples || []
  } : {
    id: promptId || "demo",
    title: "Advanced Code Review Assistant",
    description: "A comprehensive prompt for conducting thorough code reviews with security, performance, and best practices analysis.",
    author: "Sarah Chen",
    timeAgo: "2 days ago",
    category: "Development",
    difficulty: "Advanced",
    tags: ["code-review", "security", "performance", "best-practices"],
    status: "Published",
    statusCount: 12,
    votes: 47,
    userVote: null,
    platform: "Apollo Intelligence App",
    urls: ["https://www.google.com"],
    goal: "Enable Apollo teams to conduct thorough code reviews that improve security, performance, and maintainability across all portfolio company technology initiatives.",
    context: "Apollo portfolio companies frequently develop custom software solutions, APIs, and technology platforms. Code quality directly impacts system reliability, security posture, and operational efficiency - all critical factors in value creation.",
    problem: "Manual code reviews are inconsistent, time-consuming, and often miss critical security vulnerabilities or performance bottlenecks. Different reviewers focus on different aspects, leading to gaps in coverage and suboptimal code quality.",
    solution: "Provide a comprehensive, structured framework for code reviews that ensures consistent evaluation across security, performance, best practices, and testing coverage. Enable non-technical stakeholders to understand review outcomes and technical debt implications.",
    content: "Please conduct a comprehensive code review focusing on security, performance, and best practices.",
    inputOutputExamples: [
      {
        input: "Review this Python API endpoint that handles user authentication for a portfolio company's customer portal.",
        output: "**Security Analysis:**\n- ✅ Proper password hashing using bcrypt\n- ❌ Missing rate limiting - implement 5 requests/minute\n- ❌ JWT tokens lack expiration time\n\n**Performance:**\n- ❌ Database query not optimized - add index on email field\n- ✅ Appropriate caching headers\n\n**Recommendations:**\n1. Add rate limiting middleware\n2. Set JWT expiration to 1 hour\n3. Create database index: CREATE INDEX idx_users_email ON users(email)"
      },
      {
        input: "Evaluate this React component for a financial dashboard showing portfolio metrics.",
        output: "**Security Analysis:**\n- ✅ Proper data sanitization\n- ❌ API keys exposed in frontend code\n\n**Performance:**\n- ❌ Component re-renders on every state change\n- ❌ Large data sets loaded without pagination\n\n**Best Practices:**\n- ❌ No error boundaries\n- ❌ Missing TypeScript types\n\n**Recommendations:**\n1. Move API keys to environment variables\n2. Implement React.memo() and useMemo()\n3. Add pagination for data tables\n4. Add error boundaries and TypeScript"
      }
    ],
    versions: [
      { 
        version: 'v1', 
        date: '3 days ago',
        content: "Please review this code for basic issues and suggest improvements." 
      },
      { 
        version: 'v2', 
        date: '2 days ago',
        content: `# Code Review Assistant

Please conduct a comprehensive code review of the following code. Focus on:

## Security Analysis
- Identify potential security vulnerabilities
- Check for proper input validation
- Review authentication and authorization
- Assess data handling practices

## Performance Optimization
- Analyze algorithm efficiency
- Identify performance bottlenecks
- Suggest optimization opportunities
- Review resource usage patterns

## Best Practices Compliance
- Code structure and organization
- Naming conventions and readability
- Error handling implementation
- Documentation quality

## Testing Coverage
- Assess test completeness 
- Identify missing test cases
- Review test quality and maintainability

Please provide specific recommendations with code examples where applicable.

Code to review:
[PASTE YOUR CODE HERE]`
      },
      { 
        version: 'v3', 
        date: '1 day ago',
        content: "Extended version with additional architectural review guidelines..." 
      }
    ],
    metadata: {
      language: "English",
      model: "Claude-3.5-Sonnet",
      temperature: 0.7,
      maxTokens: 4000,
      platform: "Apollo Intelligence App",
      createdAt: "2024-01-15T10:30:00Z",
      updatedAt: "2024-01-17T14:20:00Z"
    }
  }

  // Handle versions - use mock versions for now since real data doesn't have versions
  const versions = displayPrompt.versions || [
    { 
      version: 'v1', 
      date: '3 days ago',
      content: displayPrompt.content || "No content available"
    }
  ]
  const currentVersion = versions.find(v => v.version === selectedVersion) || versions[0]

  const getStatusVariant = (status) => {
    switch(status.toLowerCase()) {
      case 'approved': return 'approved'
      case 'planned': return 'planned'
      case 'review': return 'review'
      case 'under review': return 'review'
      case 'published': return 'approved'
      case 'in development': return 'development'
      case 'suggested': return 'suggested'
      default: return 'default'
    }
  }

  // Use the vote hook
  const {
    votes,
    userVote,
    isVoting,
    hasUserVoted,
    error,
    handleVote: handleVoteInternal,
    upvote,
    downvote,
    clearError
  } = useVote(
    displayPrompt.id,
    displayPrompt.votes,
    displayPrompt.userVote,
    onVote // Pass the callback to parent
  )

  const handleVote = async (direction) => {
    try {
      clearError() // Clear any previous errors
      await handleVoteInternal(direction)
    } catch (error) {
      if (error.code === 'AUTH_REQUIRED') {
        // Handle authentication required (e.g., show login modal)
        console.log('Authentication required for voting')
      }
      // Error is already set in the hook state
    }
  }

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  return (
    <div className="prompt-detail">
      {/* Header */}
      <div className="detail-header">
        <button className="back-button" onClick={onBack}>
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
            <path d="M19 12H5M12 19l-7-7 7-7" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
          </svg>
          Back to Library
        </button>
      </div>

      {/* Main Content */}
      <div className="detail-content">
        <div className="detail-main">
          {/* Title and Meta */}
          <div className="detail-title-section">
            <h1 className="detail-title">{displayPrompt.title}</h1>
            <p className="detail-description">{displayPrompt.description}</p>
            
            <div className="detail-meta">
              <span className="detail-author">{displayPrompt.author} • {displayPrompt.timeAgo}</span>
              <span className="category-badge">{displayPrompt.category}</span>
              {displayPrompt.difficulty && <span className="difficulty-badge">{displayPrompt.difficulty}</span>}
              <Badge variant={getStatusVariant(displayPrompt.status)} size="md">
                [Quick Win ({displayPrompt.statusCount})] {displayPrompt.status}
              </Badge>
            </div>

            <div className="detail-tags">
              {displayPrompt.tags.map(tag => (
                <span key={tag} className="tag">#{tag}</span>
              ))}
            </div>
          </div>

          {/* Version Selector */}
          <div className="version-section">
            <label className="version-label">Version:</label>
            <select 
              className="version-selector" 
              value={selectedVersion}
              onChange={(e) => setSelectedVersion(e.target.value)}
            >
              {versions.map(version => (
                <option key={version.version} value={version.version}>
                  {version.version} ({version.date})
                </option>
              ))}
            </select>
          </div>

          {/* Open Prompt Button */}
          <div className="open-prompt-section">
            <a 
              href={displayPrompt.urls[0]} 
              target="_blank" 
              rel="noopener noreferrer"
              className="open-prompt-button"
            >
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                <path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6M15 3h6v6M10 14L21 3" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
              Open prompt in {displayPrompt.platform}
            </a>
          </div>

          {/* Goal, Context, Problem, Solution Section */}
          <div className="prompt-overview">
            <div className="overview-grid">
              <div className="overview-item">
                <h4 className="overview-label">Goal</h4>
                <p className="overview-text">{displayPrompt.goal}</p>
              </div>
              <div className="overview-item">
                <h4 className="overview-label">Context</h4>
                <p className="overview-text">{displayPrompt.context}</p>
              </div>
              <div className="overview-item">
                <h4 className="overview-label">Problem</h4>
                <p className="overview-text">{displayPrompt.problem}</p>
              </div>
              <div className="overview-item">
                <h4 className="overview-label">Solution</h4>
                <p className="overview-text">{displayPrompt.solution}</p>
              </div>
            </div>
          </div>

          {/* Prompt Content */}
          <div className="prompt-content">
            <div className="content-header">
              <h3>Prompt Content</h3>
              <span className="version-info">{currentVersion.version} • {currentVersion.date}</span>
            </div>
            <div className="content-display">
              <pre className="content-text">{currentVersion.content}</pre>
            </div>
          </div>

          {/* Metadata */}
          <div className="metadata-section">
            <h3>Configuration</h3>
            <div className="metadata-grid">
              <div className="metadata-item">
                <span className="metadata-label">Language:</span>
                <span className="metadata-value">{displayPrompt.metadata?.language || 'English'}</span>
              </div>
              <div className="metadata-item">
                <span className="metadata-label">Model:</span>
                <span className="metadata-value">{displayPrompt.metadata?.model || 'GPT-4'}</span>
              </div>
              <div className="metadata-item">
                <span className="metadata-label">Temperature:</span>
                <span className="metadata-value">{displayPrompt.metadata?.temperature || '0.7'}</span>
              </div>
              <div className="metadata-item">
                <span className="metadata-label">Max Tokens:</span>
                <span className="metadata-value">{displayPrompt.metadata?.maxTokens || '2000'}</span>
              </div>
              <div className="metadata-item">
                <span className="metadata-label">Created:</span>
                <span className="metadata-value">{formatDate(displayPrompt.metadata?.createdAt || displayPrompt.created_at)}</span>
              </div>
              <div className="metadata-item">
                <span className="metadata-label">Updated:</span>
                <span className="metadata-value">{formatDate(displayPrompt.metadata?.updatedAt || displayPrompt.updated_at)}</span>
              </div>
              <div className="metadata-item">
                <span className="metadata-label">Platform:</span>
                <span className="metadata-value">{displayPrompt.platform}</span>
              </div>
            </div>
          </div>

          {/* Input Output Examples */}
          <ExampleCard 
            examples={displayPrompt.inputOutputExamples && displayPrompt.inputOutputExamples.length > 0 
              ? displayPrompt.inputOutputExamples 
              : [
                  {
                    input: "Upload the Q3 portfolio performance data and create a summary analysis.",
                    output: "Here's a comprehensive Q3 portfolio analysis:\n\n**Key Performance Metrics:**\n- Total Portfolio Value: $2.4B (+8.2% QoQ)\n- Top Performing Assets: Healthcare sector (+15.3%)\n- Underperforming: Real Estate (-2.1%)\n\n**Risk Analysis:**\n- VaR 95%: $12.3M\n- Sharpe Ratio: 1.47\n- Beta: 0.89\n\n**Recommendations:**\n1. Increase allocation to healthcare\n2. Hedge real estate exposure\n3. Monitor emerging markets volatility"
                  },
                  {
                    input: "Review the due diligence checklist for the TechCorp acquisition and highlight any red flags.",
                    output: "**Due Diligence Review - TechCorp Acquisition**\n\n✅ **Cleared Items:**\n- Financial statements audited\n- IP portfolio verified\n- Management team backgrounds checked\n\n⚠️ **Red Flags Identified:**\n- Customer concentration: 40% revenue from single client\n- Pending litigation: $5M claim unresolved\n- Key employee retention: 3 senior engineers leaving\n\n**Recommendation:** Request additional customer diversification plan and legal opinion on litigation exposure before proceeding."
                  },
                  {
                    input: "Generate an investment thesis for the renewable energy sector focus.",
                    output: "**Renewable Energy Investment Thesis**\n\n**Market Opportunity:**\n- $2.8T global market by 2030 (12% CAGR)\n- Policy tailwinds: IRA tax credits, EU Green Deal\n- Technology cost curves favoring renewables\n\n**Investment Strategy:**\n- Target: Grid-scale storage and transmission\n- Geographic focus: US Southwest, Nordic Europe\n- Stage: Growth equity, $50-500M check sizes\n\n**Key Risks:**\n- Regulatory changes\n- Technology obsolescence\n- Supply chain constraints\n\n**Target IRR:** 18-25% over 5-7 year hold period"
                  }
                ]
            }
          />
        </div>

        {/* Vote Section */}
        <div className="detail-vote-section">
          <div className="vote-container">
            <button 
              className={`vote-button large ${userVote === 'up' ? 'voted' : ''} ${isVoting ? 'voting' : ''}`}
              onClick={() => handleVote('up')}
              disabled={isVoting}
              style={{ 
                opacity: isVoting ? 0.6 : 1,
                cursor: isVoting ? 'not-allowed' : 'pointer'
              }}
            >
              <svg width="16" height="12" viewBox="0 0 12 8" fill="none">
                <path d="M6 0L12 8H0L6 0Z" fill="currentColor"/>
              </svg>
            </button>
            <div className="vote-count large">{votes}</div>
            <button 
              className={`vote-button large ${userVote === 'down' ? 'voted' : ''} ${isVoting ? 'voting' : ''}`}
              onClick={() => handleVote('down')}
              disabled={isVoting}
              style={{ 
                opacity: isVoting ? 0.6 : 1,
                cursor: isVoting ? 'not-allowed' : 'pointer'
              }}
            >
              <svg width="16" height="12" viewBox="0 0 12 8" fill="none">
                <path d="M6 8L0 0H12L6 8Z" fill="currentColor"/>
              </svg>
            </button>
          </div>
          {error && (
            <div className="vote-error" style={{ 
              color: '#dc2626', 
              fontSize: '14px', 
              marginTop: '8px',
              textAlign: 'center',
              padding: '8px',
              backgroundColor: '#fef2f2',
              border: '1px solid #fecaca',
              borderRadius: '4px'
            }}>
              {error}
            </div>
          )}
          
          <div className="vote-actions">
            <button className="action-button" onClick={() => alert('Save functionality would bookmark this prompt to your favorites.')}>
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                <path d="M19 21l-7-5-7 5V5a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2v16z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
              Save
            </button>
            <button className="action-button" onClick={() => {
              const promptData = JSON.stringify(displayPrompt, null, 2);
              const blob = new Blob([promptData], { type: 'application/json' });
              const url = URL.createObjectURL(blob);
              const a = document.createElement('a');
              a.href = url;
              a.download = `${displayPrompt.title.replace(/[^a-z0-9]/gi, '_').toLowerCase()}.json`;
              a.click();
              URL.revokeObjectURL(url);
            }}>
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                <path d="M4 12v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-8M16 6l-4-4-4 4M12 2v13" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
              Export
            </button>
            <button className="action-button" onClick={() => alert('Follow functionality would notify you of updates to this prompt.')}>
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                <path d="M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9M13.73 21a2 2 0 0 1-3.46 0" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
              Follow
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

export default PromptDetail