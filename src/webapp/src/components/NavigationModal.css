.nav-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  backdrop-filter: blur(4px);
  padding: 20px;
}

.nav-modal {
  background: #1e293b;
  border-radius: 12px;
  width: 100%;
  max-width: 800px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  animation: modalSlideIn 0.3s ease-out;
  color: white;
}

.nav-modal-header {
  padding: 24px 24px 16px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: linear-gradient(135deg, #2d3748 0%, #1a202c 100%);
  border-radius: 12px 12px 0 0;
}

.nav-modal-header h2 {
  margin: 0;
  font-family: 'Segoe UI', -apple-system, sans-serif;
  font-size: 20px;
  font-weight: 600;
  color: #e2e8f0;
}

.close-button {
  background: none;
  border: none;
  color: #94a3b8;
  font-size: 24px;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-button:hover {
  background: rgba(255, 255, 255, 0.1);
  color: #f1f5f9;
}

.nav-modal-content {
  padding: 24px;
}

/* Home Dashboard */
.home-dashboard {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

.dashboard-section h3 {
  margin: 0 0 16px 0;
  font-size: 18px;
  font-weight: 600;
  color: #f1f5f9;
}

.action-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.action-card {
  background: #2d3748;
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.2s;
}

.action-card:hover {
  background: #374151;
  border-color: #007acc;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 122, 204, 0.1);
}

.action-icon {
  font-size: 32px;
  margin-bottom: 12px;
}

.action-title {
  font-size: 16px;
  font-weight: 600;
  color: #f1f5f9;
  margin-bottom: 8px;
}

.action-desc {
  font-size: 14px;
  color: #94a3b8;
  line-height: 1.4;
}

.activity-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.activity-item {
  display: flex;
  gap: 12px;
  padding: 12px;
  background: #2d3748;
  border-radius: 6px;
}

.activity-time {
  font-size: 12px;
  color: #64748b;
  min-width: 80px;
  flex-shrink: 0;
}

.activity-text {
  font-size: 14px;
  color: #e2e8f0;
}

/* Chat Interface */
.chat-interface {
  display: flex;
  flex-direction: column;
  gap: 20px;
  height: 500px;
}

.chat-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-bottom: 16px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.chat-header h3 {
  margin: 0;
  font-size: 18px;
  color: #f1f5f9;
}

.chat-status {
  padding: 4px 8px;
  background: #10b981;
  color: white;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 16px 0;
}

.message {
  display: flex;
  gap: 12px;
  margin-bottom: 16px;
}

.message-avatar {
  width: 32px;
  height: 32px;
  border-radius: 16px;
  background: #007acc;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.message-content {
  flex: 1;
}

.message-text {
  background: #2d3748;
  padding: 12px 16px;
  border-radius: 12px;
  color: #e2e8f0;
  line-height: 1.4;
}

.message-time {
  font-size: 12px;
  color: #64748b;
  margin-top: 4px;
}

.chat-input-area {
  display: flex;
  gap: 8px;
  align-items: center;
}

.chat-message-input {
  flex: 1;
  padding: 12px 16px;
  background: #2d3748;
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  color: #e2e8f0;
  font-size: 14px;
}

.chat-message-input:focus {
  outline: none;
  border-color: #007acc;
  box-shadow: 0 0 0 3px rgba(0, 122, 204, 0.1);
}

.send-button {
  padding: 12px 20px;
  background: #007acc;
  color: white;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.send-button:hover {
  background: #0056b3;
}

.chat-suggestions {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.suggestion {
  padding: 6px 12px;
  background: #374151;
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  font-size: 12px;
  color: #94a3b8;
  cursor: pointer;
  transition: all 0.2s;
}

.suggestion:hover {
  background: #4b5563;
  color: #f1f5f9;
}

/* People Directory */
.people-directory {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.people-search-input {
  width: 100%;
  padding: 12px 16px;
  background: #2d3748;
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  color: #e2e8f0;
  font-size: 14px;
}

.people-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 16px;
}

.person-card {
  background: #2d3748;
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 16px;
  display: flex;
  align-items: center;
  gap: 12px;
}

.person-avatar {
  width: 48px;
  height: 48px;
  border-radius: 24px;
  background: #007acc;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  flex-shrink: 0;
}

.person-info {
  flex: 1;
}

.person-name {
  font-size: 16px;
  font-weight: 600;
  color: #f1f5f9;
  margin-bottom: 2px;
}

.person-title {
  font-size: 14px;
  color: #94a3b8;
  margin-bottom: 2px;
}

.person-team {
  font-size: 12px;
  color: #64748b;
}

.person-status {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
  text-transform: uppercase;
}

.person-status.online {
  background: #10b981;
  color: white;
}

.person-status.away {
  background: #f59e0b;
  color: white;
}

.person-status.offline {
  background: #6b7280;
  color: white;
}

.teams-section h3 {
  margin: 0 0 16px 0;
  font-size: 18px;
  color: #f1f5f9;
}

.team-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.team-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background: #2d3748;
  border-radius: 6px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.team-item:hover {
  background: #374151;
}

.team-name {
  font-size: 14px;
  color: #e2e8f0;
}

.team-count {
  font-size: 12px;
  color: #64748b;
}

/* Content Library */
.content-library {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

.content-categories {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.category-card {
  background: #2d3748;
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 20px;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s;
}

.category-card:hover {
  background: #374151;
  border-color: #007acc;
  transform: translateY(-2px);
}

.category-icon {
  font-size: 32px;
  margin-bottom: 12px;
}

.category-title {
  font-size: 16px;
  font-weight: 600;
  color: #f1f5f9;
  margin-bottom: 8px;
}

.category-count {
  font-size: 14px;
  color: #64748b;
}

.recent-content h3 {
  margin: 0 0 16px 0;
  font-size: 18px;
  color: #f1f5f9;
}

.content-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.content-item {
  display: flex;
  gap: 12px;
  padding: 12px;
  background: #2d3748;
  border-radius: 6px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.content-item:hover {
  background: #374151;
}

.content-icon {
  font-size: 20px;
  flex-shrink: 0;
}

.content-info {
  flex: 1;
}

.content-title {
  font-size: 14px;
  font-weight: 500;
  color: #e2e8f0;
  margin-bottom: 4px;
}

.content-meta {
  font-size: 12px;
  color: #64748b;
}

/* Apps Grid */
.apps-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
}

.app-card {
  background: #2d3748;
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.2s;
}

.app-card:hover {
  background: #374151;
  border-color: #007acc;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 122, 204, 0.1);
}

.app-icon {
  font-size: 40px;
  margin-bottom: 16px;
}

.app-name {
  font-size: 16px;
  font-weight: 600;
  color: #f1f5f9;
  margin-bottom: 8px;
}

.app-desc {
  font-size: 14px;
  color: #94a3b8;
  line-height: 1.4;
}

/* Animations */
@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .nav-modal {
    margin: 10px;
    max-width: none;
  }
  
  .action-grid,
  .people-grid,
  .content-categories,
  .apps-grid {
    grid-template-columns: 1fr;
  }
  
  .chat-interface {
    height: 400px;
  }
}