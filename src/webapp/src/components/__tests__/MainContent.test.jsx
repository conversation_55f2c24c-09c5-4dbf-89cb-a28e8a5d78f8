import { describe, it, expect, vi, beforeEach } from 'vitest'
import { screen, fireEvent, within } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { renderWithProviders, mockPromptData, mockPromptsArray } from '../../test/utils'
import MainContent from '../MainContent'

describe('MainContent', () => {
  const defaultProps = {
    prompts: mockPromptsArray,
    categories: [],
    sortBy: 'trending',
    setSortBy: vi.fn(),
    searchTerm: '',
    setSearchTerm: vi.fn(),
    onVote: vi.fn(),
    onCardClick: vi.fn(),
    onNewPromptClick: vi.fn(),
    onCitationClick: vi.fn(),
    onMobileMenuToggle: vi.fn(),
    messages: [],
    setMessages: vi.fn(),
    query: '',
    setQuery: vi.fn(),
    loading: false,
    setLoading: vi.fn(),
    isExpanded: false,
    setIsExpanded: vi.fn()
  }

  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('Basic Rendering', () => {
    it('renders main content structure correctly', () => {
      renderWithProviders(<MainContent {...defaultProps} />)

      expect(screen.getByText('Apollo Prompt Library')).toBeInTheDocument()
      expect(screen.getByPlaceholderText('Search prompts...')).toBeInTheDocument()
      expect(screen.getByRole('combobox')).toHaveValue('trending')
      expect(screen.getByText('Submit Idea')).toBeInTheDocument()
      expect(screen.getByText(/Curated collection of investment analysis prompts/)).toBeInTheDocument()
    })

    it('renders header with correct elements', () => {
      renderWithProviders(<MainContent {...defaultProps} />)

      expect(screen.getByText('☰')).toBeInTheDocument()
      expect(screen.getByRole('heading', { name: 'Apollo Prompt Library' })).toBeInTheDocument()
      expect(screen.getByPlaceholderText('Search prompts...')).toBeInTheDocument()
      expect(screen.getByRole('combobox')).toBeInTheDocument()
      expect(screen.getByRole('button', { name: /Submit Idea/ })).toBeInTheDocument()
    })

    it('renders footer elements correctly', () => {
      renderWithProviders(<MainContent {...defaultProps} />)

      expect(screen.getByText('Ask me anything about the Apollo prompts library')).toBeInTheDocument()
    })
  })

  describe('Search Input Functionality', () => {
    it('displays search term value correctly', () => {
      const propsWithSearchTerm = {
        ...defaultProps,
        searchTerm: 'test search'
      }
      renderWithProviders(<MainContent {...propsWithSearchTerm} />)

      const searchInput = screen.getByPlaceholderText('Search prompts...')
      expect(searchInput).toHaveValue('test search')
    })

    it('calls setSearchTerm on input change', async () => {
      const user = userEvent.setup()
      renderWithProviders(<MainContent {...defaultProps} />)

      const searchInput = screen.getByPlaceholderText('Search prompts...')
      await user.type(searchInput, 'test')

      expect(defaultProps.setSearchTerm).toHaveBeenCalledTimes(4) // Once for each character
      // userEvent.type calls onChange with each character individually
      expect(defaultProps.setSearchTerm).toHaveBeenCalledWith('t')
      expect(defaultProps.setSearchTerm).toHaveBeenCalledWith('e')  
      expect(defaultProps.setSearchTerm).toHaveBeenCalledWith('s')
      expect(defaultProps.setSearchTerm).toHaveBeenCalledWith('t')
    })

    it('handles search input onChange event with correct value', () => {
      renderWithProviders(<MainContent {...defaultProps} />)

      const searchInput = screen.getByPlaceholderText('Search prompts...')
      fireEvent.change(searchInput, { target: { value: 'apollo investment' } })

      expect(defaultProps.setSearchTerm).toHaveBeenCalledWith('apollo investment')
    })

    it('clears search term when input is cleared', () => {
      const propsWithSearchTerm = {
        ...defaultProps,
        searchTerm: 'existing search'
      }
      renderWithProviders(<MainContent {...propsWithSearchTerm} />)

      const searchInput = screen.getByPlaceholderText('Search prompts...')
      fireEvent.change(searchInput, { target: { value: '' } })

      expect(defaultProps.setSearchTerm).toHaveBeenCalledWith('')
    })

    it('handles special characters in search input', () => {
      renderWithProviders(<MainContent {...defaultProps} />)

      const searchInput = screen.getByPlaceholderText('Search prompts...')
      fireEvent.change(searchInput, { target: { value: '!@#$%^&*()' } })

      expect(defaultProps.setSearchTerm).toHaveBeenCalledWith('!@#$%^&*()')
    })
  })

  describe('Sort Dropdown Behavior', () => {
    it('displays current sort value correctly', () => {
      renderWithProviders(<MainContent {...defaultProps} />)

      const sortSelect = screen.getByRole('combobox')
      expect(sortSelect).toHaveValue('trending')
    })

    it('renders all sort options', () => {
      renderWithProviders(<MainContent {...defaultProps} />)

      const sortSelect = screen.getByRole('combobox')
      const options = within(sortSelect).getAllByRole('option')

      expect(options).toHaveLength(3)
      expect(within(sortSelect).getByRole('option', { name: 'Trending' })).toBeInTheDocument()
      expect(within(sortSelect).getByRole('option', { name: 'Newest' })).toBeInTheDocument()
      expect(within(sortSelect).getByRole('option', { name: 'Most Votes' })).toBeInTheDocument()
    })

    it('calls setSortBy when selection changes', async () => {
      const user = userEvent.setup()
      renderWithProviders(<MainContent {...defaultProps} />)

      const sortSelect = screen.getByRole('combobox')
      await user.selectOptions(sortSelect, 'newest')

      expect(defaultProps.setSortBy).toHaveBeenCalledWith('newest')
    })

    it('handles different sort option selections', () => {
      renderWithProviders(<MainContent {...defaultProps} />)

      const sortSelect = screen.getByRole('combobox')
      
      fireEvent.change(sortSelect, { target: { value: 'votes' } })
      expect(defaultProps.setSortBy).toHaveBeenCalledWith('votes')

      fireEvent.change(sortSelect, { target: { value: 'newest' } })
      expect(defaultProps.setSortBy).toHaveBeenCalledWith('newest')

      fireEvent.change(sortSelect, { target: { value: 'trending' } })
      expect(defaultProps.setSortBy).toHaveBeenCalledWith('trending')
    })

    it('displays correct sort value when prop changes', () => {
      const { rerender } = renderWithProviders(<MainContent {...defaultProps} />)

      expect(screen.getByRole('combobox')).toHaveValue('trending')

      const newProps = { ...defaultProps, sortBy: 'newest' }
      rerender(<MainContent {...newProps} />)

      expect(screen.getByRole('combobox')).toHaveValue('newest')
    })
  })

  describe('Prompt List Rendering', () => {
    it('renders correct number of PromptCard components', () => {
      renderWithProviders(<MainContent {...defaultProps} />)

      const promptCards = document.querySelectorAll('.prompt-card')
      expect(promptCards).toHaveLength(2) // mockPromptsArray has 2 items
    })

    it('renders single prompt correctly', () => {
      const singlePromptProps = {
        ...defaultProps,
        prompts: [mockPromptData]
      }
      renderWithProviders(<MainContent {...singlePromptProps} />)

      const promptCards = document.querySelectorAll('.prompt-card')
      expect(promptCards).toHaveLength(1)
      expect(screen.getByText('Test Prompt')).toBeInTheDocument()
    })

    it('renders multiple prompts correctly', () => {
      renderWithProviders(<MainContent {...defaultProps} />)

      expect(screen.getByText('Test Prompt')).toBeInTheDocument()
      expect(screen.getByText('Second Test Prompt')).toBeInTheDocument()
    })

    it('renders prompts with unique keys', () => {
      renderWithProviders(<MainContent {...defaultProps} />)

      const promptList = document.querySelector('.prompt-list')
      const children = Array.from(promptList.children)
      
      // Each PromptCard should have unique content
      expect(children).toHaveLength(2)
      expect(screen.getByText('Test Prompt')).toBeInTheDocument()
      expect(screen.getByText('Second Test Prompt')).toBeInTheDocument()
    })

    it('renders prompts in the order provided', () => {
      const orderedPrompts = [
        { ...mockPromptData, id: 1, title: 'First Prompt' },
        { ...mockPromptData, id: 2, title: 'Second Prompt' },
        { ...mockPromptData, id: 3, title: 'Third Prompt' }
      ]
      const orderedProps = {
        ...defaultProps,
        prompts: orderedPrompts
      }
      renderWithProviders(<MainContent {...orderedProps} />)

      const titles = screen.getAllByText(/Prompt$/)
      expect(titles).toHaveLength(3)
      expect(titles[0]).toHaveTextContent('First Prompt')
      expect(titles[1]).toHaveTextContent('Second Prompt')
      expect(titles[2]).toHaveTextContent('Third Prompt')
    })
  })

  describe('Props Passing to PromptCard', () => {
    it('passes onVote prop to all PromptCard components', () => {
      renderWithProviders(<MainContent {...defaultProps} />)

      // Verify that vote sections exist (indicating onVote is passed and PromptCard renders correctly)
      const voteSections = document.querySelectorAll('.vote-section')
      expect(voteSections.length).toBeGreaterThan(0)
    })

    it('passes onCardClick prop to all PromptCard components', () => {
      renderWithProviders(<MainContent {...defaultProps} />)

      const firstCard = document.querySelector('.prompt-card')
      fireEvent.click(firstCard)

      expect(defaultProps.onCardClick).toHaveBeenCalledWith(mockPromptData.id)
    })

    it('passes correct prompt data to each PromptCard', () => {
      renderWithProviders(<MainContent {...defaultProps} />)

      // Verify first prompt data
      expect(screen.getByText('Test Prompt')).toBeInTheDocument()
      expect(screen.getByText('Test Category')).toBeInTheDocument()

      // Verify second prompt data
      expect(screen.getByText('Second Test Prompt')).toBeInTheDocument()
      expect(screen.getByText('Another Category')).toBeInTheDocument()
    })

    it('ensures all required props are passed to PromptCard', () => {
      renderWithProviders(<MainContent {...defaultProps} />)

      // Each PromptCard should render without errors, indicating all required props are passed
      const promptCards = document.querySelectorAll('.prompt-card')
      expect(promptCards).toHaveLength(2)

      // Verify essential prompt data is displayed
      expect(screen.getByText('Test Prompt')).toBeInTheDocument()
      expect(screen.getByText('Second Test Prompt')).toBeInTheDocument()
    })
  })

  describe('Empty State Handling', () => {
    it('renders empty prompt list without errors', () => {
      const emptyProps = {
        ...defaultProps,
        prompts: []
      }
      renderWithProviders(<MainContent {...emptyProps} />)

      // Should still render main structure
      expect(screen.getByText('Apollo Prompt Library')).toBeInTheDocument()
      expect(screen.getByPlaceholderText('Search prompts...')).toBeInTheDocument()
      
      // Should not have any prompt cards
      const promptCards = document.querySelectorAll('.prompt-card')
      expect(promptCards).toHaveLength(0)
    })

    it('handles undefined prompts array gracefully', () => {
      const undefinedPromptsProps = {
        ...defaultProps,
        prompts: undefined
      }

      // This should throw an error since the component doesn't handle undefined prompts
      expect(() => {
        renderWithProviders(<MainContent {...undefinedPromptsProps} />)
      }).toThrow()
    })

    it('displays description even with empty prompts', () => {
      const emptyProps = {
        ...defaultProps,
        prompts: []
      }
      renderWithProviders(<MainContent {...emptyProps} />)

      expect(screen.getByText(/Curated collection of investment analysis prompts/)).toBeInTheDocument()
    })

    it('maintains functionality with empty prompts array', () => {
      const emptyProps = {
        ...defaultProps,
        prompts: []
      }
      renderWithProviders(<MainContent {...emptyProps} />)

      // Search should still work
      const searchInput = screen.getByPlaceholderText('Search prompts...')
      fireEvent.change(searchInput, { target: { value: 'test' } })
      expect(defaultProps.setSearchTerm).toHaveBeenCalledWith('test')

      // Sort should still work
      const sortSelect = screen.getByRole('combobox')
      fireEvent.change(sortSelect, { target: { value: 'newest' } })
      expect(defaultProps.setSortBy).toHaveBeenCalledWith('newest')
    })
  })

  describe('Event Handler Prop Validation', () => {
    it('calls onVote with correct arguments when vote area is clicked', () => {
      renderWithProviders(<MainContent {...defaultProps} />)

      // Find and click a vote section (since PromptCard handles the vote interaction)
      const voteSections = document.querySelectorAll('.vote-section')
      if (voteSections.length > 0) {
        fireEvent.click(voteSections[0])
        // Note: This test verifies the prop is passed, actual voting logic is in PromptCard
      }
    })

    it('calls onCardClick with correct prompt id', () => {
      renderWithProviders(<MainContent {...defaultProps} />)

      const firstCard = document.querySelector('.prompt-card')
      fireEvent.click(firstCard)

      expect(defaultProps.onCardClick).toHaveBeenCalledWith(1) // mockPromptData.id
    })

    it('does not break when optional callbacks are not provided', () => {
      const propsWithoutCallbacks = {
        ...defaultProps,
        onVote: undefined,
        onCardClick: undefined
      }

      expect(() => {
        renderWithProviders(<MainContent {...propsWithoutCallbacks} />)
      }).not.toThrow()
    })

    it('handles multiple rapid callback invocations', () => {
      renderWithProviders(<MainContent {...defaultProps} />)

      const searchInput = screen.getByPlaceholderText('Search prompts...')
      
      // Rapid input changes
      fireEvent.change(searchInput, { target: { value: 'a' } })
      fireEvent.change(searchInput, { target: { value: 'ab' } })
      fireEvent.change(searchInput, { target: { value: 'abc' } })

      expect(defaultProps.setSearchTerm).toHaveBeenCalledTimes(3)
      expect(defaultProps.setSearchTerm).toHaveBeenNthCalledWith(1, 'a')
      expect(defaultProps.setSearchTerm).toHaveBeenNthCalledWith(2, 'ab')
      expect(defaultProps.setSearchTerm).toHaveBeenNthCalledWith(3, 'abc')
    })

    it('preserves callback function references across re-renders', () => {
      const { rerender } = renderWithProviders(<MainContent {...defaultProps} />)

      const searchInput = screen.getByPlaceholderText('Search prompts...')
      fireEvent.change(searchInput, { target: { value: 'test1' } })

      // Re-render with same prop functions
      rerender(<MainContent {...defaultProps} />)

      fireEvent.change(searchInput, { target: { value: 'test2' } })

      expect(defaultProps.setSearchTerm).toHaveBeenCalledWith('test1')
      expect(defaultProps.setSearchTerm).toHaveBeenCalledWith('test2')
    })
  })

  describe('Component Structure and CSS Classes', () => {
    it('applies correct CSS classes to main elements', () => {
      renderWithProviders(<MainContent {...defaultProps} />)

      expect(document.querySelector('.main-content')).toBeInTheDocument()
      expect(document.querySelector('.header')).toBeInTheDocument()
      expect(document.querySelector('.header-controls')).toBeInTheDocument()
      expect(document.querySelector('.content-container')).toBeInTheDocument()
      expect(document.querySelector('.prompt-list')).toBeInTheDocument()
      expect(document.querySelector('.ai-query-container')).toBeInTheDocument()
    })

    it('applies correct classes to form elements', () => {
      renderWithProviders(<MainContent {...defaultProps} />)

      expect(document.querySelector('.search-input')).toBeInTheDocument()
      expect(document.querySelector('.sort-select')).toBeInTheDocument()
      expect(document.querySelector('.submit-idea-button')).toBeInTheDocument()
    })

    it('maintains proper DOM hierarchy', () => {
      renderWithProviders(<MainContent {...defaultProps} />)

      const mainContent = document.querySelector('.main-content')
      const header = document.querySelector('.header')
      const contentContainer = document.querySelector('.content-container')
      const promptList = document.querySelector('.prompt-list')

      expect(mainContent).toContainElement(header)
      expect(mainContent).toContainElement(contentContainer)
      expect(contentContainer).toContainElement(promptList)
    })
  })

  describe('Accessibility and User Experience', () => {
    it('provides accessible form labels and roles', () => {
      renderWithProviders(<MainContent {...defaultProps} />)

      const searchInput = screen.getByPlaceholderText('Search prompts...')
      const sortSelect = screen.getByRole('combobox')
      const submitButton = screen.getByRole('button', { name: /Submit Idea/ })

      expect(searchInput).toHaveAttribute('type', 'text')
      expect(sortSelect).toBeInTheDocument()
      expect(submitButton).toBeInTheDocument()
    })

    it('maintains focus management for interactive elements', () => {
      renderWithProviders(<MainContent {...defaultProps} />)

      const searchInput = screen.getByPlaceholderText('Search prompts...')
      searchInput.focus()
      expect(document.activeElement).toBe(searchInput)

      const sortSelect = screen.getByRole('combobox')
      sortSelect.focus()
      expect(document.activeElement).toBe(sortSelect)
    })

    it('provides meaningful placeholder text', () => {
      renderWithProviders(<MainContent {...defaultProps} />)

      expect(screen.getByPlaceholderText('Search prompts...')).toBeInTheDocument()
    })
  })
})