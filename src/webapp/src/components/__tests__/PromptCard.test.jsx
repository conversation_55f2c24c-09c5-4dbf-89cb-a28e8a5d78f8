import { describe, it, expect, vi, beforeEach } from 'vitest'
import { screen, fireEvent, render } from '@testing-library/react'
import { renderWithProviders, mockPromptData, createMockEvent } from '../../test/utils'
import PromptCard from '../PromptCard'

describe('PromptCard', () => {
  const mockOnCardClick = vi.fn()
  const mockOnVote = vi.fn()

  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('Basic Rendering', () => {
    it('renders prompt card with all basic information', () => {
      renderWithProviders(
        <PromptCard 
          prompt={mockPromptData} 
          onCardClick={mockOnCardClick}
          onVote={mockOnVote}
        />
      )

      expect(screen.getByText('Test Prompt')).toBeInTheDocument()
      expect(screen.getByText('A test prompt description')).toBeInTheDocument()
      expect(screen.getByText('Test Category')).toBeInTheDocument()
      expect(screen.getByText(/Test Author/)).toBeInTheDocument()
      expect(screen.getByText(/1 hour ago/)).toBeInTheDocument()
      expect(screen.getByText('Beginner')).toBeInTheDocument()
    })

    it('renders the prompt card with correct DOM structure', () => {
      const { container } = renderWithProviders(
        <PromptCard 
          prompt={mockPromptData} 
          onCardClick={mockOnCardClick}
          onVote={mockOnVote}
        />
      )

      expect(container.querySelector('.prompt-card')).toBeInTheDocument()
      expect(container.querySelector('.vote-section')).toBeInTheDocument()
      expect(container.querySelector('.card-content')).toBeInTheDocument()
      expect(container.querySelector('.card-title')).toBeInTheDocument()
      expect(container.querySelector('.card-description')).toBeInTheDocument()
      expect(container.querySelector('.card-meta')).toBeInTheDocument()
      expect(container.querySelector('.card-footer')).toBeInTheDocument()
    })
  })

  describe('Voting Functionality', () => {
    it('displays vote count correctly', () => {
      renderWithProviders(
        <PromptCard 
          prompt={mockPromptData} 
          onCardClick={mockOnCardClick}
          onVote={mockOnVote}
        />
      )

      expect(screen.getByText('10')).toBeInTheDocument()
    })

    it('displays different vote count values', () => {
      const promptWithDifferentVotes = { ...mockPromptData, votes: 42 }
      renderWithProviders(
        <PromptCard 
          prompt={promptWithDifferentVotes} 
          onCardClick={mockOnCardClick}
          onVote={mockOnVote}
        />
      )

      expect(screen.getByText('42')).toBeInTheDocument()
    })

    it('displays zero votes correctly', () => {
      const promptWithZeroVotes = { ...mockPromptData, votes: 0 }
      renderWithProviders(
        <PromptCard 
          prompt={promptWithZeroVotes} 
          onCardClick={mockOnCardClick}
          onVote={mockOnVote}
        />
      )

      expect(screen.getByText('0')).toBeInTheDocument()
    })

    it('shows unvoted state by default (gray styling)', () => {
      const { container } = renderWithProviders(
        <PromptCard 
          prompt={mockPromptData} 
          onCardClick={mockOnCardClick}
          onVote={mockOnVote}
        />
      )

      const voteCount = container.querySelector('.vote-count')
      expect(voteCount).not.toHaveClass('voted')
    })

    it('shows voted state when user has voted (purple styling)', () => {
      const votedPrompt = { ...mockPromptData, userVote: true }
      const { container } = renderWithProviders(
        <PromptCard 
          prompt={votedPrompt} 
          onCardClick={mockOnCardClick}
          onVote={mockOnVote}
        />
      )

      const voteCount = container.querySelector('.vote-count')
      expect(voteCount).toHaveClass('voted')
    })

    it('calls onVote when vote button is clicked', () => {
      const { container } = renderWithProviders(
        <PromptCard 
          prompt={mockPromptData} 
          onCardClick={mockOnCardClick}
          onVote={mockOnVote}
        />
      )

      const voteButton = container.querySelector('.vote-count')
      fireEvent.click(voteButton)

      expect(mockOnVote).toHaveBeenCalledWith(mockPromptData.id, 'toggle')
    })

    it('prevents event propagation when vote button is clicked', () => {
      const { container } = renderWithProviders(
        <PromptCard 
          prompt={mockPromptData} 
          onCardClick={mockOnCardClick}
          onVote={mockOnVote}
        />
      )

      const voteButton = container.querySelector('.vote-count')
      fireEvent.click(voteButton)

      // onCardClick should not be called when vote button is clicked
      expect(mockOnCardClick).not.toHaveBeenCalled()
      expect(mockOnVote).toHaveBeenCalledWith(mockPromptData.id, 'toggle')
    })

    it('handles vote click with custom event object', () => {
      const { container } = renderWithProviders(
        <PromptCard 
          prompt={mockPromptData} 
          onCardClick={mockOnCardClick}
          onVote={mockOnVote}
        />
      )

      const voteButton = container.querySelector('.vote-count')
      // Just test the normal click functionality since we can't easily test custom event handling
      fireEvent.click(voteButton)

      expect(mockOnVote).toHaveBeenCalledWith(mockPromptData.id, 'toggle')
      expect(mockOnCardClick).not.toHaveBeenCalled()
    })
  })

  describe('Card Click Handling', () => {
    it('calls onCardClick when card is clicked', () => {
      const { container } = renderWithProviders(
        <PromptCard 
          prompt={mockPromptData} 
          onCardClick={mockOnCardClick}
          onVote={mockOnVote}
        />
      )

      const card = container.querySelector('.prompt-card')
      fireEvent.click(card)

      expect(mockOnCardClick).toHaveBeenCalledWith(mockPromptData.id)
    })

    it('calls onCardClick when clicking on card content areas', () => {
      const { container } = renderWithProviders(
        <PromptCard 
          prompt={mockPromptData} 
          onCardClick={mockOnCardClick}
          onVote={mockOnVote}
        />
      )

      const cardContent = container.querySelector('.card-content')
      fireEvent.click(cardContent)

      expect(mockOnCardClick).toHaveBeenCalledWith(mockPromptData.id)
    })

    it('calls onCardClick when clicking on title', () => {
      renderWithProviders(
        <PromptCard 
          prompt={mockPromptData} 
          onCardClick={mockOnCardClick}
          onVote={mockOnVote}
        />
      )

      const title = screen.getByText('Test Prompt')
      fireEvent.click(title)

      expect(mockOnCardClick).toHaveBeenCalledWith(mockPromptData.id)
    })

    it('does not call onCardClick when vote button is clicked', () => {
      const { container } = renderWithProviders(
        <PromptCard 
          prompt={mockPromptData} 
          onCardClick={mockOnCardClick}
          onVote={mockOnVote}
        />
      )

      const voteButton = container.querySelector('.vote-count')
      fireEvent.click(voteButton)

      expect(mockOnCardClick).not.toHaveBeenCalled()
    })
  })

  describe('Status Badge Rendering', () => {
    it('renders approved status with correct CSS class', () => {
      const approvedPrompt = { ...mockPromptData, status: 'Approved' }
      const { container } = renderWithProviders(
        <PromptCard 
          prompt={approvedPrompt} 
          onCardClick={mockOnCardClick}
          onVote={mockOnVote}
        />
      )

      const statusBadge = container.querySelector('.apollo-badge')
      expect(statusBadge).toHaveClass('apollo-badge--approved')
      expect(screen.getByText(/Approved/)).toBeInTheDocument()
    })

    it('renders planned status with correct CSS class', () => {
      const plannedPrompt = { ...mockPromptData, status: 'Planned' }
      const { container } = renderWithProviders(
        <PromptCard 
          prompt={plannedPrompt} 
          onCardClick={mockOnCardClick}
          onVote={mockOnVote}
        />
      )

      const statusBadge = container.querySelector('.apollo-badge')
      expect(statusBadge).toHaveClass('apollo-badge--planned') // Planned now maps to planned variant
      expect(screen.getByText(/Planned/)).toBeInTheDocument()
    })

    it('renders review status with correct CSS class', () => {
      const reviewPrompt = { ...mockPromptData, status: 'Under Review' } // Using the actual case from the switch statement
      const { container } = renderWithProviders(
        <PromptCard 
          prompt={reviewPrompt} 
          onCardClick={mockOnCardClick}
          onVote={mockOnVote}
        />
      )

      const statusBadge = container.querySelector('.apollo-badge')
      expect(statusBadge).toHaveClass('apollo-badge--review')
      expect(screen.getByText(/Under Review/)).toBeInTheDocument()
    })

    it('renders published status with correct CSS class', () => {
      const publishedPrompt = { ...mockPromptData, status: 'Published' }
      const { container } = renderWithProviders(
        <PromptCard 
          prompt={publishedPrompt} 
          onCardClick={mockOnCardClick}
          onVote={mockOnVote}
        />
      )

      const statusBadge = container.querySelector('.apollo-badge')
      expect(statusBadge).toHaveClass('apollo-badge--approved') // Published maps to approved variant
      expect(screen.getByText(/Published/)).toBeInTheDocument()
    })

    it('renders default status class for unknown status', () => {
      const unknownStatusPrompt = { ...mockPromptData, status: 'Unknown' }
      const { container } = renderWithProviders(
        <PromptCard 
          prompt={unknownStatusPrompt} 
          onCardClick={mockOnCardClick}
          onVote={mockOnVote}
        />
      )

      const statusBadge = container.querySelector('.apollo-badge')
      expect(statusBadge).toHaveClass('apollo-badge--default')
      expect(screen.getByText(/Unknown/)).toBeInTheDocument()
    })

    it('handles case-insensitive status matching', () => {
      const lowerCaseStatusPrompt = { ...mockPromptData, status: 'approved' }
      const { container } = renderWithProviders(
        <PromptCard 
          prompt={lowerCaseStatusPrompt} 
          onCardClick={mockOnCardClick}
          onVote={mockOnVote}
        />
      )

      const statusBadge = container.querySelector('.apollo-badge')
      expect(statusBadge).toHaveClass('apollo-badge--approved')
    })

    it('displays status text in badge', () => {
      const promptWithStatus = { ...mockPromptData, status: 'Approved' }
      renderWithProviders(
        <PromptCard 
          prompt={promptWithStatus} 
          onCardClick={mockOnCardClick}
          onVote={mockOnVote}
        />
      )

      expect(screen.getByText('Approved')).toBeInTheDocument()
    })
  })

  describe('Tag Rendering', () => {
    it('renders tags correctly', () => {
      renderWithProviders(
        <PromptCard 
          prompt={mockPromptData} 
          onCardClick={mockOnCardClick}
          onVote={mockOnVote}
        />
      )

      expect(screen.getByText('#tag1')).toBeInTheDocument()
      expect(screen.getByText('#tag2')).toBeInTheDocument()
    })

    it('renders multiple tags with correct formatting', () => {
      const multiTagPrompt = { 
        ...mockPromptData, 
        tags: ['react', 'javascript', 'testing', 'frontend'] 
      }
      renderWithProviders(
        <PromptCard 
          prompt={multiTagPrompt} 
          onCardClick={mockOnCardClick}
          onVote={mockOnVote}
        />
      )

      expect(screen.getByText('#react')).toBeInTheDocument()
      expect(screen.getByText('#javascript')).toBeInTheDocument()
      expect(screen.getByText('#testing')).toBeInTheDocument()
      expect(screen.getByText('#frontend')).toBeInTheDocument()
    })

    it('handles empty tags array', () => {
      const noTagsPrompt = { ...mockPromptData, tags: [] }
      const { container } = renderWithProviders(
        <PromptCard 
          prompt={noTagsPrompt} 
          onCardClick={mockOnCardClick}
          onVote={mockOnVote}
        />
      )

      const tagsContainer = container.querySelector('.tags')
      expect(tagsContainer.children).toHaveLength(0)
    })

    it('handles single tag', () => {
      const singleTagPrompt = { ...mockPromptData, tags: ['solo'] }
      renderWithProviders(
        <PromptCard 
          prompt={singleTagPrompt} 
          onCardClick={mockOnCardClick}
          onVote={mockOnVote}
        />
      )

      expect(screen.getByText('#solo')).toBeInTheDocument()
    })

    it('renders tags with correct CSS class', () => {
      const { container } = renderWithProviders(
        <PromptCard 
          prompt={mockPromptData} 
          onCardClick={mockOnCardClick}
          onVote={mockOnVote}
        />
      )

      const tags = container.querySelectorAll('.tag')
      expect(tags).toHaveLength(2)
      tags.forEach(tag => {
        expect(tag).toHaveClass('tag')
      })
    })
  })

  describe('Comments Display', () => {
    it('displays comment count when provided', () => {
      renderWithProviders(
        <PromptCard 
          prompt={mockPromptData} 
          onCardClick={mockOnCardClick}
          onVote={mockOnVote}
        />
      )

      expect(screen.getByText('3')).toBeInTheDocument()
    })

    it('displays zero comments when no comments exist', () => {
      const noCommentsPrompt = { ...mockPromptData, comments: 0 }
      renderWithProviders(
        <PromptCard 
          prompt={noCommentsPrompt} 
          onCardClick={mockOnCardClick}
          onVote={mockOnVote}
        />
      )

      expect(screen.getByText('0')).toBeInTheDocument()
    })

    it('displays zero comments when comments property is undefined', () => {
      const { comments, ...promptWithoutComments } = mockPromptData
      renderWithProviders(
        <PromptCard 
          prompt={promptWithoutComments} 
          onCardClick={mockOnCardClick}
          onVote={mockOnVote}
        />
      )

      expect(screen.getByText('0')).toBeInTheDocument()
    })

    it('renders comment icon', () => {
      const { container } = renderWithProviders(
        <PromptCard 
          prompt={mockPromptData} 
          onCardClick={mockOnCardClick}
          onVote={mockOnVote}
        />
      )

      const commentIcon = container.querySelector('.comment-icon')
      expect(commentIcon).toBeInTheDocument()
    })
  })

  describe('User Interaction States', () => {
    it('applies hover state CSS classes', () => {
      const { container } = renderWithProviders(
        <PromptCard 
          prompt={mockPromptData} 
          onCardClick={mockOnCardClick}
          onVote={mockOnVote}
        />
      )

      const card = container.querySelector('.prompt-card')
      expect(card).toHaveClass('prompt-card')
      
      // Verify hover states can be applied (CSS classes exist)
      const voteButton = container.querySelector('.vote-count')
      expect(voteButton).toHaveClass('vote-count')
    })

    it('has cursor pointer on clickable elements', () => {
      const { container } = renderWithProviders(
        <PromptCard 
          prompt={mockPromptData} 
          onCardClick={mockOnCardClick}
          onVote={mockOnVote}
        />
      )

      const card = container.querySelector('.prompt-card')
      const voteButton = container.querySelector('.vote-count')
      
      // These elements should have cursor: pointer in CSS
      expect(card).toBeInTheDocument()
      expect(voteButton).toBeInTheDocument()
    })
  })

  describe('Props Validation and Edge Cases', () => {
    it('handles prompt object with missing optional properties', () => {
      const minimalPrompt = {
        id: 1,
        title: 'Minimal Prompt',
        description: 'Basic description',
        category: 'Test',
        tags: [],
        status: 'Draft',
        votes: 0,
        author: 'Test Author',
        timeAgo: '1 hour ago',
        statusCount: 1
      }

      const { container } = renderWithProviders(
        <PromptCard 
          prompt={minimalPrompt} 
          onCardClick={mockOnCardClick}
          onVote={mockOnVote}
        />
      )

      expect(screen.getByText('Minimal Prompt')).toBeInTheDocument()
      expect(screen.getByText('Basic description')).toBeInTheDocument()
      
      // Check for vote count specifically
      const voteCount = container.querySelector('.vote-count')
      expect(voteCount).toHaveTextContent('0')
      
      // Check for comment count specifically
      const commentCount = container.querySelector('.comment-count')
      expect(commentCount).toHaveTextContent('0')
    })

    it('handles prompt with no difficulty', () => {
      const { difficulty, ...promptWithoutDifficulty } = mockPromptData
      const { container } = renderWithProviders(
        <PromptCard 
          prompt={promptWithoutDifficulty} 
          onCardClick={mockOnCardClick}
          onVote={mockOnVote}
        />
      )

      // Should not render difficulty badge
      expect(container.querySelector('.difficulty-badge')).not.toBeInTheDocument()
    })

    it('renders difficulty badge when present', () => {
      renderWithProviders(
        <PromptCard 
          prompt={mockPromptData} 
          onCardClick={mockOnCardClick}
          onVote={mockOnVote}
        />
      )

      expect(screen.getByText('Beginner')).toBeInTheDocument()
    })

    it('handles long titles and descriptions', () => {
      const longContentPrompt = {
        ...mockPromptData,
        title: 'This is a very long title that should be handled properly by the component without breaking the layout',
        description: 'This is a very long description that contains multiple sentences and should wrap properly within the card layout without causing any visual issues or breaking the overall design of the prompt card component.'
      }

      renderWithProviders(
        <PromptCard 
          prompt={longContentPrompt} 
          onCardClick={mockOnCardClick}
          onVote={mockOnVote}
        />
      )

      expect(screen.getByText(longContentPrompt.title)).toBeInTheDocument()
      expect(screen.getByText(longContentPrompt.description)).toBeInTheDocument()
    })

    it('handles special characters in content', () => {
      const specialCharPrompt = {
        ...mockPromptData,
        title: 'Test & Prompt <Script>',
        description: 'Description with "quotes" and special chars: @#$%^&*()',
        author: 'Author & Co.',
        tags: ['tag-with-dash', 'tag_with_underscore', 'tag@special']
      }

      renderWithProviders(
        <PromptCard 
          prompt={specialCharPrompt} 
          onCardClick={mockOnCardClick}
          onVote={mockOnVote}
        />
      )

      expect(screen.getByText('Test & Prompt <Script>')).toBeInTheDocument()
      expect(screen.getByText('Description with "quotes" and special chars: @#$%^&*()')).toBeInTheDocument()
      expect(screen.getByText(/Author & Co\./)).toBeInTheDocument()
      expect(screen.getByText('#tag-with-dash')).toBeInTheDocument()
      expect(screen.getByText('#tag_with_underscore')).toBeInTheDocument()
      expect(screen.getByText('#tag@special')).toBeInTheDocument()
    })

    it('handles numeric values properly', () => {
      const numericPrompt = {
        ...mockPromptData,
        votes: 999999,
        statusCount: 0,
        comments: 12345
      }

      renderWithProviders(
        <PromptCard 
          prompt={numericPrompt} 
          onCardClick={mockOnCardClick}
          onVote={mockOnVote}
        />
      )

      expect(screen.getByText('999999')).toBeInTheDocument()
      expect(screen.getByText('12345')).toBeInTheDocument()
      // The current PromptCard just shows the status, not "Quick Win (count)"
      expect(screen.getByText('Approved')).toBeInTheDocument()
    })
  })

  describe('Event Handling Edge Cases', () => {
    it('handles rapid successive clicks', () => {
      const { container } = renderWithProviders(
        <PromptCard 
          prompt={mockPromptData} 
          onCardClick={mockOnCardClick}
          onVote={mockOnVote}
        />
      )

      const card = container.querySelector('.prompt-card')
      
      // Simulate rapid clicks
      fireEvent.click(card)
      fireEvent.click(card)
      fireEvent.click(card)

      expect(mockOnCardClick).toHaveBeenCalledTimes(3)
      expect(mockOnCardClick).toHaveBeenCalledWith(mockPromptData.id)
    })

    it('handles rapid vote clicks', () => {
      const { container } = renderWithProviders(
        <PromptCard 
          prompt={mockPromptData} 
          onCardClick={mockOnCardClick}
          onVote={mockOnVote}
        />
      )

      const voteButton = container.querySelector('.vote-count')
      
      // Simulate rapid vote clicks
      fireEvent.click(voteButton)
      fireEvent.click(voteButton)
      fireEvent.click(voteButton)

      expect(mockOnVote).toHaveBeenCalledTimes(3)
      expect(mockOnVote).toHaveBeenCalledWith(mockPromptData.id, 'toggle')
      expect(mockOnCardClick).not.toHaveBeenCalled()
    })

    it('handles mixed card and vote clicks', () => {
      const { container } = renderWithProviders(
        <PromptCard 
          prompt={mockPromptData} 
          onCardClick={mockOnCardClick}
          onVote={mockOnVote}
        />
      )

      const card = container.querySelector('.prompt-card')
      const voteButton = container.querySelector('.vote-count')
      
      fireEvent.click(card)
      fireEvent.click(voteButton)
      fireEvent.click(card)

      expect(mockOnCardClick).toHaveBeenCalledTimes(2)
      expect(mockOnVote).toHaveBeenCalledTimes(1)
    })
  })

  describe('Accessibility', () => {
    it('has appropriate ARIA attributes', () => {
      const { container } = renderWithProviders(
        <PromptCard 
          prompt={mockPromptData} 
          onCardClick={mockOnCardClick}
          onVote={mockOnVote}
        />
      )

      const card = container.querySelector('.prompt-card')
      const voteButton = container.querySelector('.vote-count')
      
      // Elements should be focusable
      expect(card).toBeInTheDocument()
      expect(voteButton).toBeInTheDocument()
    })
  })
})