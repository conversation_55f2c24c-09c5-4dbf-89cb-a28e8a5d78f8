import { describe, it, expect, vi, beforeEach } from 'vitest'
import { screen, fireEvent, render } from '@testing-library/react'
import { renderWithProviders } from '../../test/utils'
import PromptDetail from '../PromptDetail'

describe('PromptDetail', () => {
  const mockOnBack = vi.fn()
  const mockOnVote = vi.fn()
  const mockPromptId = 'test-prompt-1'

  beforeEach(() => {
    vi.clearAllMocks()
    // Mock Date.now() to ensure consistent date formatting tests
    vi.spyOn(Date.prototype, 'toLocaleDateString').mockReturnValue('January 15, 2024 at 10:30 AM')
  })

  describe('Basic Rendering', () => {
    it('renders prompt detail with all major sections', () => {
      renderWithProviders(
        <PromptDetail 
          promptId={mockPromptId}
          onBack={mockOnBack}
          onVote={mockOnVote}
        />
      )

      // Check main sections
      expect(screen.getByText('Advanced Code Review Assistant')).toBeInTheDocument()
      expect(screen.getByText(/comprehensive prompt for conducting thorough code reviews/)).toBeInTheDocument()
      expect(screen.getByText('Sarah Chen • 2 days ago')).toBeInTheDocument()
      expect(screen.getByText('Back to Library')).toBeInTheDocument()
    })

    it('renders header section correctly', () => {
      const { container } = renderWithProviders(
        <PromptDetail 
          promptId={mockPromptId}
          onBack={mockOnBack}
          onVote={mockOnVote}
        />
      )

      expect(container.querySelector('.detail-header')).toBeInTheDocument()
      expect(container.querySelector('.back-button')).toBeInTheDocument()
    })

    it('renders metadata section with all configuration items', () => {
      renderWithProviders(
        <PromptDetail 
          promptId={mockPromptId}
          onBack={mockOnBack}
          onVote={mockOnVote}
        />
      )

      expect(screen.getByText('Configuration')).toBeInTheDocument()
      expect(screen.getByText('Language:')).toBeInTheDocument()
      expect(screen.getByText('English')).toBeInTheDocument()
      expect(screen.getByText('Model:')).toBeInTheDocument()
      expect(screen.getByText('Claude-3.5-Sonnet')).toBeInTheDocument()
      expect(screen.getByText('Temperature:')).toBeInTheDocument()
      expect(screen.getByText('0.7')).toBeInTheDocument()
      expect(screen.getByText('Max Tokens:')).toBeInTheDocument()
      expect(screen.getByText('4000')).toBeInTheDocument()
    })

    it('renders content sections correctly', () => {
      renderWithProviders(
        <PromptDetail 
          promptId={mockPromptId}
          onBack={mockOnBack}
          onVote={mockOnVote}
        />
      )

      expect(screen.getByText('Goal')).toBeInTheDocument()
      expect(screen.getByText('Context')).toBeInTheDocument()
      expect(screen.getByText('Problem')).toBeInTheDocument()
      expect(screen.getByText('Solution')).toBeInTheDocument()
    })

    it('renders examples section correctly', () => {
      renderWithProviders(
        <PromptDetail 
          promptId={mockPromptId}
          onBack={mockOnBack}
          onVote={mockOnVote}
        />
      )

      expect(screen.getByText('Input Output Examples')).toBeInTheDocument()
      expect(screen.getAllByText('Input')).toHaveLength(2)
      expect(screen.getAllByText('Output')).toHaveLength(2)
    })
  })

  describe('Version Switching', () => {
    it('renders version selector with all versions', () => {
      const { container } = renderWithProviders(
        <PromptDetail 
          promptId={mockPromptId}
          onBack={mockOnBack}
          onVote={mockOnVote}
        />
      )

      const versionSelector = container.querySelector('.version-selector')
      expect(versionSelector).toBeInTheDocument()
      
      const options = versionSelector.querySelectorAll('option')
      expect(options).toHaveLength(3)
      expect(options[0]).toHaveTextContent('v1 (3 days ago)')
      expect(options[1]).toHaveTextContent('v2 (2 days ago)')
      expect(options[2]).toHaveTextContent('v3 (1 day ago)')
    })

    it('defaults to v2 version', () => {
      const { container } = renderWithProviders(
        <PromptDetail 
          promptId={mockPromptId}
          onBack={mockOnBack}
          onVote={mockOnVote}
        />
      )

      const versionSelector = container.querySelector('.version-selector')
      expect(versionSelector.value).toBe('v2')
      expect(screen.getByText('v2 • 2 days ago')).toBeInTheDocument()
    })

    it('changes content when version is switched', () => {
      const { container } = renderWithProviders(
        <PromptDetail 
          promptId={mockPromptId}
          onBack={mockOnBack}
          onVote={mockOnVote}
        />
      )

      const versionSelector = container.querySelector('.version-selector')
      const contentDisplay = container.querySelector('.content-display')
      
      // Initially on v2, should show comprehensive content with markdown formatting
      expect(contentDisplay).toHaveTextContent('# Code Review Assistant')
      expect(contentDisplay).toHaveTextContent('Please conduct a comprehensive code review')
      
      // Switch to v1
      fireEvent.change(versionSelector, { target: { value: 'v1' } })
      expect(screen.getByText('Please review this code for basic issues and suggest improvements.')).toBeInTheDocument()
      expect(screen.getByText('v1 • 3 days ago')).toBeInTheDocument()
      
      // Switch to v3
      fireEvent.change(versionSelector, { target: { value: 'v3' } })
      expect(screen.getByText('Extended version with additional architectural review guidelines...')).toBeInTheDocument()
      expect(screen.getByText('v3 • 1 day ago')).toBeInTheDocument()
    })

    it('updates version info in content header when version changes', () => {
      const { container } = renderWithProviders(
        <PromptDetail 
          promptId={mockPromptId}
          onBack={mockOnBack}
          onVote={mockOnVote}
        />
      )

      const versionSelector = container.querySelector('.version-selector')
      
      // Switch to v1
      fireEvent.change(versionSelector, { target: { value: 'v1' } })
      expect(screen.getByText('v1 • 3 days ago')).toBeInTheDocument()
      
      // Switch back to v2
      fireEvent.change(versionSelector, { target: { value: 'v2' } })
      expect(screen.getByText('v2 • 2 days ago')).toBeInTheDocument()
    })

    it('handles version selector change events correctly', () => {
      const { container } = renderWithProviders(
        <PromptDetail 
          promptId={mockPromptId}
          onBack={mockOnBack}
          onVote={mockOnVote}
        />
      )

      const versionSelector = container.querySelector('.version-selector')
      
      // Test multiple version changes
      fireEvent.change(versionSelector, { target: { value: 'v3' } })
      expect(versionSelector.value).toBe('v3')
      
      fireEvent.change(versionSelector, { target: { value: 'v1' } })
      expect(versionSelector.value).toBe('v1')
      
      fireEvent.change(versionSelector, { target: { value: 'v2' } })
      expect(versionSelector.value).toBe('v2')
    })
  })

  describe('Vote Handling', () => {
    it('displays current vote count', () => {
      renderWithProviders(
        <PromptDetail 
          promptId={mockPromptId}
          onBack={mockOnBack}
          onVote={mockOnVote}
        />
      )

      expect(screen.getByText('47')).toBeInTheDocument()
    })

    it('renders vote buttons with correct SVG icons', () => {
      const { container } = renderWithProviders(
        <PromptDetail 
          promptId={mockPromptId}
          onBack={mockOnBack}
          onVote={mockOnVote}
        />
      )

      const voteButtons = container.querySelectorAll('.vote-button')
      expect(voteButtons).toHaveLength(2)
      
      // Check that both buttons have SVG elements
      voteButtons.forEach(button => {
        expect(button.querySelector('svg')).toBeInTheDocument()
      })
    })

    it('calls onVote with correct parameters when upvote is clicked', () => {
      const { container } = renderWithProviders(
        <PromptDetail 
          promptId={mockPromptId}
          onBack={mockOnBack}
          onVote={mockOnVote}
        />
      )

      const upvoteButton = container.querySelectorAll('.vote-button')[0]
      fireEvent.click(upvoteButton)

      expect(mockOnVote).toHaveBeenCalledWith(mockPromptId, 'up')
      expect(mockOnVote).toHaveBeenCalledTimes(1)
    })

    it('calls onVote with correct parameters when downvote is clicked', () => {
      const { container } = renderWithProviders(
        <PromptDetail 
          promptId={mockPromptId}
          onBack={mockOnBack}
          onVote={mockOnVote}
        />
      )

      const downvoteButton = container.querySelectorAll('.vote-button')[1]
      fireEvent.click(downvoteButton)

      expect(mockOnVote).toHaveBeenCalledWith(mockPromptId, 'down')
      expect(mockOnVote).toHaveBeenCalledTimes(1)
    })

    it('handles rapid vote clicks correctly', () => {
      const { container } = renderWithProviders(
        <PromptDetail 
          promptId={mockPromptId}
          onBack={mockOnBack}
          onVote={mockOnVote}
        />
      )

      const upvoteButton = container.querySelectorAll('.vote-button')[0]
      
      // Simulate rapid clicks
      fireEvent.click(upvoteButton)
      fireEvent.click(upvoteButton)
      fireEvent.click(upvoteButton)

      expect(mockOnVote).toHaveBeenCalledTimes(3)
      expect(mockOnVote).toHaveBeenCalledWith(mockPromptId, 'up')
    })

    it('shows correct vote state styling when user has not voted', () => {
      const { container } = renderWithProviders(
        <PromptDetail 
          promptId={mockPromptId}
          onBack={mockOnBack}
          onVote={mockOnVote}
        />
      )

      const voteButtons = container.querySelectorAll('.vote-button')
      voteButtons.forEach(button => {
        expect(button).not.toHaveClass('voted')
      })
    })

    it('shows vote action buttons (Save, Export, Follow)', () => {
      renderWithProviders(
        <PromptDetail 
          promptId={mockPromptId}
          onBack={mockOnBack}
          onVote={mockOnVote}
        />
      )

      expect(screen.getByText('Save')).toBeInTheDocument()
      expect(screen.getByText('Export')).toBeInTheDocument()
      expect(screen.getByText('Follow')).toBeInTheDocument()
    })
  })

  describe('Date Formatting', () => {
    beforeEach(() => {
      // Reset the mock before each test
      vi.restoreAllMocks()
    })

    it('formats ISO date strings correctly', () => {
      const { container } = renderWithProviders(
        <PromptDetail 
          promptId={mockPromptId}
          onBack={mockOnBack}
          onVote={mockOnVote}
        />
      )

      // Check that formatted dates are displayed (the formatDate function is called)
      expect(screen.getByText('Created:')).toBeInTheDocument()
      expect(screen.getByText('Updated:')).toBeInTheDocument()
      
      // Check that some date content exists in the metadata section
      const metadataSection = container.querySelector('.metadata-section')
      expect(metadataSection).toBeInTheDocument()
    })

    it('handles different date formats', () => {
      // Test that the formatDate function is called with proper parameters
      const { container } = renderWithProviders(
        <PromptDetail 
          promptId={mockPromptId}
          onBack={mockOnBack}
          onVote={mockOnVote}
        />
      )

      expect(container.querySelector('.metadata-section')).toBeInTheDocument()
    })

    it('displays relative time in version selector options', () => {
      const { container } = renderWithProviders(
        <PromptDetail 
          promptId={mockPromptId}
          onBack={mockOnBack}
          onVote={mockOnVote}
        />
      )

      const options = container.querySelectorAll('.version-selector option')
      expect(options[0]).toHaveTextContent('v1 (3 days ago)')
      expect(options[1]).toHaveTextContent('v2 (2 days ago)')
      expect(options[2]).toHaveTextContent('v3 (1 day ago)')
    })
  })

  describe('Navigation', () => {
    it('renders back button with correct text and icon', () => {
      const { container } = renderWithProviders(
        <PromptDetail 
          promptId={mockPromptId}
          onBack={mockOnBack}
          onVote={mockOnVote}
        />
      )

      const backButton = container.querySelector('.back-button')
      expect(backButton).toBeInTheDocument()
      expect(backButton).toHaveTextContent('Back to Library')
      expect(backButton.querySelector('svg')).toBeInTheDocument()
    })

    it('calls onBack when back button is clicked', () => {
      const { container } = renderWithProviders(
        <PromptDetail 
          promptId={mockPromptId}
          onBack={mockOnBack}
          onVote={mockOnVote}
        />
      )

      const backButton = container.querySelector('.back-button')
      fireEvent.click(backButton)

      expect(mockOnBack).toHaveBeenCalledTimes(1)
    })

    it('handles multiple back button clicks', () => {
      const { container } = renderWithProviders(
        <PromptDetail 
          promptId={mockPromptId}
          onBack={mockOnBack}
          onVote={mockOnVote}
        />
      )

      const backButton = container.querySelector('.back-button')
      
      fireEvent.click(backButton)
      fireEvent.click(backButton)
      fireEvent.click(backButton)

      expect(mockOnBack).toHaveBeenCalledTimes(3)
    })
  })

  describe('External Links', () => {
    it('renders external link with correct attributes', () => {
      const { container } = renderWithProviders(
        <PromptDetail 
          promptId={mockPromptId}
          onBack={mockOnBack}
          onVote={mockOnVote}
        />
      )

      const externalLink = container.querySelector('.open-prompt-button')
      expect(externalLink).toBeInTheDocument()
      expect(externalLink).toHaveAttribute('href', 'https://www.google.com')
      expect(externalLink).toHaveAttribute('target', '_blank')
      expect(externalLink).toHaveAttribute('rel', 'noopener noreferrer')
    })

    it('displays correct link text with platform name', () => {
      renderWithProviders(
        <PromptDetail 
          promptId={mockPromptId}
          onBack={mockOnBack}
          onVote={mockOnVote}
        />
      )

      expect(screen.getByText('Open prompt in Apollo Intelligence App')).toBeInTheDocument()
    })

    it('includes external link icon', () => {
      const { container } = renderWithProviders(
        <PromptDetail 
          promptId={mockPromptId}
          onBack={mockOnBack}
          onVote={mockOnVote}
        />
      )

      const externalLink = container.querySelector('.open-prompt-button')
      expect(externalLink.querySelector('svg')).toBeInTheDocument()
    })
  })

  describe('Status Badge Rendering', () => {
    it('renders status badge with correct text and class', () => {
      const { container } = renderWithProviders(
        <PromptDetail 
          promptId={mockPromptId}
          onBack={mockOnBack}
          onVote={mockOnVote}
        />
      )

      const statusBadge = container.querySelector('.apollo-badge')
      expect(statusBadge).toBeInTheDocument()
      expect(statusBadge).toHaveClass('apollo-badge--approved') // Published maps to approved variant
      expect(statusBadge).toHaveTextContent('[Quick Win (12)] Published')
    })

    it('applies correct CSS class based on status', () => {
      // Test the getStatusClass function behavior through rendering
      const { container } = renderWithProviders(
        <PromptDetail 
          promptId={mockPromptId}
          onBack={mockOnBack}
          onVote={mockOnVote}
        />
      )

      const statusBadge = container.querySelector('.apollo-badge')
      expect(statusBadge).toHaveClass('apollo-badge--approved') // Published maps to approved variant
    })

    it('displays status count in badge', () => {
      renderWithProviders(
        <PromptDetail 
          promptId={mockPromptId}
          onBack={mockOnBack}
          onVote={mockOnVote}
        />
      )

      expect(screen.getByText(/Quick Win \(12\)/)).toBeInTheDocument()
    })

    it('includes category and difficulty badges', () => {
      renderWithProviders(
        <PromptDetail 
          promptId={mockPromptId}
          onBack={mockOnBack}
          onVote={mockOnVote}
        />
      )

      expect(screen.getByText('Development')).toBeInTheDocument()
      expect(screen.getByText('Advanced')).toBeInTheDocument()
    })
  })

  describe('Content Sections', () => {
    it('displays Goal section with correct content', () => {
      renderWithProviders(
        <PromptDetail 
          promptId={mockPromptId}
          onBack={mockOnBack}
          onVote={mockOnVote}
        />
      )

      expect(screen.getByText('Goal')).toBeInTheDocument()
      expect(screen.getByText(/Enable Apollo teams to conduct thorough code reviews/)).toBeInTheDocument()
    })

    it('displays Context section with correct content', () => {
      renderWithProviders(
        <PromptDetail 
          promptId={mockPromptId}
          onBack={mockOnBack}
          onVote={mockOnVote}
        />
      )

      expect(screen.getByText('Context')).toBeInTheDocument()
      expect(screen.getByText(/Apollo portfolio companies frequently develop/)).toBeInTheDocument()
    })

    it('displays Problem section with correct content', () => {
      renderWithProviders(
        <PromptDetail 
          promptId={mockPromptId}
          onBack={mockOnBack}
          onVote={mockOnVote}
        />
      )

      expect(screen.getByText('Problem')).toBeInTheDocument()
      expect(screen.getByText(/Manual code reviews are inconsistent/)).toBeInTheDocument()
    })

    it('displays Solution section with correct content', () => {
      renderWithProviders(
        <PromptDetail 
          promptId={mockPromptId}
          onBack={mockOnBack}
          onVote={mockOnVote}
        />
      )

      expect(screen.getByText('Solution')).toBeInTheDocument()
      expect(screen.getByText(/Provide a comprehensive, structured framework/)).toBeInTheDocument()
    })

    it('renders overview grid with proper structure', () => {
      const { container } = renderWithProviders(
        <PromptDetail 
          promptId={mockPromptId}
          onBack={mockOnBack}
          onVote={mockOnVote}
        />
      )

      const overviewGrid = container.querySelector('.overview-grid')
      expect(overviewGrid).toBeInTheDocument()
      
      const overviewItems = container.querySelectorAll('.overview-item')
      expect(overviewItems).toHaveLength(4)
    })
  })

  describe('Input/Output Examples', () => {
    it('renders correct number of examples', () => {
      const { container } = renderWithProviders(
        <PromptDetail 
          promptId={mockPromptId}
          onBack={mockOnBack}
          onVote={mockOnVote}
        />
      )

      const exampleItems = container.querySelectorAll('.example-item')
      expect(exampleItems).toHaveLength(2)
    })

    it('displays example inputs correctly', () => {
      renderWithProviders(
        <PromptDetail 
          promptId={mockPromptId}
          onBack={mockOnBack}
          onVote={mockOnVote}
        />
      )

      expect(screen.getByText(/Review this Python API endpoint/)).toBeInTheDocument()
      expect(screen.getByText(/Evaluate this React component/)).toBeInTheDocument()
    })

    it('displays example outputs with formatting', () => {
      const { container } = renderWithProviders(
        <PromptDetail 
          promptId={mockPromptId}
          onBack={mockOnBack}
          onVote={mockOnVote}
        />
      )

      // Check that pre tags are used for formatted output
      const preElements = container.querySelectorAll('.example-text pre')
      expect(preElements.length).toBeGreaterThan(0)
    })

    it('renders example structure correctly', () => {
      const { container } = renderWithProviders(
        <PromptDetail 
          promptId={mockPromptId}
          onBack={mockOnBack}
          onVote={mockOnVote}
        />
      )

      const exampleItems = container.querySelectorAll('.example-item')
      expect(exampleItems).toHaveLength(2)
      
      exampleItems.forEach(item => {
        expect(item.querySelector('.example-content-two-column')).toBeInTheDocument()
        expect(item.querySelector('.example-input-column')).toBeInTheDocument()
        expect(item.querySelector('.example-output-column')).toBeInTheDocument()
      })
    })

    it('handles complex example content with markdown-like formatting', () => {
      renderWithProviders(
        <PromptDetail 
          promptId={mockPromptId}
          onBack={mockOnBack}
          onVote={mockOnVote}
        />
      )

      // Check for specific content from examples (using getAllByText since these appear in multiple places)
      expect(screen.getAllByText(/Security Analysis/).length).toBeGreaterThan(0)
      expect(screen.getAllByText(/Performance/).length).toBeGreaterThan(0)
      expect(screen.getAllByText(/Recommendations/).length).toBeGreaterThan(0)
    })
  })

  describe('Mock Data Handling', () => {
    it('uses mock data correctly for all prompt properties', () => {
      renderWithProviders(
        <PromptDetail 
          promptId={mockPromptId}
          onBack={mockOnBack}
          onVote={mockOnVote}
        />
      )

      // Verify key mock data is displayed
      expect(screen.getByText('Advanced Code Review Assistant')).toBeInTheDocument()
      expect(screen.getByText('Sarah Chen • 2 days ago')).toBeInTheDocument()
      expect(screen.getByText('Development')).toBeInTheDocument()
      expect(screen.getByText('Advanced')).toBeInTheDocument()
      expect(screen.getByText('47')).toBeInTheDocument() // vote count
    })

    it('handles mock data tags correctly', () => {
      renderWithProviders(
        <PromptDetail 
          promptId={mockPromptId}
          onBack={mockOnBack}
          onVote={mockOnVote}
        />
      )

      expect(screen.getByText('#code-review')).toBeInTheDocument()
      expect(screen.getByText('#security')).toBeInTheDocument()
      expect(screen.getByText('#performance')).toBeInTheDocument()
      expect(screen.getByText('#best-practices')).toBeInTheDocument()
    })

    it('displays mock metadata correctly', () => {
      renderWithProviders(
        <PromptDetail 
          promptId={mockPromptId}
          onBack={mockOnBack}
          onVote={mockOnVote}
        />
      )

      expect(screen.getByText('Claude-3.5-Sonnet')).toBeInTheDocument()
      expect(screen.getByText('0.7')).toBeInTheDocument()
      expect(screen.getByText('4000')).toBeInTheDocument()
      expect(screen.getByText('Apollo Intelligence App')).toBeInTheDocument()
    })

    it('handles mock data versions array correctly', () => {
      const { container } = renderWithProviders(
        <PromptDetail 
          promptId={mockPromptId}
          onBack={mockOnBack}
          onVote={mockOnVote}
        />
      )

      const options = container.querySelectorAll('.version-selector option')
      expect(options).toHaveLength(3)
      
      // Check that all versions from mock data are present
      const versionTexts = Array.from(options).map(option => option.textContent)
      expect(versionTexts).toContain('v1 (3 days ago)')
      expect(versionTexts).toContain('v2 (2 days ago)')
      expect(versionTexts).toContain('v3 (1 day ago)')
    })

    it('handles nested mock data structure for examples', () => {
      renderWithProviders(
        <PromptDetail 
          promptId={mockPromptId}
          onBack={mockOnBack}
          onVote={mockOnVote}
        />
      )

      // Verify complex nested example data is rendered
      expect(screen.getByText(/Review this Python API endpoint/)).toBeInTheDocument()
      expect(screen.getByText(/Evaluate this React component/)).toBeInTheDocument()
    })

    it('uses promptId parameter correctly in vote handlers', () => {
      const customPromptId = 'custom-test-id'
      const { container } = renderWithProviders(
        <PromptDetail 
          promptId={customPromptId}
          onBack={mockOnBack}
          onVote={mockOnVote}
        />
      )

      const upvoteButton = container.querySelectorAll('.vote-button')[0]
      fireEvent.click(upvoteButton)

      expect(mockOnVote).toHaveBeenCalledWith(customPromptId, 'up')
    })
  })

  describe('Component State Management', () => {
    it('manages selected version state correctly', () => {
      const { container } = renderWithProviders(
        <PromptDetail 
          promptId={mockPromptId}
          onBack={mockOnBack}
          onVote={mockOnVote}
        />
      )

      const versionSelector = container.querySelector('.version-selector')
      
      // Initial state should be v2
      expect(versionSelector.value).toBe('v2')
      
      // Change to v1
      fireEvent.change(versionSelector, { target: { value: 'v1' } })
      expect(versionSelector.value).toBe('v1')
      
      // Change to v3
      fireEvent.change(versionSelector, { target: { value: 'v3' } })
      expect(versionSelector.value).toBe('v3')
    })

    it('updates content display when version state changes', () => {
      const { container } = renderWithProviders(
        <PromptDetail 
          promptId={mockPromptId}
          onBack={mockOnBack}
          onVote={mockOnVote}
        />
      )

      const versionSelector = container.querySelector('.version-selector')
      
      // Switch to v1 and verify content changes
      fireEvent.change(versionSelector, { target: { value: 'v1' } })
      expect(screen.getByText('Please review this code for basic issues and suggest improvements.')).toBeInTheDocument()
      
      // Switch to v3 and verify content changes
      fireEvent.change(versionSelector, { target: { value: 'v3' } })
      expect(screen.getByText('Extended version with additional architectural review guidelines...')).toBeInTheDocument()
    })
  })

  describe('Edge Cases and Error Handling', () => {
    it('handles missing version gracefully', () => {
      const { container } = renderWithProviders(
        <PromptDetail 
          promptId={mockPromptId}
          onBack={mockOnBack}
          onVote={mockOnVote}
        />
      )

      // Even if we try to set an invalid version, it should fall back to a valid one
      const versionSelector = container.querySelector('.version-selector')
      fireEvent.change(versionSelector, { target: { value: 'invalid-version' } })
      
      // Should still display content (falls back to v2)
      expect(container.querySelector('.content-display')).toBeInTheDocument()
    })

    it('displays platform name in multiple locations consistently', () => {
      renderWithProviders(
        <PromptDetail 
          promptId={mockPromptId}
          onBack={mockOnBack}
          onVote={mockOnVote}
        />
      )

      // Platform name should appear in both external link and metadata
      const apolloReferences = screen.getAllByText(/Apollo Intelligence App/)
      expect(apolloReferences.length).toBeGreaterThan(1)
    })

    it('handles vote state when user has not voted', () => {
      const { container } = renderWithProviders(
        <PromptDetail 
          promptId={mockPromptId}
          onBack={mockOnBack}
          onVote={mockOnVote}
        />
      )

      // Check that vote buttons are not in voted state (userVote is null in mock)
      const voteButtons = container.querySelectorAll('.vote-button')
      voteButtons.forEach(button => {
        expect(button).not.toHaveClass('voted')
      })
    })
  })

  describe('DOM Structure and CSS Classes', () => {
    it('has correct main DOM structure', () => {
      const { container } = renderWithProviders(
        <PromptDetail 
          promptId={mockPromptId}
          onBack={mockOnBack}
          onVote={mockOnVote}
        />
      )

      expect(container.querySelector('.prompt-detail')).toBeInTheDocument()
      expect(container.querySelector('.detail-header')).toBeInTheDocument()
      expect(container.querySelector('.detail-content')).toBeInTheDocument()
      expect(container.querySelector('.detail-main')).toBeInTheDocument()
      expect(container.querySelector('.detail-vote-section')).toBeInTheDocument()
    })

    it('applies correct CSS classes to major sections', () => {
      const { container } = renderWithProviders(
        <PromptDetail 
          promptId={mockPromptId}
          onBack={mockOnBack}
          onVote={mockOnVote}
        />
      )

      expect(container.querySelector('.detail-title-section')).toBeInTheDocument()
      expect(container.querySelector('.version-section')).toBeInTheDocument()
      expect(container.querySelector('.prompt-overview')).toBeInTheDocument()
      expect(container.querySelector('.prompt-content')).toBeInTheDocument()
      expect(container.querySelector('.metadata-section')).toBeInTheDocument()
      expect(container.querySelector('.examples-section')).toBeInTheDocument()
    })

    it('applies correct CSS classes to interactive elements', () => {
      const { container } = renderWithProviders(
        <PromptDetail 
          promptId={mockPromptId}
          onBack={mockOnBack}
          onVote={mockOnVote}
        />
      )

      expect(container.querySelector('.back-button')).toBeInTheDocument()
      expect(container.querySelector('.version-selector')).toBeInTheDocument()
      expect(container.querySelector('.open-prompt-button')).toBeInTheDocument()
      expect(container.querySelectorAll('.vote-button')).toHaveLength(2)
      expect(container.querySelectorAll('.action-button')).toHaveLength(3)
    })
  })
})