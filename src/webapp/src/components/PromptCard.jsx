import './PromptCard.css'
import { Badge } from './atoms'
import { useVote } from '../hooks/useVote'

function PromptCard({ prompt, onVote, onCardClick }) {
  const getStatusVariant = (status) => {
    switch(status.toLowerCase()) {
      case 'approved': return 'approved'
      case 'under review': return 'review'
      case 'in development': return 'development'
      case 'planned': return 'planned'
      case 'published': return 'approved'
      case 'suggested': return 'suggested'
      default: return 'default'
    }
  }

  // Use the vote hook
  const {
    votes,
    userVote,
    isVoting,
    hasUserVoted,
    error,
    handleVote: handleVoteInternal,
    clearError
  } = useVote(
    prompt.id,
    prompt.votes,
    prompt.userVote,
    onVote // Pass the callback to parent
  )

  const handleVote = async (direction, e) => {
    e.stopPropagation()
    
    try {
      clearError() // Clear any previous errors
      await handleVoteInternal(direction)
    } catch (error) {
      if (error.code === 'AUTH_REQUIRED') {
        // Handle authentication required (e.g., show login modal)
        console.log('Authentication required for voting')
      }
      // Error is already set in the hook state
    }
  }

  const handleCardClick = () => {
    onCardClick(prompt.id)
  }

  return (
    <div className="prompt-card" onClick={handleCardClick} data-testid={`prompt-card-${prompt.id}`}>
      <div className="vote-section">
        <div 
          className={`vote-count ${hasUserVoted ? 'voted' : ''} ${isVoting ? 'voting' : ''}`}
          onClick={(e) => handleVote('toggle', e)}
          data-testid={`vote-button-${prompt.id}`}
          style={{ 
            opacity: isVoting ? 0.6 : 1,
            cursor: isVoting ? 'not-allowed' : 'pointer'
          }}
        >
          <span data-testid={`prompt-votes-${prompt.id}`}>{votes}</span>
        </div>
        {error && (
          <div className="vote-error" style={{ 
            color: '#dc2626', 
            fontSize: '12px', 
            marginTop: '4px',
            textAlign: 'center'
          }}>
            {error}
          </div>
        )}
      </div>
      
      <div className="card-content">
        <h3 className="card-title">{prompt.title}</h3>
        <p className="card-description">{prompt.description}</p>
        
        <div className="card-meta">
          <span className="author">{prompt.author} • {prompt.timeAgo}</span>
          <span className="category-badge">{prompt.category}</span>
          {prompt.difficulty && <span className="difficulty-badge">{prompt.difficulty}</span>}
        </div>
        
        <div className="card-footer">
          <div className="tags">
            {prompt.tags.map(tag => (
              <span key={tag} className="tag">#{tag}</span>
            ))}
          </div>
          <div className="card-footer-right">
            <div className="comment-section">
              <svg width="16" height="16" viewBox="0 0 16 16" fill="none" className="comment-icon">
                <path d="M14 2H2C1.4 2 1 2.4 1 3V11C1 11.6 1.4 12 2 12H3V15L6 12H14C14.6 12 15 11.6 15 11V3C15 2.4 14.6 2 14 2Z" stroke="currentColor" strokeWidth="1.5" fill="none"/>
              </svg>
              <span className="comment-count">{prompt.comments || 0}</span>
            </div>
            <Badge variant={getStatusVariant(prompt.status)} size="sm">
              {prompt.status}
            </Badge>
          </div>
        </div>
      </div>
    </div>
  )
}

export default PromptCard