import React, { useState } from 'react';
import { promptsAPI, categoriesAPI } from '../api/client';
import { useAuth } from '../auth/AuthContext';
import { Loading } from './atoms';
import './NewPromptModal.css';

const NewPromptModal = ({ isOpen, onClose, onPromptCreated }) => {
  const { user, isAuthenticated } = useAuth();
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    content: '',
    category: '',
    tags: '',
    status: 'Suggested',
    difficulty: 'Intermediate'
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [categories, setCategories] = useState([]);

  // Load categories when modal opens
  React.useEffect(() => {
    if (isOpen) {
      categoriesAPI.getAll().then(setCategories).catch(console.error);
    }
  }, [isOpen]);

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!isAuthenticated) {
      setError('You must be logged in to create prompts');
      return;
    }

    setLoading(true);
    setError('');

    try {
      const promptData = {
        ...formData,
        tags: formData.tags.split(',').map(tag => tag.trim()).filter(Boolean)
      };

      const newPrompt = await promptsAPI.create(promptData, user.id);
      onPromptCreated?.(newPrompt);
      onClose();
      
      // Reset form
      setFormData({
        title: '',
        description: '',
        content: '',
        category: '',
        tags: '',
        status: 'Suggested',
        difficulty: 'Intermediate'
      });
    } catch (error) {
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  if (!isOpen) return null;

  return (
    <div className="new-prompt-modal-overlay" onClick={onClose}>
      <div className="new-prompt-modal" onClick={e => e.stopPropagation()}>
        <div className="modal-header">
          <h2>Create New Prompt</h2>
          <button className="close-button" onClick={onClose}>×</button>
        </div>
        
        <form onSubmit={handleSubmit} className="new-prompt-form">
          <div className="form-group">
            <label htmlFor="title">Title *</label>
            <input
              type="text"
              id="title"
              name="title"
              value={formData.title}
              onChange={handleChange}
              placeholder="Enter prompt title"
              required
              maxLength={200}
            />
          </div>

          <div className="form-group">
            <label htmlFor="description">Description *</label>
            <textarea
              id="description"
              name="description"
              value={formData.description}
              onChange={handleChange}
              placeholder="Brief description of what this prompt does"
              required
              rows={3}
              maxLength={500}
            />
          </div>

          <div className="form-group">
            <label htmlFor="content">Prompt Content *</label>
            <textarea
              id="content"
              name="content"
              value={formData.content}
              onChange={handleChange}
              placeholder="Enter the detailed prompt content here..."
              required
              rows={8}
            />
          </div>

          <div className="form-row">
            <div className="form-group">
              <label htmlFor="category">Category *</label>
              <select
                id="category"
                name="category"
                value={formData.category}
                onChange={handleChange}
                required
              >
                <option value="">Select a category</option>
                {categories.map(cat => (
                  <option key={cat.name} value={cat.name}>{cat.name}</option>
                ))}
              </select>
            </div>

            <div className="form-group">
              <label htmlFor="difficulty">Difficulty</label>
              <select
                id="difficulty"
                name="difficulty"
                value={formData.difficulty}
                onChange={handleChange}
              >
                <option value="Beginner">Beginner</option>
                <option value="Intermediate">Intermediate</option>
                <option value="Advanced">Advanced</option>
              </select>
            </div>
          </div>

          <div className="form-row">
            <div className="form-group">
              <label htmlFor="status">Status</label>
              <select
                id="status"
                name="status"
                value={formData.status}
                onChange={handleChange}
              >
                <option value="Suggested">Suggested</option>
                <option value="Approved">Approved</option>
                <option value="In Development">In Development</option>
                <option value="Under Review">Under Review</option>
                <option value="Published">Published</option>
              </select>
            </div>

            <div className="form-group">
              <label htmlFor="tags">Tags</label>
              <input
                type="text"
                id="tags"
                name="tags"
                value={formData.tags}
                onChange={handleChange}
                placeholder="Comma-separated tags (e.g., Analysis, Strategy, Due Diligence)"
              />
            </div>
          </div>

          {error && <div className="error-message">{error}</div>}

          <div className="modal-actions">
            <button type="button" onClick={onClose} className="cancel-button" disabled={loading}>
              Cancel
            </button>
            <button type="submit" className="create-button" disabled={loading}>
              {loading && <Loading variant="spinner" size="xs" color="secondary" className="apollo-loading--button" />}
              {loading ? 'Creating...' : 'Create Prompt'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default NewPromptModal;