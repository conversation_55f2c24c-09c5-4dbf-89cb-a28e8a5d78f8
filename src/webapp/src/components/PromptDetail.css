/* Prompt Detail Component */
.prompt-detail {
  background-color: #1F2025;
  height: 100vh;
  display: flex;
  flex-direction: column;
  font-family: 'Segoe UI', sans-serif;
}

/* Header */
.detail-header {
  padding: 16px 24px;
  border-bottom: 1px solid rgba(255,255,255,0.05);
  background-color: #1F2025;
}

.back-button {
  display: flex;
  align-items: center;
  gap: 8px;
  background: none;
  border: none;
  color: #808080;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  padding: 8px 12px;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.back-button:hover {
  color: #e0e0e0;
  background-color: rgba(255,255,255,0.05);
}

.back-button svg {
  stroke: currentColor;
}

/* Main Content */
.detail-content {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.detail-main {
  flex: 1;
  overflow-y: auto;
  padding: 32px 40px;
}

/* Title Section */
.detail-title-section {
  margin-bottom: 32px;
}

.detail-title {
  font-size: 32px;
  font-weight: 700;
  color: #e0e0e0;
  margin: 0 0 12px 0;
  line-height: 1.2;
}

.detail-description {
  font-size: 16px;
  color: #b0b0b0;
  line-height: 1.6;
  margin-bottom: 20px;
}

.detail-meta {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
  margin-bottom: 16px;
}

.detail-author {
  font-size: 14px;
  color: #808080;
}

.category-badge {
  font-size: 12px;
  background: rgba(255,255,255,0.05);
  color: #b0b0b0;
  padding: 4px 8px;
  border-radius: 4px;
  border: 1px solid rgba(255,255,255,0.1);
}

.difficulty-badge {
  font-size: 12px;
  background: rgba(0, 123, 99, 0.1);
  color: #007B63;
  padding: 4px 8px;
  border-radius: 4px;
  border: 1px solid rgba(139, 92, 246, 0.2);
}

.status-badge {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  white-space: nowrap;
}

.status-approved {
  background: rgba(16, 185, 129, 0.1);
  color: #10b981;
}

.status-planned {
  background: rgba(59, 130, 246, 0.1);
  color: #3b82f6;
}

.status-review {
  background: rgba(245, 158, 11, 0.1);
  color: #f59e0b;
}

.status-published {
  background: rgba(0, 123, 99, 0.1);
  color: #007B63;
}

.status-default {
  background: rgba(255,255,255,0.05);
  color: #808080;
}

.detail-tags {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.tag {
  background: rgba(0, 123, 99, 0.1);
  color: #007B63;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 13px;
  font-weight: 500;
}

/* Version Section */
.version-section {
  margin-bottom: 32px;
  display: flex;
  align-items: center;
  gap: 12px;
}

.version-label {
  font-size: 14px;
  font-weight: 600;
  color: #e0e0e0;
}

.version-selector {
  padding: 8px 12px;
  border: 1px solid rgba(255,255,255,0.1);
  border-radius: 6px;
  background-color: #282A2C;
  color: #e0e0e0;
  font-size: 14px;
  cursor: pointer;
  min-width: 200px;
}

.version-selector:focus {
  outline: none;
  border-color: #007B63;
  box-shadow: 0 0 0 2px rgba(0, 123, 99, 0.25);
}

.version-selector option {
  background-color: #282A2C;
  color: #e0e0e0;
}

/* Open Prompt Button */
.open-prompt-section {
  margin: 20px 0;
}

.open-prompt-button {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  background-color: #007B63;
  color: white;
  text-decoration: none;
  padding: 12px 20px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  transition: background-color 0.2s;
}

.open-prompt-button:hover {
  background-color: #006653;
  color: white;
  text-decoration: none;
}

.open-prompt-button svg {
  stroke: currentColor;
}

/* Overview Section */
.prompt-overview {
  margin-bottom: 32px;
  background: #282A2C;
  border: 1px solid rgba(255,255,255,0.05);
  border-radius: 12px;
  padding: 24px;
}

.overview-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
}

.overview-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.overview-label {
  font-size: 16px;
  font-weight: 600;
  color: #007B63;
  margin: 0;
}

.overview-text {
  font-size: 14px;
  line-height: 1.6;
  color: #e0e0e0;
  margin: 0;
}

/* Prompt Content */
.prompt-content {
  margin-bottom: 32px;
}

.content-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.content-header h3 {
  font-size: 20px;
  font-weight: 600;
  color: #e0e0e0;
  margin: 0;
}

.version-info {
  font-size: 14px;
  color: #808080;
  background: rgba(255,255,255,0.05);
  padding: 4px 8px;
  border-radius: 4px;
}

.content-display {
  background: #282A2C;
  border: 1px solid rgba(255,255,255,0.05);
  border-radius: 12px;
  padding: 24px;
  position: relative;
}

.content-text {
  font-family: 'Segoe UI', 'Monaco', 'Consolas', monospace;
  font-size: 14px;
  line-height: 1.6;
  color: #e0e0e0;
  white-space: pre-wrap;
  word-wrap: break-word;
  margin: 0;
  max-height: 500px;
  overflow-y: auto;
}

/* Metadata Section */
.metadata-section {
  margin-bottom: 32px;
}

.metadata-section h3 {
  font-size: 20px;
  font-weight: 600;
  color: #e0e0e0;
  margin: 0 0 16px 0;
}

.metadata-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
  background: #282A2C;
  border: 1px solid rgba(255,255,255,0.05);
  border-radius: 12px;
  padding: 24px;
}

.metadata-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid rgba(255,255,255,0.05);
}

.metadata-item:last-child {
  border-bottom: none;
}

.metadata-label {
  font-size: 14px;
  font-weight: 500;
  color: #b0b0b0;
}

.metadata-value {
  font-size: 14px;
  color: #e0e0e0;
  font-weight: 500;
}

/* Vote Section */
.detail-vote-section {
  width: 280px;
  background-color: #282A2C;
  border-left: 1px solid rgba(255,255,255,0.05);
  padding: 32px 24px;
  display: flex;
  flex-direction: column;
  gap: 32px;
}

.vote-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 24px;
  background: rgba(255,255,255,0.02);
  border-radius: 12px;
  border: 1px solid rgba(255,255,255,0.05);
}

.vote-button.large {
  background: none;
  border: none;
  color: #808080;
  cursor: pointer;
  padding: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  border-radius: 8px;
  width: 48px;
  height: 48px;
}

.vote-button.large:hover {
  color: #007B63;
  background: rgba(0, 123, 99, 0.1);
}

.vote-button.large.voted {
  color: #007B63;
  background: rgba(139, 92, 246, 0.15);
}

.vote-count.large {
  font-size: 24px;
  font-weight: 700;
  color: #e0e0e0;
  text-align: center;
  padding: 8px 0;
}

.vote-actions {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.action-button {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  background: rgba(255,255,255,0.05);
  border: 1px solid rgba(255,255,255,0.1);
  border-radius: 8px;
  color: #b0b0b0;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
  width: 100%;
  justify-content: flex-start;
}

.action-button:hover {
  background: rgba(255,255,255,0.08);
  color: #e0e0e0;
  border-color: rgba(255,255,255,0.15);
}

.action-button svg {
  stroke: currentColor;
  flex-shrink: 0;
}

/* Examples Section */
.examples-section {
  margin-bottom: 32px;
}

.examples-section h3 {
  font-size: 20px;
  font-weight: 600;
  color: #e0e0e0;
  margin: 0 0 16px 0;
}

.examples-container {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.example-item {
  background: #282A2C;
  border: 1px solid rgba(255,255,255,0.05);
  border-radius: 12px;
  padding: 24px;
}

.example-header {
  margin-bottom: 16px;
}

.example-header h4 {
  font-size: 16px;
  font-weight: 600;
  color: #007B63;
  margin: 0;
}

.example-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.example-content-two-column {
  display: grid;
  grid-template-columns: 1fr 1px 1fr;
  gap: 24px;
  align-items: start;
}

.example-input-column,
.example-output-column {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.example-divider {
  width: 1px;
  background: rgba(255, 255, 255, 0.1);
  min-height: 200px;
  opacity: 0.5;
}

.example-input,
.example-output {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.example-input h5,
.example-output h5,
.example-input-column h5,
.example-output-column h5 {
  font-size: 14px;
  font-weight: 600;
  color: #b0b0b0;
  margin: 0;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.example-text {
  background: rgba(255,255,255,0.02);
  border: 1px solid rgba(255,255,255,0.05);
  border-radius: 8px;
  padding: 16px;
  font-size: 14px;
  line-height: 1.6;
  color: #e0e0e0;
}

.example-text pre {
  margin: 0;
  white-space: pre-wrap;
  word-wrap: break-word;
  font-family: 'Segoe UI', 'Monaco', 'Consolas', monospace;
}

/* Scrollbar styling for content areas */
.detail-main::-webkit-scrollbar,
.content-text::-webkit-scrollbar {
  width: 8px;
}

.detail-main::-webkit-scrollbar-track,
.content-text::-webkit-scrollbar-track {
  background: #1F2025;
}

.detail-main::-webkit-scrollbar-thumb,
.content-text::-webkit-scrollbar-thumb {
  background: #4a4a4a;
  border-radius: 4px;
}

.detail-main::-webkit-scrollbar-thumb:hover,
.content-text::-webkit-scrollbar-thumb:hover {
  background: #5a5a5a;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .detail-main {
    padding: 24px 32px;
  }
  
  .detail-vote-section {
    width: 240px;
    padding: 24px 20px;
  }
}

@media (max-width: 1024px) {
  .detail-content {
    flex-direction: column;
  }
  
  .detail-vote-section {
    width: 100%;
    border-left: none;
    border-top: 1px solid rgba(255,255,255,0.05);
    padding: 20px 24px;
  }
  
  .vote-container {
    flex-direction: row;
    justify-content: center;
    gap: 16px;
  }
  
  .vote-actions {
    flex-direction: row;
    justify-content: center;
    flex-wrap: wrap;
    gap: 8px;
  }
  
  .action-button {
    flex: 1;
    min-width: 120px;
  }
}

@media (max-width: 768px) {
  .prompt-detail {
    padding-top: 60px; /* Account for mobile menu button */
  }
  
  .detail-header {
    padding: 12px 16px;
  }
  
  .detail-main {
    padding: 20px 16px;
  }
  
  .detail-title {
    font-size: 24px;
    line-height: 1.2;
  }
  
  .detail-description {
    font-size: 15px;
  }
  
  .metadata-grid {
    grid-template-columns: 1fr;
    padding: 16px;
  }
  
  .detail-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .overview-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .content-display {
    padding: 16px;
  }
  
  .content-text {
    font-size: 13px;
    max-height: 400px;
  }
  
  .version-selector {
    width: 100%;
    min-width: auto;
  }
  
  .detail-vote-section {
    padding: 16px;
  }
  
  .vote-actions {
    gap: 6px;
  }
  
  .action-button {
    font-size: 13px;
    padding: 10px 12px;
    min-width: 100px;
  }
}

@media (max-width: 480px) {
  .detail-main {
    padding: 16px 12px;
  }
  
  .detail-title {
    font-size: 20px;
  }
  
  .detail-description {
    font-size: 14px;
  }
  
  .overview-grid,
  .metadata-grid {
    padding: 12px;
  }
  
  .content-display {
    padding: 12px;
  }
  
  .content-text {
    font-size: 12px;
  }
  
  .open-prompt-button {
    padding: 10px 16px;
    font-size: 14px;
    width: 100%;
    justify-content: center;
  }
  
  .vote-actions {
    flex-direction: column;
  }
  
  .action-button {
    width: 100%;
    min-width: auto;
  }
}