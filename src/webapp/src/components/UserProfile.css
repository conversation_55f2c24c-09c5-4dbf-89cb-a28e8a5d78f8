/* User Profile Modal Styles */
.profile-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  backdrop-filter: blur(8px);
}

.profile-modal {
  background: white;
  border-radius: 16px;
  width: 100%;
  max-width: 800px;
  max-height: 90vh;
  overflow: hidden;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
  animation: modalSlideIn 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
  border: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  flex-direction: column;
}

/* Header */
.profile-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 32px;
  position: relative;
}

.profile-header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.profile-avatar-section {
  display: flex;
  gap: 20px;
  align-items: center;
}

.profile-avatar {
  width: 80px;
  height: 80px;
  border-radius: 40px;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  font-weight: 700;
  border: 3px solid rgba(255, 255, 255, 0.3);
  flex-shrink: 0;
}

.profile-info h2 {
  margin: 0 0 8px 0;
  font-size: 28px;
  font-weight: 700;
  font-family: 'AGaramondPro-Bold', Georgia, serif;
}

.profile-role {
  margin: 0 0 4px 0;
  font-size: 16px;
  opacity: 0.9;
  font-weight: 500;
}

.profile-email {
  margin: 0;
  font-size: 14px;
  opacity: 0.8;
}

.close-button {
  background: rgba(255, 255, 255, 0.1);
  border: none;
  color: white;
  cursor: pointer;
  padding: 12px;
  border-radius: 8px;
  transition: background-color 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-button:hover {
  background: rgba(255, 255, 255, 0.2);
}

/* Tabs */
.profile-tabs {
  display: flex;
  background: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
}

.profile-tab {
  flex: 1;
  background: none;
  border: none;
  padding: 16px 24px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  color: #64748b;
  border-bottom: 2px solid transparent;
}

.profile-tab.active {
  color: #667eea;
  border-bottom-color: #667eea;
  background: white;
}

.profile-tab:hover:not(.active) {
  color: #1a1a1a;
  background: #f1f5f9;
}

/* Content */
.profile-content {
  flex: 1;
  overflow-y: auto;
  padding: 32px;
}

/* Section Headers */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e2e8f0;
}

.section-header h3 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #1a1a1a;
}

/* Buttons */
.edit-button {
  background: #667eea;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.2s;
  display: flex;
  align-items: center;
  gap: 6px;
}

.edit-button:hover {
  background: #5a67d8;
}

.edit-actions {
  display: flex;
  gap: 8px;
}

.save-button {
  background: #10b981;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.save-button:hover {
  background: #059669;
}

.cancel-button {
  background: #6b7280;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.cancel-button:hover {
  background: #4b5563;
}

/* Profile Form */
.profile-form {
  margin-top: 16px;
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 24px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-group label {
  font-weight: 500;
  color: #374151;
  font-size: 14px;
}

.form-group input,
.form-group select {
  padding: 12px 16px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 14px;
  transition: border-color 0.2s;
  font-family: 'Segoe UI', -apple-system, sans-serif;
}

.form-group input:focus,
.form-group select:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-value {
  padding: 12px 0;
  color: #1a1a1a;
  font-size: 14px;
}

/* Security Section */
.security-section {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

.security-item {
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 24px;
}

.security-item h4 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #1a1a1a;
}

.password-form {
  display: flex;
  flex-direction: column;
  gap: 16px;
  max-width: 400px;
}

.change-password-button {
  background: #667eea;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
  align-self: flex-start;
}

.change-password-button:hover:not(:disabled) {
  background: #5a67d8;
}

.change-password-button:disabled {
  background: #d1d5db;
  cursor: not-allowed;
}

.security-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.security-option-info p {
  margin: 0 0 4px 0;
  color: #374151;
}

.security-status {
  font-size: 12px;
  color: #6b7280;
  background: #f3f4f6;
  padding: 2px 8px;
  border-radius: 12px;
}

.configure-button {
  background: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s;
}

.configure-button:hover {
  background: #e5e7eb;
  border-color: #9ca3af;
}

.sessions-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.session-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background: #f8fafc;
  border-radius: 8px;
}

.session-device {
  font-weight: 500;
  color: #1a1a1a;
  margin-bottom: 2px;
}

.session-details {
  font-size: 12px;
  color: #6b7280;
}

.session-current {
  background: #10b981;
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: 600;
  text-transform: uppercase;
}

.danger-zone {
  border-color: #fecaca;
  background: #fef2f2;
}

.danger-zone h4 {
  color: #dc2626;
}

.danger-actions {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.logout-all-button,
.delete-account-button {
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s;
}

.logout-all-button {
  background: #f59e0b;
  color: white;
  border: none;
}

.logout-all-button:hover {
  background: #d97706;
}

.delete-account-button {
  background: none;
  color: #dc2626;
  border: 1px solid #dc2626;
}

.delete-account-button:hover {
  background: #dc2626;
  color: white;
}

/* Preferences Section */
.preferences-section {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.preference-group {
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 24px;
}

.preference-group h4 {
  margin: 0 0 20px 0;
  font-size: 16px;
  font-weight: 600;
  color: #1a1a1a;
}

.preference-item {
  margin-bottom: 20px;
}

.preference-item:last-child {
  margin-bottom: 0;
}

.preference-label {
  display: flex;
  align-items: center;
  gap: 12px;
  cursor: pointer;
  font-weight: 500;
  color: #1a1a1a;
  margin-bottom: 4px;
}

.preference-label input[type="checkbox"] {
  display: none;
}

.checkbox-custom {
  width: 20px;
  height: 20px;
  border: 2px solid #d1d5db;
  border-radius: 4px;
  position: relative;
  transition: all 0.2s;
}

.preference-label input[type="checkbox"]:checked + .checkbox-custom {
  background: #667eea;
  border-color: #667eea;
}

.preference-label input[type="checkbox"]:checked + .checkbox-custom::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 12px;
  font-weight: 600;
}

.preference-item p {
  margin: 0;
  color: #6b7280;
  font-size: 14px;
  line-height: 1.4;
}

.preference-item label:not(.preference-label) {
  display: block;
  font-weight: 500;
  color: #374151;
  margin-bottom: 8px;
}

.preference-select {
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  background: white;
  cursor: pointer;
  transition: border-color 0.2s;
}

.preference-select:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* Responsive Design */
@media (max-width: 768px) {
  .profile-modal {
    max-width: 95%;
    margin: 20px;
    max-height: 95vh;
  }
  
  .profile-header {
    padding: 24px;
  }
  
  .profile-avatar-section {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
  
  .profile-avatar {
    width: 60px;
    height: 60px;
    font-size: 20px;
  }
  
  .profile-info h2 {
    font-size: 24px;
  }
  
  .profile-content {
    padding: 24px;
  }
  
  .form-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .profile-tabs {
    overflow-x: auto;
  }
  
  .profile-tab {
    white-space: nowrap;
    min-width: 120px;
  }
  
  .security-option {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .danger-actions {
    flex-direction: column;
  }
  
  .danger-actions button {
    width: 100%;
  }
}