import { useState, useEffect, useRef } from 'react'
import { useAuth } from '../auth/AuthContext'
import { usePermissions } from '../hooks/usePermissions'
import { Icon, Button, FilterSelect, Avatar } from './atoms'
import './TwoTierSidebar.css'

function TwoTierSidebar({ statuses, categories, selectedStatus, setSelectedStatus, selectedCategory, setSelectedCategory, onLoginClick, onNewPromptClick, onExportClick, onClearCache, onHomeClick, onChatClick, onPeopleClick, onContentClick, onAppsClick, isMobileOpen, setIsMobileOpen }) {
  const { user, isAuthenticated, logout } = useAuth()
  const { getRoleDisplayName } = usePermissions()
  const [showUserMenu, setShowUserMenu] = useState(false)
  const userMenuRef = useRef(null)

  // Close mobile menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (isMobileOpen && !event.target.closest('.sidebar-container') && !event.target.closest('.mobile-menu-button')) {
        setIsMobileOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [isMobileOpen])

  // Close mobile menu on escape key
  useEffect(() => {
    const handleEscape = (event) => {
      if (event.key === 'Escape' && isMobileOpen) {
        setIsMobileOpen(false)
      }
    }

    document.addEventListener('keydown', handleEscape)
    return () => document.removeEventListener('keydown', handleEscape)
  }, [isMobileOpen])

  // Close user menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (userMenuRef.current && !userMenuRef.current.contains(event.target)) {
        setShowUserMenu(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  const handleLogout = async () => {
    setShowUserMenu(false)
    await logout()
  }

  return (
    <>
      {/* Mobile menu button */}
      <Button 
        variant="ghost"
        size="sm"
        className="mobile-menu-button"
        onClick={() => setIsMobileOpen(!isMobileOpen)}
        aria-label="Open sidebar menu"
      >
        <Icon name="menu" size={20} />
      </Button>

      {/* Mobile overlay */}
      <div className={`sidebar-overlay ${isMobileOpen ? 'active' : ''}`} />

      <div className={`sidebar-container ${isMobileOpen ? 'mobile-open' : ''}`}>
      {/* Icon sidebar (tier 1) */}
      <div className="icon-sidebar">
        <div className="logo-icon" title="Apollo Intelligence">
          <svg viewBox="0 0 100 100" className="logo-svg">
            <text x="50" y="75" textAnchor="middle" fontSize="80" fontFamily="Adobe Garamond Pro, Georgia, Times, serif" fontWeight="400">A</text>
          </svg>
        </div>
        
        <Button variant="ghost" className="icon-button" onClick={onHomeClick}>
          <Icon name="home" size={28} className="apollo-icon--sidebar" />
          <span>Home</span>
        </Button>
        
        <Button variant="ghost" className="icon-button" onClick={onChatClick}>
          <Icon name="chat" size={28} className="apollo-icon--sidebar" />
          <span>Chat</span>
        </Button>
        
        <Button variant="ghost" className="icon-button" onClick={onPeopleClick}>
          <Icon name="people" size={28} className="apollo-icon--sidebar" />
          <span>People</span>
        </Button>
        
        <Button variant="ghost" className="icon-button" onClick={onContentClick}>
          <Icon name="content" size={28} className="apollo-icon--sidebar" />
          <span>Content</span>
        </Button>
        
        <Button variant="ghost" className="icon-button" onClick={onAppsClick}>
          <Icon name="apps" size={28} className="apollo-icon--sidebar" />
          <span>Apps</span>
        </Button>
        
        <Button variant="ghost" className="icon-button active" onClick={() => window.scrollTo(0, 0)}>
          <Icon name="book" size={28} className="apollo-icon--sidebar" />
          <span className="two-line">Prompt<br/>Library</span>
        </Button>
        
        <div className="user-menu-container" ref={userMenuRef}>
          <Avatar
            name={isAuthenticated ? user?.fullName : ''}
            size="md"
            variant="circle"
            className={`${isAuthenticated ? 'apollo-avatar--clickable' : ''}`}
            title={isAuthenticated ? user.fullName : 'Not signed in'}
            onClick={() => isAuthenticated && setShowUserMenu(!showUserMenu)}
            tabIndex={isAuthenticated ? 0 : -1}
            role={isAuthenticated ? 'button' : 'img'}
            aria-label={isAuthenticated ? `${user.fullName} - Open user menu` : 'Not signed in'}
          />
          
          {isAuthenticated && showUserMenu && (
            <div className="user-menu-dropdown">
              <div className="user-menu-header">
                <div className="menu-user-info">
                  <div className="menu-user-name">{user?.fullName || 'User'}</div>
                  <div className="menu-user-role">{getRoleDisplayName() || 'Member'}</div>
                </div>
              </div>
              
              <div className="user-menu-section">
                <Button variant="ghost" size="sm" className="user-menu-item" onClick={() => alert('Profile/Settings - Coming soon!')}>
                  <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                    <path d="M8 8c1.66 0 3-1.34 3-3S9.66 2 8 2 5 3.34 5 5s1.34 3 3 3zm0 2c-2.33 0-7 1.17-7 3.5V15h14v-1.5c0-2.33-4.67-3.5-7-3.5z"/>
                  </svg>
                  Profile/Settings
                </Button>
                <Button variant="ghost" size="sm" className="user-menu-item logout-item" onClick={handleLogout}>
                  <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                    <path d="M6 2h4v1H6V2zM4 4h8v8H4V4zm2 2v4h4V6H6z"/>
                  </svg>
                  Sign Out
                </Button>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Main sidebar (tier 2) */}
      <div className="main-sidebar">
        <div className="sidebar-header">
          <h2>Prompt Library</h2>
        </div>
        
        <Button 
          variant="ghost"
          className="nav-item nav-button"
          onClick={() => isAuthenticated ? onNewPromptClick?.() : onLoginClick?.()}
          title={isAuthenticated ? 'Create a new prompt' : 'Sign in to create prompts'}
        >
          <Icon name="plus" size={20} />
          <span>New Prompt</span>
          <Icon name="arrow-down" size={16} className="dropdown-arrow" />
        </Button>
        
        <div className="nav-section">
          <Button variant="ghost" className="nav-item nav-button" onClick={() => alert('Categories: View and manage prompt categories. This would show a detailed category management interface.')}>
            <svg viewBox="0 0 24 24">
              <circle cx="12" cy="12" r="3"/>
              <path d="M12 1v6m0 6v6m4.22-10.22l4.24-4.24M6.34 6.34L2.1 2.1m17.8 17.8l-4.24-4.24M6.34 17.66L2.1 21.9"/>
            </svg>
            <span>Categories</span>
          </Button>
          
          <Button variant="ghost" className="nav-item nav-button" onClick={() => alert('Templates: Access prompt templates and examples. This would show a template gallery with pre-built prompt structures.')}>
            <svg viewBox="0 0 24 24">
              <path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z"/>
              <polyline points="14 2 14 8 20 8"/>
              <line x1="8" y1="13" x2="16" y2="13"/>
              <line x1="8" y1="17" x2="16" y2="17"/>
              <polyline points="10 9 9 9 8 9"/>
            </svg>
            <span>Templates</span>
          </Button>
          
          <Button variant="ghost" className="nav-item nav-button" onClick={() => alert('Analytics: View prompt library usage statistics and performance metrics. This would show charts and insights about prompt effectiveness.')}>
            <svg viewBox="0 0 24 24">
              <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>
              <rect x="7" y="7" width="3" height="9"/>
              <rect x="14" y="7" width="3" height="5"/>
            </svg>
            <span>Analytics</span>
          </Button>
        </div>

        <div className="filter-section">
          <div className="nav-label">Prompt Status</div>
          <FilterSelect
            value={selectedStatus}
            onChange={setSelectedStatus}
            options={statuses.map(status => ({ value: status, label: status }))}
            showAll={true}
            allLabel="All Statuses"
            showCounts={false}
            size="sm"
            fullWidth={true}
            data-testid="status-filter-select"
          />
        </div>

        <div className="filter-section">
          <div className="nav-label">Categories</div>
          <FilterSelect
            value={selectedCategory}
            onChange={setSelectedCategory}
            options={categories.map(category => ({
              value: category.name,
              label: category.name,
              count: category.count
            }))}
            showAll={true}
            allLabel="All Categories"
            showCounts={true}
            size="sm"
            fullWidth={true}
            data-testid="category-filter-select"
          />
        </div>

        <div style={{marginTop: 'auto'}}>
          {isAuthenticated ? (
            <>
              <div className="chat-info">Welcome back, {user.fullName}!</div>
              <div className="chat-actions">
                <Button variant="ghost" size="sm" onClick={onClearCache}>Clear Cache</Button> or 
                <Button variant="ghost" size="sm" onClick={onExportClick}>Export</Button>
              </div>
              <div className="chat-actions" style={{marginTop: '8px'}}>
                <Button variant="ghost" size="sm" className="logout-button" onClick={logout}>Sign Out</Button>
              </div>
            </>
          ) : (
            <>
              <div className="chat-info">Sign in to create and vote on prompts.</div>
              <div className="chat-actions">
                <Button variant="primary" size="sm" className="login-button" onClick={onLoginClick}>Sign In</Button>
              </div>
            </>
          )}
        </div>
      </div>
    </div>
    </>
  )
}

export default TwoTierSidebar