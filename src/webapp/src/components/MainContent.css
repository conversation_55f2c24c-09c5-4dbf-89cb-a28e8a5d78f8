/* Main Content */
.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background-color: var(--apollo-bg-primary);
  min-width: 0; /* Prevents flex item from overflowing */
}

.header {
  padding: var(--apollo-space-lg) var(--apollo-space-2xl);
  border-bottom: 1px solid var(--apollo-border-subtle);
  display: flex;
  align-items: center;
  gap: var(--apollo-space-lg);
  flex-wrap: wrap;
  min-height: var(--apollo-space-6xl);
}

.header-icon {
  width: var(--apollo-space-3xl);
  height: var(--apollo-space-3xl);
  background-color: var(--apollo-primary-light);
  border-radius: var(--apollo-radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
}

.header-title {
  font-size: var(--apollo-text-lg);
  color: var(--apollo-text-primary);
  font-weight: var(--apollo-font-semibold);
  font-family: var(--apollo-font-ui);
}

.menu-icon {
  width: var(--apollo-space-2xl);
  height: var(--apollo-space-2xl);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: var(--apollo-text-tertiary);
}

.header-controls {
  margin-left: auto;
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
  min-width: 0;
}

.search-container {
  position: relative;
  display: flex;
  align-items: center;
}

.search-container::before {
  content: '';
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  width: 16px;
  height: 16px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23606060' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='11' cy='11' r='8'%3E%3C/circle%3E%3Cpath d='m21 21-4.35-4.35'%3E%3C/path%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: center;
  z-index: 1;
  pointer-events: none;
}

.search-container:focus-within::before {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23007B63' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='11' cy='11' r='8'%3E%3C/circle%3E%3Cpath d='m21 21-4.35-4.35'%3E%3C/path%3E%3C/svg%3E");
}

.search-input {
  padding: 8px 12px 8px 40px;
  border: 1px solid var(--apollo-border-light);
  border-radius: var(--apollo-radius-sm);
  background-color: var(--apollo-bg-input);
  color: var(--apollo-text-primary);
  font-size: var(--apollo-text-base);
  width: 250px;
  min-width: 0;
  flex-shrink: 1;
  transition: all 0.2s ease;
}

.search-input:focus {
  outline: none;
  border-color: var(--apollo-primary);
  box-shadow: 0 0 0 2px rgba(0, 123, 99, 0.25);
  width: 280px;
}

.search-input::placeholder {
  color: var(--apollo-text-muted);
}

.sort-select {
  padding: 8px 12px;
  border: 1px solid var(--apollo-border-light);
  border-radius: var(--apollo-radius-sm);
  background-color: var(--apollo-bg-input);
  color: var(--apollo-text-primary);
  font-size: var(--apollo-text-base);
  cursor: pointer;
}

.sort-select:focus {
  outline: none;
  border-color: var(--apollo-primary);
}

/* Submit idea button styles moved to Button component */

/* Content Container */
.content-container {
  flex: 1;
  overflow-y: auto;
  padding: 24px;
}

.description {
  color: var(--apollo-text-tertiary);
  font-size: var(--apollo-text-base);
  margin-bottom: 24px;
  line-height: 1.5;
}

.prompt-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

/* AI Query Container */
.ai-query-container {
  padding: 20px;
  border-top: 1px solid var(--apollo-border-subtle);
}

/* Scrollbar styling */
::-webkit-scrollbar {
  width: var(--apollo-space-sm);
}

::-webkit-scrollbar-track {
  background: var(--apollo-scrollbar-track);
}

::-webkit-scrollbar-thumb {
  background: var(--apollo-scrollbar-thumb);
  border-radius: var(--apollo-radius-xs);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--apollo-scrollbar-thumb-hover);
}

/* Responsive Design */
@media (max-width: 1200px) {
  .search-input {
    width: 200px;
  }
}

@media (max-width: 1024px) {
  .header {
    padding: 12px 16px;
    gap: 12px;
  }
  
  .search-input {
    width: 180px;
  }
  
  /* Button responsive styles handled by Button component */
  
  .header-title {
    font-size: 15px;
  }
}

@media (max-width: 768px) {
  .main-content {
    margin-left: 0;
  }
  
  .header {
    padding: 60px 16px 12px 16px; /* Account for mobile menu button */
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
    min-height: auto;
  }
  
  .header-title {
    font-size: 18px;
    text-align: center;
    margin-bottom: 8px;
  }
  
  .header-controls {
    margin-left: 0;
    justify-content: center;
    flex-wrap: wrap;
    gap: 8px;
  }
  
  .search-input {
    width: 100%;
    max-width: 300px;
    order: 1;
  }
  
  .sort-select {
    flex: 1;
    min-width: 120px;
    order: 2;
  }
  
  /* Button order styles handled by parent container */
  
  .content-container {
    padding: 16px;
  }
  
  .chat-input-container {
    padding: 16px;
  }
}

@media (max-width: 480px) {
  .header {
    padding: 60px 12px 12px 12px;
  }
  
  .header-controls {
    flex-direction: column;
    gap: 8px;
  }
  
  .search-input {
    width: 100%;
  }
  
  .sort-select {
    width: 100%;
  }
  
  /* Full width button styles handled by Button component */
  
  .content-container {
    padding: 12px;
  }
  
  .chat-input-container {
    padding: 12px;
  }
  
  .header-title {
    font-size: 16px;
  }
}