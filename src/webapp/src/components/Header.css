/* Professional Header Styles */
.main-header {
  background: white;
  border-bottom: 1px solid #e2e8f0;
  position: sticky;
  top: 0;
  z-index: 1000;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.header-content {
  max-width: 100%;
  padding: 0 24px;
  height: 64px;
  display: flex;
  align-items: center;
  gap: 24px;
}

/* Mobile menu button */
.mobile-menu-button {
  display: none;
  background: none;
  border: none;
  color: #64748b;
  cursor: pointer;
  padding: 8px;
  border-radius: 8px;
  transition: all 0.2s;
}

.mobile-menu-button:hover {
  background: #f1f5f9;
  color: #1a1a1a;
}

/* Logo */
.header-logo {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-shrink: 0;
}

.logo-icon {
  width: 36px;
  height: 36px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: 'AGaramondPro-Bold', Georgia, serif;
  font-size: 16px;
  font-weight: 700;
  color: white;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.2);
}

.logo-text {
  display: flex;
  flex-direction: column;
}

.logo-title {
  font-family: 'AGaramondPro-Bold', Georgia, serif;
  font-size: 18px;
  font-weight: 700;
  color: #1a1a1a;
  line-height: 1;
  margin-bottom: 1px;
}

.logo-subtitle {
  font-size: 11px;
  color: #64748b;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Search */
.header-search {
  flex: 1;
  max-width: 600px;
}

.search-input-container {
  position: relative;
  display: flex;
  align-items: center;
}

.search-icon {
  position: absolute;
  left: 16px;
  color: #64748b;
  z-index: 1;
}

.search-input {
  width: 100%;
  padding: 12px 16px 12px 44px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 14px;
  background: #f8fafc;
  transition: all 0.2s;
  font-family: 'Segoe UI', -apple-system, sans-serif;
}

.search-input:focus {
  outline: none;
  border-color: #667eea;
  background: white;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.search-input::placeholder {
  color: #9ca3af;
}

.search-shortcut {
  position: absolute;
  right: 12px;
  background: #e5e7eb;
  color: #6b7280;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 11px;
  font-weight: 500;
  font-family: monospace;
}

/* Header Actions */
.header-actions {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-shrink: 0;
}

.header-action-button {
  position: relative;
  background: none;
  border: none;
  color: #64748b;
  cursor: pointer;
  padding: 10px;
  border-radius: 8px;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.header-action-button:hover {
  background: #f1f5f9;
  color: #1a1a1a;
}

.notification-badge {
  position: absolute;
  top: 6px;
  right: 6px;
  background: #ef4444;
  color: white;
  border-radius: 10px;
  padding: 1px 5px;
  font-size: 10px;
  font-weight: 600;
  min-width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid white;
}

/* User Menu */
.user-menu-container {
  position: relative;
}

.user-menu-trigger {
  background: none;
  border: none;
  cursor: pointer;
  padding: 6px 12px;
  border-radius: 8px;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 12px;
  max-width: 240px;
}

.user-menu-trigger:hover {
  background: #f1f5f9;
}

.user-avatar {
  width: 32px;
  height: 32px;
  border-radius: 16px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: 12px;
  flex-shrink: 0;
}

.user-info {
  display: flex;
  flex-direction: column;
  text-align: left;
  min-width: 0;
  flex: 1;
}

.user-name {
  font-weight: 600;
  color: #1a1a1a;
  font-size: 14px;
  line-height: 1;
  margin-bottom: 2px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.user-role {
  font-size: 12px;
  color: #64748b;
  line-height: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.dropdown-arrow {
  color: #64748b;
  transition: transform 0.2s;
  flex-shrink: 0;
}

.dropdown-arrow.open {
  transform: rotate(180deg);
}

/* User Menu Dropdown */
.user-menu-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  min-width: 280px;
  z-index: 1000;
  animation: dropdownSlideIn 0.2s ease-out;
  margin-top: 8px;
}

@keyframes dropdownSlideIn {
  from {
    opacity: 0;
    transform: translateY(-8px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.user-menu-header {
  padding: 16px;
  border-bottom: 1px solid #f1f5f9;
  display: flex;
  align-items: center;
  gap: 12px;
  background: #f8fafc;
  border-radius: 12px 12px 0 0;
}

.menu-user-avatar {
  width: 48px;
  height: 48px;
  border-radius: 24px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: 16px;
  flex-shrink: 0;
}

.menu-user-info {
  flex: 1;
  min-width: 0;
}

.menu-user-name {
  font-weight: 600;
  color: #1a1a1a;
  font-size: 16px;
  margin-bottom: 2px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.menu-user-email {
  font-size: 14px;
  color: #64748b;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.user-menu-section {
  padding: 8px;
  border-bottom: 1px solid #f1f5f9;
}

.user-menu-section:last-child {
  border-bottom: none;
}

.menu-section-label {
  padding: 8px 12px 4px;
  font-size: 11px;
  font-weight: 600;
  color: #9ca3af;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.user-menu-item {
  width: 100%;
  background: none;
  border: none;
  padding: 12px 16px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 14px;
  color: #374151;
  text-align: left;
  font-family: 'Segoe UI', -apple-system, sans-serif;
}

.user-menu-item:hover {
  background: #f1f5f9;
  color: #1a1a1a;
}

.user-menu-item svg {
  flex-shrink: 0;
  color: #64748b;
}

.user-menu-item:hover svg {
  color: #374151;
}

.logout-item {
  color: #dc2626 !important;
}

.logout-item:hover {
  background: #fef2f2 !important;
  color: #dc2626 !important;
}

.logout-item svg {
  color: #dc2626 !important;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .header-search {
    max-width: 400px;
  }
  
  .user-info {
    display: none;
  }
  
  .user-menu-trigger {
    padding: 6px;
  }
}

@media (max-width: 768px) {
  .mobile-menu-button {
    display: flex;
  }
  
  .header-content {
    padding: 0 16px;
    gap: 16px;
  }
  
  .header-search {
    display: none;
  }
  
  .logo-text {
    display: none;
  }
  
  .header-action-button:not(.user-menu-trigger) {
    display: none;
  }
  
  .user-menu-dropdown {
    min-width: 260px;
    right: -16px;
  }
}

@media (max-width: 480px) {
  .header-content {
    padding: 0 12px;
    gap: 12px;
  }
  
  .logo-icon {
    width: 32px;
    height: 32px;
    font-size: 14px;
  }
  
  .user-menu-dropdown {
    right: -12px;
    left: 12px;
    min-width: auto;
  }
}