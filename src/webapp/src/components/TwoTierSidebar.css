/* Two-tier Sidebar */
.sidebar-container {
  display: flex;
  height: 100vh;
  position: relative;
}

/* Mobile overlay */
.sidebar-overlay {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 998;
}

.sidebar-overlay.active {
  display: block;
}

/* Mobile sidebar container */
@media (max-width: 768px) {
  .sidebar-container {
    position: fixed;
    top: 0;
    left: -360px;
    width: 360px;
    z-index: 999;
    transition: left 0.3s ease;
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.3);
  }
  
  .sidebar-container.mobile-open {
    left: 0;
  }
}

/* Icon sidebar (tier 1) */
.icon-sidebar {
  width: var(--apollo-sidebar-icon-width);
  background-color: var(--apollo-bg-tertiary);
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-top: var(--apollo-space-xl);
  border-right: 1px solid var(--apollo-border-subtle);
  flex-shrink: 0;
}

/* Responsive icon sidebar */
@media (max-width: 1024px) {
  .icon-sidebar {
    width: 60px;
  }
  
  .icon-button svg {
    width: 24px;
    height: 24px;
  }
  
  .icon-button span {
    font-size: 10px;
  }
}

@media (max-width: 768px) {
  .icon-sidebar {
    width: 80px;
  }
}

.logo-icon {
  width: var(--apollo-space-6xl);
  height: var(--apollo-space-6xl);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: var(--apollo-space-2xl);
  cursor: pointer;
  transition: all var(--apollo-transition-slow);
  border-radius: var(--apollo-radius-lg);
  background: transparent;
  border: none;
}

.logo-svg {
  width: 100%;
  height: 100%;
  fill: white;
  color: white;
  transition: all 0.3s ease;
}

.logo-svg text {
  fill: white;
  color: white;
}

.logo-icon:hover .logo-svg {
  fill: var(--apollo-primary);
  color: var(--apollo-primary);
  transition: fill var(--apollo-transition-slow);
}

.logo-icon:hover .logo-svg text {
  fill: var(--apollo-primary);
  color: var(--apollo-primary);
}

.icon-button {
  width: 100%;
  padding: 12px 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  transition: background-color 0.2s;
  color: #808080;
  text-decoration: none;
  background: none;
  border: none;
  font: inherit;
}

.icon-button:hover {
  background-color: rgba(255,255,255,0.05);
  color: #e0e0e0;
}

.icon-button.active {
  background-color: var(--apollo-bg-card-hover);
  color: var(--apollo-text-primary);
}

.icon-button svg {
  width: 28px;
  height: 28px;
  fill: none;
  stroke: currentColor;
  stroke-width: 1.5;
  margin-bottom: 6px;
}

.icon-button span {
  font-size: 11px;
  font-weight: 400;
  letter-spacing: 0.02em;
}

.icon-button span.two-line {
  text-align: center;
  line-height: 1.2;
}

/* Avatar positioning in user menu */
.apollo-avatar--clickable {
  cursor: pointer;
}

.apollo-avatar--clickable:hover {
  transform: translateY(-1px);
  box-shadow: var(--apollo-shadow-sm);
}

/* User Menu Container */
.user-menu-container {
  position: relative;
  display: flex;
  justify-content: center;
  margin-top: auto;
  margin-bottom: 20px;
}

/* User Menu Dropdown */
.user-menu-dropdown {
  position: absolute;
  bottom: 40px;
  left: 40px;
  background: #2F3135;
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.4);
  min-width: 200px;
  z-index: 1000;
  animation: dropdownSlideUp 0.2s ease-out;
}

@keyframes dropdownSlideUp {
  from {
    opacity: 0;
    transform: translateY(8px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.user-menu-header {
  padding: 16px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.menu-user-info {
  text-align: center;
}

.menu-user-name {
  font-weight: 600;
  color: #e0e0e0;
  font-size: 14px;
  margin-bottom: 2px;
}

.menu-user-role {
  font-size: 12px;
  color: #808080;
}

.user-menu-section {
  padding: 8px;
}

.user-menu-item {
  width: 100%;
  background: none;
  border: none;
  padding: 10px 12px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 13px;
  color: #b0b0b0;
  text-align: left;
  font-family: inherit;
  margin-bottom: 2px;
}

.user-menu-item:hover {
  background: rgba(255, 255, 255, 0.05);
  color: #e0e0e0;
}

.user-menu-item svg {
  flex-shrink: 0;
  width: 14px;
  height: 14px;
}

.user-menu-item.logout-item {
  color: var(--apollo-error);
  margin-top: var(--apollo-space-xs);
}

.user-menu-item.logout-item:hover {
  background: rgba(255, 107, 107, 0.1);
  color: #ff5252;
}

/* Main sidebar (tier 2) */
.main-sidebar {
  width: var(--apollo-sidebar-main-width);
  background-color: var(--apollo-bg-secondary);
  display: flex;
  flex-direction: column;
  border-right: 1px solid var(--apollo-border-subtle);
  flex-shrink: 0;
}

/* Responsive main sidebar */
@media (max-width: 1024px) {
  .main-sidebar {
    width: 240px;
  }
}

@media (max-width: 768px) {
  .main-sidebar {
    width: 280px;
  }
}

.sidebar-header {
  padding: 24px 20px 16px;
  border-bottom: 1px solid rgba(255,255,255,0.05);
}

.sidebar-header h2 {
  font-size: 24px;
  font-weight: 600;
  color: #e0e0e0;
  margin: 0;
  font-family: 'Segoe UI', sans-serif;
}

.nav-section {
  padding: 15px 0;
}

.nav-item {
  display: flex;
  align-items: center;
  padding: 12px 20px;
  cursor: pointer;
  transition: background-color 0.2s;
  color: #b0b0b0;
}

.nav-item:hover {
  background-color: rgba(255,255,255,0.05);
}

.nav-item.active {
  background-color: var(--apollo-bg-card-hover);
  color: var(--apollo-text-primary);
}

.nav-item svg {
  width: 20px;
  height: 20px;
  margin-right: 12px;
  fill: none;
  stroke: currentColor;
  stroke-width: 1.5;
}

.dropdown-arrow {
  width: 16px !important;
  height: 16px !important;
  margin-left: auto !important;
  margin-right: 0 !important;
}

.nav-label {
  font-size: 11px;
  color: #808080;
  padding: 8px 20px;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.filter-section {
  padding: 8px 0;
}

.status-item {
  display: flex;
  align-items: center;
  padding: 8px 20px;
  cursor: pointer;
  transition: background-color 0.2s;
  color: #b0b0b0;
}

.status-item:hover {
  background-color: rgba(255,255,255,0.05);
}

.status-item input[type="radio"] {
  display: none;
}

.status-item .status-icon {
  width: 16px;
  height: 16px;
  margin-right: 10px;
  fill: none;
  stroke: #808080;
  stroke-width: 1.5;
}

.status-item input[type="radio"]:checked + .status-icon {
  stroke: var(--apollo-primary);
}

.category-item {
  display: flex;
  align-items: center;
  padding: 8px 20px;
  border: none;
  background: transparent;
  cursor: pointer;
  font-size: 14px;
  color: #b0b0b0;
  transition: background-color 0.2s;
  width: 100%;
  text-align: left;
}

.category-item:hover {
  background-color: rgba(255,255,255,0.05);
}

.category-item.active {
  background-color: var(--apollo-primary-light);
  color: var(--apollo-primary);
}

.category-item svg {
  width: 16px;
  height: 16px;
  margin-right: 10px;
  fill: none;
  stroke: currentColor;
  stroke-width: 1.5;
}

.category-name {
  flex: 1;
}

.category-count {
  background-color: var(--apollo-badge-bg);
  color: var(--apollo-text-tertiary);
  padding: 2px 6px;
  border-radius: var(--apollo-radius-lg);
  font-size: var(--apollo-text-xs);
  font-weight: var(--apollo-font-medium);
  margin-left: auto;
}

.category-item.active .category-count {
  background-color: var(--apollo-primary);
  color: var(--apollo-text-white);
}

.chat-info {
  font-size: 12px;
  color: #606060;
  padding: 0 20px 10px;
}

.chat-actions {
  font-size: 12px;
  padding: 0 20px 20px;
}

.chat-actions a {
  color: #808080;
  text-decoration: none;
}

.chat-actions a:hover {
  color: #b0b0b0;
}

.text-button {
  background: none;
  border: none;
  color: #808080;
  text-decoration: none;
  font-size: 12px;
  cursor: pointer;
  transition: color 0.2s;
  padding: 0;
  font-family: inherit;
}

.text-button:hover {
  color: #b0b0b0;
  text-decoration: underline;
}

.nav-button {
  background: none;
  border: none;
  cursor: pointer;
  font-family: inherit;
  width: 100%;
  text-align: left;
}

.logout-button {
  color: var(--apollo-error);
}

.logout-button:hover {
  color: var(--apollo-error-hover);
}

.login-button {
  font-weight: var(--apollo-font-medium);
  color: var(--apollo-primary);
}

.login-button:hover {
  color: var(--apollo-primary-hover);
}

/* Mobile hamburger menu button */
.mobile-menu-button {
  display: none;
  position: fixed;
  top: 16px;
  left: 16px;
  z-index: 1000;
  background-color: #282A2C;
  border: 1px solid rgba(255,255,255,0.1);
  border-radius: 8px;
  padding: 12px;
  cursor: pointer;
  color: #e0e0e0;
  transition: all 0.2s ease;
}

.mobile-menu-button:hover {
  background-color: #2F3135;
  border-color: rgba(255,255,255,0.2);
}

.mobile-menu-button svg {
  width: 20px;
  height: 20px;
  stroke: currentColor;
}

@media (max-width: 768px) {
  .mobile-menu-button {
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

/* Hide sidebar on mobile by default */
@media (max-width: 768px) {
  .sidebar-container:not(.mobile-open) {
    left: -360px;
  }
}