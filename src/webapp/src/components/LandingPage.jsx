import React, { useEffect } from 'react';
import './LandingPage.css';

const LandingPage = ({ onLoginClick }) => {
  // Add debug function for screenshots
  useEffect(() => {
    const takeScreenshot = () => {
      console.log('Taking screenshot of landing page...');
      console.log('Body background:', getComputedStyle(document.body).backgroundColor);
      console.log('Landing page element:', document.querySelector('.landing-page'));
      console.log('App element:', document.querySelector('.app'));
      console.log('Sidebar element:', document.querySelector('.sidebar-container'));
    };
    
    // Add to window for console access
    window.debugLanding = takeScreenshot;
    
    // Auto-run debug on load
    setTimeout(takeScreenshot, 100);
    
    return () => {
      delete window.debugLanding;
    };
  }, []);
  return (
    <div className="landing-page">
      <div className="landing-modal">
        <div className="apollo-logo">
          <svg viewBox="0 0 100 100" className="logo-svg">
            <text x="50" y="75" textAnchor="middle" fontSize="80" fontFamily="Adobe Garamond Pro, Georgia, Times, serif" fontWeight="400">A</text>
          </svg>
        </div>
        
        <button className="login-button" onClick={onLoginClick}>
          Sign In
        </button>
      </div>
    </div>
  );
};

export default LandingPage;