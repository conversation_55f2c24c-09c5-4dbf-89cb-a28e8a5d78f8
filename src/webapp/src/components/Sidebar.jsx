import './Sidebar.css'

function Sidebar({ statuses, categories, selectedStatus, setSelectedStatus, selectedCategory, setSelectedCategory }) {
  return (
    <aside className="sidebar">
      <div className="admin-badge">Admin only</div>
      
      <div className="filter-section">
        <h3>Statuses</h3>
        <div className="status-list">
          {statuses.map(status => (
            <label key={status} className="status-item">
              <input 
                type="radio" 
                name="status" 
                checked={selectedStatus === status}
                onChange={() => setSelectedStatus(selectedStatus === status ? '' : status)}
              />
              <span className="radio-custom">○</span>
              {status}
            </label>
          ))}
        </div>
      </div>

      <div className="filter-section">
        <h3>Categories</h3>
        <div className="category-list">
          {categories.map(category => (
            <button 
              key={category.name} 
              className={`category-item ${selectedCategory === category.name ? 'active' : ''}`}
              onClick={() => setSelectedCategory(selectedCategory === category.name ? '' : category.name)}
            >
              <span className="category-name"># {category.name}</span>
              <span className="category-count">{category.count}</span>
            </button>
          ))}
        </div>
      </div>

      <div className="sidebar-footer">
        <div className="remaining-count">23 prompts remaining</div>
        <button className="upgrade-btn">Upgrade now</button>
      </div>
    </aside>
  )
}

export default Sidebar