import React, { useState } from 'react';
import { useAuth } from '../auth/AuthContext';
import './UserProfile.css';

const UserProfile = ({ isOpen, onClose }) => {
  const { user, logout } = useAuth();
  const [activeTab, setActiveTab] = useState('profile');
  const [isEditing, setIsEditing] = useState(false);
  const [editedUser, setEditedUser] = useState({
    fullName: user?.fullName || '',
    email: user?.email || '',
    title: user?.title || '',
    department: user?.department || '',
    phone: user?.phone || '',
    timezone: user?.timezone || 'America/New_York'
  });
  const [currentPassword, setCurrentPassword] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [preferences, setPreferences] = useState({
    emailNotifications: true,
    pushNotifications: false,
    weeklyDigest: true,
    darkMode: false,
    defaultView: 'grid',
    itemsPerPage: 20
  });

  const handleSaveProfile = () => {
    // Mock save operation
    console.log('Saving profile:', editedUser);
    setIsEditing(false);
    // In a real app, this would make an API call
  };

  const handleCancelEdit = () => {
    setEditedUser({
      fullName: user?.fullName || '',
      email: user?.email || '',
      title: user?.title || '',
      department: user?.department || '',
      phone: user?.phone || '',
      timezone: user?.timezone || 'America/New_York'
    });
    setIsEditing(false);
  };

  const handleChangePassword = () => {
    if (newPassword !== confirmPassword) {
      alert('New passwords do not match');
      return;
    }
    if (newPassword.length < 8) {
      alert('Password must be at least 8 characters');
      return;
    }
    // Mock password change
    console.log('Changing password');
    setCurrentPassword('');
    setNewPassword('');
    setConfirmPassword('');
    alert('Password changed successfully');
  };

  const handleSavePreferences = () => {
    // Mock save preferences
    console.log('Saving preferences:', preferences);
    alert('Preferences saved successfully');
  };

  if (!isOpen || !user) return null;

  return (
    <div className="profile-modal-overlay" onClick={onClose}>
      <div className="profile-modal" onClick={e => e.stopPropagation()}>
        <div className="profile-header">
          <div className="profile-header-content">
            <div className="profile-avatar-section">
              <div className="profile-avatar">
                {user.fullName.split(' ').map(n => n[0]).join('').toUpperCase()}
              </div>
              <div className="profile-info">
                <h2>{user.fullName}</h2>
                <p className="profile-role">{user.role} • {user.department || 'Investment Team'}</p>
                <p className="profile-email">{user.email}</p>
              </div>
            </div>
            <button className="close-button" onClick={onClose}>
              <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                <path d="M8 6.586L13.657 1 15 2.343 9.414 8 15 13.657 13.657 15 8 9.414 2.343 15 1 13.657 6.586 8 1 2.343 2.343 1 8 6.586z"/>
              </svg>
            </button>
          </div>
        </div>

        <div className="profile-tabs">
          <button 
            className={`profile-tab ${activeTab === 'profile' ? 'active' : ''}`}
            onClick={() => setActiveTab('profile')}
          >
            Profile
          </button>
          <button 
            className={`profile-tab ${activeTab === 'security' ? 'active' : ''}`}
            onClick={() => setActiveTab('security')}
          >
            Security
          </button>
          <button 
            className={`profile-tab ${activeTab === 'preferences' ? 'active' : ''}`}
            onClick={() => setActiveTab('preferences')}
          >
            Preferences
          </button>
        </div>

        <div className="profile-content">
          {activeTab === 'profile' && (
            <div className="profile-section">
              <div className="section-header">
                <h3>Profile Information</h3>
                {!isEditing ? (
                  <button className="edit-button" onClick={() => setIsEditing(true)}>
                    <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                      <path d="M11.5 2a1.5 1.5 0 0 1 2.5 1.5L8.5 8.5l-2 2-.5 2 2-.5 2-2L14.5 5.5a1.5 1.5 0 0 1-2.5 1.5L7.5 11.5 5 14l-1.5-1.5L6 10l4.5-4.5z"/>
                    </svg>
                    Edit
                  </button>
                ) : (
                  <div className="edit-actions">
                    <button className="save-button" onClick={handleSaveProfile}>Save</button>
                    <button className="cancel-button" onClick={handleCancelEdit}>Cancel</button>
                  </div>
                )}
              </div>

              <div className="profile-form">
                <div className="form-grid">
                  <div className="form-group">
                    <label>Full Name</label>
                    {isEditing ? (
                      <input
                        type="text"
                        value={editedUser.fullName}
                        onChange={(e) => setEditedUser({...editedUser, fullName: e.target.value})}
                      />
                    ) : (
                      <div className="form-value">{user.fullName}</div>
                    )}
                  </div>

                  <div className="form-group">
                    <label>Email</label>
                    {isEditing ? (
                      <input
                        type="email"
                        value={editedUser.email}
                        onChange={(e) => setEditedUser({...editedUser, email: e.target.value})}
                      />
                    ) : (
                      <div className="form-value">{user.email}</div>
                    )}
                  </div>

                  <div className="form-group">
                    <label>Job Title</label>
                    {isEditing ? (
                      <input
                        type="text"
                        value={editedUser.title}
                        onChange={(e) => setEditedUser({...editedUser, title: e.target.value})}
                        placeholder="e.g., Senior Investment Analyst"
                      />
                    ) : (
                      <div className="form-value">{editedUser.title || 'Not specified'}</div>
                    )}
                  </div>

                  <div className="form-group">
                    <label>Department</label>
                    {isEditing ? (
                      <select
                        value={editedUser.department}
                        onChange={(e) => setEditedUser({...editedUser, department: e.target.value})}
                      >
                        <option value="">Select Department</option>
                        <option value="Leadership">Leadership</option>
                        <option value="Investment">Investment</option>
                        <option value="Research">Research</option>
                        <option value="Operations">Operations</option>
                        <option value="Risk Management">Risk Management</option>
                      </select>
                    ) : (
                      <div className="form-value">{editedUser.department || 'Not specified'}</div>
                    )}
                  </div>

                  <div className="form-group">
                    <label>Phone</label>
                    {isEditing ? (
                      <input
                        type="tel"
                        value={editedUser.phone}
                        onChange={(e) => setEditedUser({...editedUser, phone: e.target.value})}
                        placeholder="+****************"
                      />
                    ) : (
                      <div className="form-value">{editedUser.phone || 'Not specified'}</div>
                    )}
                  </div>

                  <div className="form-group">
                    <label>Timezone</label>
                    {isEditing ? (
                      <select
                        value={editedUser.timezone}
                        onChange={(e) => setEditedUser({...editedUser, timezone: e.target.value})}
                      >
                        <option value="America/New_York">Eastern Time (ET)</option>
                        <option value="America/Chicago">Central Time (CT)</option>
                        <option value="America/Denver">Mountain Time (MT)</option>
                        <option value="America/Los_Angeles">Pacific Time (PT)</option>
                        <option value="Europe/London">London (GMT)</option>
                        <option value="Europe/Zurich">Zurich (CET)</option>
                        <option value="Asia/Hong_Kong">Hong Kong (HKT)</option>
                        <option value="Asia/Tokyo">Tokyo (JST)</option>
                      </select>
                    ) : (
                      <div className="form-value">{editedUser.timezone || 'America/New_York'}</div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'security' && (
            <div className="security-section">
              <div className="section-header">
                <h3>Security Settings</h3>
              </div>

              <div className="security-form">
                <div className="security-item">
                  <h4>Change Password</h4>
                  <div className="password-form">
                    <div className="form-group">
                      <label>Current Password</label>
                      <input
                        type="password"
                        value={currentPassword}
                        onChange={(e) => setCurrentPassword(e.target.value)}
                        placeholder="Enter current password"
                      />
                    </div>
                    <div className="form-group">
                      <label>New Password</label>
                      <input
                        type="password"
                        value={newPassword}
                        onChange={(e) => setNewPassword(e.target.value)}
                        placeholder="Enter new password"
                      />
                    </div>
                    <div className="form-group">
                      <label>Confirm New Password</label>
                      <input
                        type="password"
                        value={confirmPassword}
                        onChange={(e) => setConfirmPassword(e.target.value)}
                        placeholder="Confirm new password"
                      />
                    </div>
                    <button 
                      className="change-password-button"
                      onClick={handleChangePassword}
                      disabled={!currentPassword || !newPassword || !confirmPassword}
                    >
                      Change Password
                    </button>
                  </div>
                </div>

                <div className="security-item">
                  <h4>Two-Factor Authentication</h4>
                  <div className="security-option">
                    <div className="security-option-info">
                      <p>Add an extra layer of security to your account</p>
                      <span className="security-status">Not configured</span>
                    </div>
                    <button className="configure-button">Configure</button>
                  </div>
                </div>

                <div className="security-item">
                  <h4>Active Sessions</h4>
                  <div className="sessions-list">
                    <div className="session-item">
                      <div className="session-info">
                        <div className="session-device">Current Session - Chrome on macOS</div>
                        <div className="session-details">New York, NY • Last active: Now</div>
                      </div>
                      <span className="session-current">Current</span>
                    </div>
                  </div>
                </div>

                <div className="security-item danger-zone">
                  <h4>Account Actions</h4>
                  <div className="danger-actions">
                    <button className="logout-all-button">Sign out all devices</button>
                    <button className="delete-account-button">Delete account</button>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'preferences' && (
            <div className="preferences-section">
              <div className="section-header">
                <h3>Preferences</h3>
                <button className="save-button" onClick={handleSavePreferences}>
                  Save Changes
                </button>
              </div>

              <div className="preferences-form">
                <div className="preference-group">
                  <h4>Notifications</h4>
                  <div className="preference-item">
                    <label className="preference-label">
                      <input
                        type="checkbox"
                        checked={preferences.emailNotifications}
                        onChange={(e) => setPreferences({...preferences, emailNotifications: e.target.checked})}
                      />
                      <span className="checkbox-custom"></span>
                      Email notifications
                    </label>
                    <p>Receive email notifications for new prompts and updates</p>
                  </div>
                  <div className="preference-item">
                    <label className="preference-label">
                      <input
                        type="checkbox"
                        checked={preferences.pushNotifications}
                        onChange={(e) => setPreferences({...preferences, pushNotifications: e.target.checked})}
                      />
                      <span className="checkbox-custom"></span>
                      Push notifications
                    </label>
                    <p>Receive browser push notifications</p>
                  </div>
                  <div className="preference-item">
                    <label className="preference-label">
                      <input
                        type="checkbox"
                        checked={preferences.weeklyDigest}
                        onChange={(e) => setPreferences({...preferences, weeklyDigest: e.target.checked})}
                      />
                      <span className="checkbox-custom"></span>
                      Weekly digest
                    </label>
                    <p>Receive weekly summary of prompt library activity</p>
                  </div>
                </div>

                <div className="preference-group">
                  <h4>Interface</h4>
                  <div className="preference-item">
                    <label className="preference-label">
                      <input
                        type="checkbox"
                        checked={preferences.darkMode}
                        onChange={(e) => setPreferences({...preferences, darkMode: e.target.checked})}
                      />
                      <span className="checkbox-custom"></span>
                      Dark mode
                    </label>
                    <p>Use dark theme for the interface</p>
                  </div>
                  <div className="preference-item">
                    <label>Default view</label>
                    <select
                      value={preferences.defaultView}
                      onChange={(e) => setPreferences({...preferences, defaultView: e.target.value})}
                      className="preference-select"
                    >
                      <option value="grid">Grid view</option>
                      <option value="list">List view</option>
                      <option value="compact">Compact view</option>
                    </select>
                  </div>
                  <div className="preference-item">
                    <label>Items per page</label>
                    <select
                      value={preferences.itemsPerPage}
                      onChange={(e) => setPreferences({...preferences, itemsPerPage: parseInt(e.target.value)})}
                      className="preference-select"
                    >
                      <option value={10}>10 items</option>
                      <option value={20}>20 items</option>
                      <option value={50}>50 items</option>
                      <option value={100}>100 items</option>
                    </select>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default UserProfile;