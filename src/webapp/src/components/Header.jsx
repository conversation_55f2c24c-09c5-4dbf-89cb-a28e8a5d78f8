import React, { useState, useRef, useEffect } from 'react';
import { useAuth } from '../auth/AuthContext';
import { usePermissions } from '../hooks/usePermissions';
import UserProfile from './UserProfile';
import './Header.css';

const Header = ({ onMobileMenuToggle }) => {
  const { user, logout, isAuthenticated } = useAuth();
  const { isAdmin, canManageUsers, canAccessSystemSettings, getRoleDisplayName } = usePermissions();
  const [showUserMenu, setShowUserMenu] = useState(false);
  const [showProfileModal, setShowProfileModal] = useState(false);
  const userMenuRef = useRef(null);

  // Close user menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (userMenuRef.current && !userMenuRef.current.contains(event.target)) {
        setShowUserMenu(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleLogout = async () => {
    setShowUserMenu(false);
    await logout();
  };

  const openProfile = () => {
    setShowUserMenu(false);
    setShowProfileModal(true);
  };

  if (!isAuthenticated) {
    return null; // Don't show header on landing page
  }

  return (
    <>
      <header className="main-header">
        <div className="header-content">
          {/* Mobile menu button */}
          <button 
            className="mobile-menu-button"
            onClick={onMobileMenuToggle}
            aria-label="Toggle mobile menu"
          >
            <svg width="20" height="20" viewBox="0 0 20 20" fill="currentColor">
              <path d="M3 5h14M3 10h14M3 15h14" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
            </svg>
          </button>

          {/* Logo */}
          <div className="header-logo">
            <div className="logo-icon">A</div>
            <div className="logo-text">
              <div className="logo-title">Apollo</div>
              <div className="logo-subtitle">Prompt Library</div>
            </div>
          </div>

          {/* Search bar */}
          <div className="header-search">
            <div className="search-input-container">
              <svg className="search-icon" width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                <path d="M11.5 6.5a5 5 0 1 1-10 0 5 5 0 0 1 10 0zm-1.27 4.73a6 6 0 1 1 .707-.707l4.387 4.387a.5.5 0 0 1-.707.707l-4.387-4.387z"/>
              </svg>
              <input
                type="text"
                placeholder="Search prompts..."
                className="search-input"
              />
              <div className="search-shortcut">⌘K</div>
            </div>
          </div>

          {/* Right side actions */}
          <div className="header-actions">
            {/* Notifications */}
            <button className="header-action-button" title="Notifications">
              <svg width="18" height="18" viewBox="0 0 18 18" fill="currentColor">
                <path d="M9 1.5C6.5 1.5 4.5 3.5 4.5 6v2.5L3 10.5v1h12v-1L13.5 8.5V6c0-2.5-2-4.5-4.5-4.5zm0 14.5c-.8 0-1.5-.7-1.5-1.5h3c0 .8-.7 1.5-1.5 1.5z"/>
              </svg>
              <span className="notification-badge">2</span>
            </button>

            {/* Help */}
            <button className="header-action-button" title="Help & Support">
              <svg width="18" height="18" viewBox="0 0 18 18" fill="currentColor">
                <path d="M9 1.5C4.86 1.5 1.5 4.86 1.5 9S4.86 16.5 9 16.5 16.5 13.14 16.5 9 13.14 1.5 9 1.5zM9 13.5c-.41 0-.75-.34-.75-.75S8.59 12 9 12s.75.34.75.75-.34.75-.75.75zm1.5-4.13c-.55.3-.75.75-.75 1.13v.5h-1.5v-.5c0-.83.42-1.58 1.13-2.04.46-.3.62-.77.62-1.21 0-.62-.5-1.25-1.5-1.25S7 6.63 7 7.25H5.5c0-1.52 1.23-2.75 2.75-2.75S11 5.73 11 7.25c0 .83-.25 1.54-.5 2.12z"/>
              </svg>
            </button>

            {/* User Menu */}
            <div className="user-menu-container" ref={userMenuRef}>
              <button 
                className="user-menu-trigger"
                onClick={() => setShowUserMenu(!showUserMenu)}
                aria-expanded={showUserMenu}
              >
                <div className="user-avatar">
                  {user?.fullName?.split(' ').map(n => n[0]).join('').toUpperCase() || 'U'}
                </div>
                <div className="user-info">
                  <div className="user-name">{user?.fullName || 'User'}</div>
                  <div className="user-role">{getRoleDisplayName() || 'Member'}</div>
                </div>
                <svg 
                  className={`dropdown-arrow ${showUserMenu ? 'open' : ''}`} 
                  width="12" 
                  height="12" 
                  viewBox="0 0 12 12" 
                  fill="currentColor"
                >
                  <path d="M6 8.5L2.5 5h7L6 8.5z"/>
                </svg>
              </button>

              {showUserMenu && (
                <div className="user-menu-dropdown">
                  <div className="user-menu-header">
                    <div className="menu-user-avatar">
                      {user?.fullName?.split(' ').map(n => n[0]).join('').toUpperCase() || 'U'}
                    </div>
                    <div className="menu-user-info">
                      <div className="menu-user-name">{user?.fullName || 'User'}</div>
                      <div className="menu-user-email">{user?.email || '<EMAIL>'}</div>
                    </div>
                  </div>

                  <div className="user-menu-section">
                    <button className="user-menu-item" onClick={openProfile}>
                      <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                        <path d="M8 8c1.66 0 3-1.34 3-3S9.66 2 8 2 5 3.34 5 5s1.34 3 3 3zm0 2c-2.33 0-7 1.17-7 3.5V15h14v-1.5c0-2.33-4.67-3.5-7-3.5z"/>
                      </svg>
                      Profile Settings
                    </button>
                    <button className="user-menu-item">
                      <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                        <path d="M8 1.5c-1.4 0-2.5 1.1-2.5 2.5v.5c-.8 0-1.5.7-1.5 1.5v7c0 .8.7 1.5 1.5 1.5h5c.8 0 1.5-.7 1.5-1.5v-7c0-.8-.7-1.5-1.5-1.5V4c0-1.4-1.1-2.5-2.5-2.5zM7 4c0-.6.4-1 1-1s1 .4 1 1v.5H7V4z"/>
                      </svg>
                      Account Security
                    </button>
                    <button className="user-menu-item">
                      <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                        <path d="M3.5 2C2.67 2 2 2.67 2 3.5v9c0 .83.67 1.5 1.5 1.5h9c.83 0 1.5-.67 1.5-1.5v-9c0-.83-.67-1.5-1.5-1.5h-9zM5 5h6v1H5V5zm0 2h6v1H5V7zm0 2h4v1H5V9z"/>
                      </svg>
                      Billing & Usage
                    </button>
                  </div>

                  {(isAdmin() || canManageUsers() || canAccessSystemSettings()) && (
                    <div className="user-menu-section">
                      <div className="menu-section-label">Administration</div>
                      {canManageUsers() && (
                        <button className="user-menu-item">
                          <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                            <path d="M8 1.5c-.7 0-1.4.1-2 .4L5.5 3H4c-.8 0-1.5.7-1.5 1.5v7c0 .8.7 1.5 1.5 1.5h8c.8 0 1.5-.7 1.5-1.5v-7c0-.8-.7-1.5-1.5-1.5h-1.5l-.5-1.1c-.6-.3-1.3-.4-2-.4zM8 5c1.7 0 3 1.3 3 3s-1.3 3-3 3-3-1.3-3-3 1.3-3 3-3z"/>
                          </svg>
                          User Management
                        </button>
                      )}
                      {canAccessSystemSettings() && (
                        <button className="user-menu-item">
                          <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                            <path d="M8 1.5L6.5 3h-3C2.67 3 2 3.67 2 4.5v7c0 .83.67 1.5 1.5 1.5h9c.83 0 1.5-.67 1.5-1.5v-7c0-.83-.67-1.5-1.5-1.5h-3L8 1.5zM7 6v1H6v1h1v1h1V8h1V7H8V6H7z"/>
                          </svg>
                          System Settings
                        </button>
                      )}
                      {isAdmin() && (
                        <button className="user-menu-item">
                          <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                            <path d="M2 3h12v1H2V3zm0 3h12v1H2V6zm0 3h12v1H2V9zm0 3h12v1H2v-1z"/>
                          </svg>
                          Analytics
                        </button>
                      )}
                    </div>
                  )}

                  <div className="user-menu-section">
                    <button className="user-menu-item">
                      <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                        <path d="M8 1.5C4.86 1.5 1.5 4.86 1.5 8S4.86 14.5 8 14.5 14.5 11.14 14.5 8 11.14 1.5 8 1.5zM8 13c-.41 0-.75-.34-.75-.75S7.59 11.5 8 11.5s.75.34.75.75-.34.75-.75.75zm1.5-3.63c-.55.3-.75.75-.75 1.13v.5h-1.5v-.5c0-.83.42-1.58 1.13-2.04.46-.3.62-.77.62-1.21 0-.62-.5-1.25-1.5-1.25S6 6.13 6 6.75H4.5c0-1.52 1.23-2.75 2.75-2.75S10 5.23 10 6.75c0 .83-.25 1.54-.5 2.12z"/>
                      </svg>
                      Help & Support
                    </button>
                    <button className="user-menu-item">
                      <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                        <path d="M2 4h12v1H2V4zm0 3h12v1H2V7zm0 3h8v1H2v-1z"/>
                      </svg>
                      Keyboard Shortcuts
                    </button>
                  </div>

                  <div className="user-menu-section">
                    <button className="user-menu-item logout-item" onClick={handleLogout}>
                      <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                        <path d="M6 2h4v1H6V2zM4 4h8v8H4V4zm2 2v4h4V6H6z"/>
                      </svg>
                      Sign Out
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </header>

      <UserProfile 
        isOpen={showProfileModal}
        onClose={() => setShowProfileModal(false)}
      />
    </>
  );
};

export default Header;