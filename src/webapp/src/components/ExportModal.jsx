import React, { useState, useMemo } from 'react';
import { exportFormats, exportAsJSON, exportAsCSV, exportAsMarkdown, exportAsText, getExportSummary } from '../utils/export';
import './ExportModal.css';

const ExportModal = ({ isOpen, onClose, prompts = [] }) => {
  const [selectedFormat, setSelectedFormat] = useState(exportFormats.JSON);
  const [includeContent, setIncludeContent] = useState(true);
  const [selectedCategories, setSelectedCategories] = useState([]);
  const [selectedStatuses, setSelectedStatuses] = useState([]);
  const [filename, setFilename] = useState('apollo-prompts');

  const exportSummary = useMemo(() => getExportSummary(prompts), [prompts]);

  // Get unique categories and statuses
  const categories = useMemo(() => 
    [...new Set(prompts.map(p => p.category))].sort(),
    [prompts]
  );
  
  const statuses = useMemo(() => 
    [...new Set(prompts.map(p => p.status))].sort(),
    [prompts]
  );

  // Filter prompts based on selections
  const filteredPrompts = useMemo(() => {
    return prompts.filter(prompt => {
      const categoryMatch = selectedCategories.length === 0 || selectedCategories.includes(prompt.category);
      const statusMatch = selectedStatuses.length === 0 || selectedStatuses.includes(prompt.status);
      return categoryMatch && statusMatch;
    });
  }, [prompts, selectedCategories, selectedStatuses]);

  const handleExport = () => {
    if (filteredPrompts.length === 0) {
      alert('No prompts match your filter criteria');
      return;
    }

    const exportData = includeContent ? filteredPrompts : filteredPrompts.map(p => ({
      ...p,
      content: p.description // Use description if content is excluded
    }));

    switch (selectedFormat) {
      case exportFormats.JSON:
        exportAsJSON(exportData, filename);
        break;
      case exportFormats.CSV:
        exportAsCSV(exportData, filename);
        break;
      case exportFormats.MARKDOWN:
        exportAsMarkdown(exportData, filename);
        break;
      case exportFormats.TXT:
        exportAsText(exportData, filename);
        break;
      default:
        console.error('Unknown export format:', selectedFormat);
        return;
    }

    onClose();
  };

  const handleCategoryToggle = (category) => {
    setSelectedCategories(prev => 
      prev.includes(category) 
        ? prev.filter(c => c !== category)
        : [...prev, category]
    );
  };

  const handleStatusToggle = (status) => {
    setSelectedStatuses(prev => 
      prev.includes(status) 
        ? prev.filter(s => s !== status)
        : [...prev, status]
    );
  };

  if (!isOpen) return null;

  return (
    <div className="export-modal-overlay" onClick={onClose}>
      <div className="export-modal" onClick={e => e.stopPropagation()}>
        <div className="modal-header">
          <h2>Export Prompts</h2>
          <button className="close-button" onClick={onClose}>×</button>
        </div>
        
        <div className="export-content">
          {/* Export Summary */}
          <div className="export-summary">
            <h3>Export Summary</h3>
            <div className="summary-stats">
              <div className="stat">
                <span className="label">Total Prompts:</span>
                <span className="value">{exportSummary.total}</span>
              </div>
              <div className="stat">
                <span className="label">Categories:</span>
                <span className="value">{Object.keys(exportSummary.categories).length}</span>
              </div>
              <div className="stat">
                <span className="label">Total Votes:</span>
                <span className="value">{exportSummary.totalVotes}</span>
              </div>
              <div className="stat">
                <span className="label">Filtered Results:</span>
                <span className="value highlight">{filteredPrompts.length}</span>
              </div>
            </div>
          </div>

          {/* Format Selection */}
          <div className="format-section">
            <h3>Export Format</h3>
            <div className="format-options">
              {Object.entries(exportFormats).map(([key, value]) => (
                <label key={value} className="format-option">
                  <input
                    type="radio"
                    name="format"
                    value={value}
                    checked={selectedFormat === value}
                    onChange={() => setSelectedFormat(value)}
                  />
                  <span className="format-label">{key}</span>
                  <span className="format-desc">
                    {value === 'json' && 'Structured data format'}
                    {value === 'csv' && 'Spreadsheet compatible'}
                    {value === 'markdown' && 'Documentation format'}
                    {value === 'txt' && 'Plain text format'}
                  </span>
                </label>
              ))}
            </div>
          </div>

          {/* Options */}
          <div className="options-section">
            <h3>Export Options</h3>
            
            <div className="form-group">
              <label htmlFor="filename">Filename</label>
              <input
                type="text"
                id="filename"
                value={filename}
                onChange={(e) => setFilename(e.target.value)}
                placeholder="apollo-prompts"
              />
            </div>

            <div className="checkbox-group">
              <label className="checkbox-option">
                <input
                  type="checkbox"
                  checked={includeContent}
                  onChange={(e) => setIncludeContent(e.target.checked)}
                />
                <span>Include full prompt content</span>
              </label>
            </div>
          </div>

          {/* Filters */}
          <div className="filters-section">
            <h3>Filter by Categories</h3>
            <div className="filter-options">
              <button 
                className="filter-toggle-all"
                onClick={() => setSelectedCategories(selectedCategories.length === categories.length ? [] : [...categories])}
              >
                {selectedCategories.length === categories.length ? 'Deselect All' : 'Select All'}
              </button>
              
              <div className="filter-checkboxes">
                {categories.map(category => (
                  <label key={category} className="filter-option">
                    <input
                      type="checkbox"
                      checked={selectedCategories.includes(category)}
                      onChange={() => handleCategoryToggle(category)}
                    />
                    <span>{category}</span>
                    <span className="count">({exportSummary.categories[category] || 0})</span>
                  </label>
                ))}
              </div>
            </div>

            <h3>Filter by Status</h3>
            <div className="filter-options">
              <button 
                className="filter-toggle-all"
                onClick={() => setSelectedStatuses(selectedStatuses.length === statuses.length ? [] : [...statuses])}
              >
                {selectedStatuses.length === statuses.length ? 'Deselect All' : 'Select All'}
              </button>
              
              <div className="filter-checkboxes">
                {statuses.map(status => (
                  <label key={status} className="filter-option">
                    <input
                      type="checkbox"
                      checked={selectedStatuses.includes(status)}
                      onChange={() => handleStatusToggle(status)}
                    />
                    <span>{status}</span>
                    <span className="count">({exportSummary.statuses[status] || 0})</span>
                  </label>
                ))}
              </div>
            </div>
          </div>

          {/* Actions */}
          <div className="modal-actions">
            <button onClick={onClose} className="cancel-button">
              Cancel
            </button>
            <button onClick={handleExport} className="export-button" disabled={filteredPrompts.length === 0}>
              Export {filteredPrompts.length} Prompts
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ExportModal;