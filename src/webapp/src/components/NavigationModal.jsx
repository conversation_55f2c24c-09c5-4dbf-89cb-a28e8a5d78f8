import React, { useState } from 'react';
import './NavigationModal.css';

const NavigationModal = ({ isOpen, onClose, title, children }) => {
  if (!isOpen) return null;

  return (
    <div className="nav-modal-overlay" onClick={onClose}>
      <div className="nav-modal" onClick={e => e.stopPropagation()}>
        <div className="nav-modal-header">
          <h2>{title}</h2>
          <button className="close-button" onClick={onClose}>×</button>
        </div>
        
        <div className="nav-modal-content">
          {children}
        </div>
      </div>
    </div>
  );
};

export const HomeModal = ({ isOpen, onClose }) => (
  <NavigationModal isOpen={isOpen} onClose={onClose} title="Apollo Intelligence - Home">
    <div className="home-dashboard">
      <div className="dashboard-section">
        <h3>Quick Actions</h3>
        <div className="action-grid">
          <div className="action-card">
            <div className="action-icon">📊</div>
            <div className="action-title">Portfolio Dashboard</div>
            <div className="action-desc">View portfolio performance and metrics</div>
          </div>
          <div className="action-card">
            <div className="action-icon">🔍</div>
            <div className="action-title">Deal Pipeline</div>
            <div className="action-desc">Track current investment opportunities</div>
          </div>
          <div className="action-card">
            <div className="action-icon">📈</div>
            <div className="action-title">Market Analysis</div>
            <div className="action-desc">Latest market trends and insights</div>
          </div>
        </div>
      </div>
      
      <div className="dashboard-section">
        <h3>Recent Activity</h3>
        <div className="activity-list">
          <div className="activity-item">
            <div className="activity-time">2 hours ago</div>
            <div className="activity-text">New prompt added: "Investment Thesis Development"</div>
          </div>
          <div className="activity-item">
            <div className="activity-time">4 hours ago</div>
            <div className="activity-text">Portfolio review completed for Q4 2024</div>
          </div>
          <div className="activity-item">
            <div className="activity-time">1 day ago</div>
            <div className="activity-text">Market research updated: Technology sector</div>
          </div>
        </div>
      </div>
    </div>
  </NavigationModal>
);

export const ChatModal = ({ isOpen, onClose }) => {
  const [chatInput, setChatInput] = useState('');
  const [messages, setMessages] = useState([
    {
      id: 1,
      type: 'ai',
      text: "Hello! I'm your Apollo AI assistant. I can help you with investment analysis, market research, and accessing our prompt library. How can I assist you today?",
      time: 'Just now'
    }
  ]);

  const handleSendMessage = () => {
    if (!chatInput.trim()) return;
    
    const newMessage = {
      id: messages.length + 1,
      type: 'user',
      text: chatInput,
      time: 'Just now'
    };
    
    setMessages(prev => [...prev, newMessage]);
    setChatInput('');
    
    // Simulate AI response
    setTimeout(() => {
      const aiResponse = {
        id: messages.length + 2,
        type: 'ai',
        text: `I understand you're asking about "${chatInput}". This is a demo chat interface. In a real implementation, I would search the Apollo prompt library and provide relevant results.`,
        time: 'Just now'
      };
      setMessages(prev => [...prev, aiResponse]);
    }, 1000);
  };

  const handleSuggestionClick = (suggestion) => {
    setChatInput(suggestion);
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter') {
      handleSendMessage();
    }
  };

  return (
    <NavigationModal isOpen={isOpen} onClose={onClose} title="Apollo Intelligence - Chat">
      <div className="chat-interface">
        <div className="chat-header">
          <h3>Apollo AI Assistant</h3>
          <div className="chat-status">Online</div>
        </div>
        
        <div className="chat-messages">
          {messages.map(message => (
            <div key={message.id} className="message">
              <div className="message-avatar">
                {message.type === 'ai' ? '🤖' : '👤'}
              </div>
              <div className="message-content">
                <div className="message-text">{message.text}</div>
                <div className="message-time">{message.time}</div>
              </div>
            </div>
          ))}
        </div>
        
        <div className="chat-input-area">
          <input 
            type="text" 
            placeholder="Ask about investments, markets, or prompts..." 
            className="chat-message-input"
            value={chatInput}
            onChange={(e) => setChatInput(e.target.value)}
            onKeyPress={handleKeyPress}
          />
          <button className="send-button" onClick={handleSendMessage}>Send</button>
        </div>
        
        <div className="chat-suggestions">
          <div className="suggestion" onClick={() => handleSuggestionClick('Help me find due diligence prompts')}>
            Help me find due diligence prompts
          </div>
          <div className="suggestion" onClick={() => handleSuggestionClick("What's the latest market analysis?")}>
            What's the latest market analysis?
          </div>
          <div className="suggestion" onClick={() => handleSuggestionClick('Show me investment thesis templates')}>
            Show me investment thesis templates
          </div>
        </div>
      </div>
    </NavigationModal>
  );
};

export const PeopleModal = ({ isOpen, onClose }) => (
  <NavigationModal isOpen={isOpen} onClose={onClose} title="Apollo Intelligence - People">
    <div className="people-directory">
      <div className="people-search">
        <input type="text" placeholder="Search people..." className="people-search-input" />
      </div>
      
      <div className="people-grid">
        <div className="person-card">
          <div className="person-avatar">AF</div>
          <div className="person-info">
            <div className="person-name">Alex Foster</div>
            <div className="person-title">Managing Director</div>
            <div className="person-team">Investment Committee</div>
          </div>
          <div className="person-status online">Online</div>
        </div>
        
        <div className="person-card">
          <div className="person-avatar">JS</div>
          <div className="person-info">
            <div className="person-name">John Smith</div>
            <div className="person-title">Principal</div>
            <div className="person-team">PE Team</div>
          </div>
          <div className="person-status away">Away</div>
        </div>
        
        <div className="person-card">
          <div className="person-avatar">MJ</div>
          <div className="person-info">
            <div className="person-name">Maria Johnson</div>
            <div className="person-title">VP Research</div>
            <div className="person-team">Research Team</div>
          </div>
          <div className="person-status online">Online</div>
        </div>
        
        <div className="person-card">
          <div className="person-avatar">RW</div>
          <div className="person-info">
            <div className="person-name">Robert Wilson</div>
            <div className="person-title">Senior Analyst</div>
            <div className="person-team">Quantitative Team</div>
          </div>
          <div className="person-status offline">Offline</div>
        </div>
      </div>
      
      <div className="teams-section">
        <h3>Teams</h3>
        <div className="team-list">
          <div className="team-item">
            <div className="team-name">Investment Committee</div>
            <div className="team-count">8 members</div>
          </div>
          <div className="team-item">
            <div className="team-name">PE Team</div>
            <div className="team-count">12 members</div>
          </div>
          <div className="team-item">
            <div className="team-name">Research Team</div>
            <div className="team-count">6 members</div>
          </div>
          <div className="team-item">
            <div className="team-name">Quantitative Team</div>
            <div className="team-count">4 members</div>
          </div>
        </div>
      </div>
    </div>
  </NavigationModal>
);

export const ContentModal = ({ isOpen, onClose }) => (
  <NavigationModal isOpen={isOpen} onClose={onClose} title="Apollo Intelligence - Content">
    <div className="content-library">
      <div className="content-categories">
        <div className="category-card">
          <div className="category-icon">📊</div>
          <div className="category-title">Reports & Analysis</div>
          <div className="category-count">248 documents</div>
        </div>
        
        <div className="category-card">
          <div className="category-icon">📚</div>
          <div className="category-title">Prompt Library</div>
          <div className="category-count">40 prompts</div>
        </div>
        
        <div className="category-card">
          <div className="category-icon">📈</div>
          <div className="category-title">Market Research</div>
          <div className="category-count">156 reports</div>
        </div>
        
        <div className="category-card">
          <div className="category-icon">🎯</div>
          <div className="category-title">Investment Memos</div>
          <div className="category-count">89 memos</div>
        </div>
      </div>
      
      <div className="recent-content">
        <h3>Recently Accessed</h3>
        <div className="content-list">
          <div className="content-item">
            <div className="content-icon">📄</div>
            <div className="content-info">
              <div className="content-title">Q4 2024 Portfolio Review</div>
              <div className="content-meta">Updated 2 hours ago • Alex Foster</div>
            </div>
          </div>
          <div className="content-item">
            <div className="content-icon">📊</div>
            <div className="content-info">
              <div className="content-title">Technology Sector Analysis</div>
              <div className="content-meta">Updated yesterday • Research Team</div>
            </div>
          </div>
          <div className="content-item">
            <div className="content-icon">📚</div>
            <div className="content-info">
              <div className="content-title">Due Diligence Prompt Templates</div>
              <div className="content-meta">Updated 3 days ago • PE Team</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </NavigationModal>
);

export const AppsModal = ({ isOpen, onClose }) => (
  <NavigationModal isOpen={isOpen} onClose={onClose} title="Apollo Intelligence - Apps">
    <div className="apps-grid">
      <div className="app-card">
        <div className="app-icon">📊</div>
        <div className="app-name">Portfolio Analytics</div>
        <div className="app-desc">Advanced portfolio performance analysis</div>
      </div>
      
      <div className="app-card">
        <div className="app-icon">🔍</div>
        <div className="app-name">Deal Sourcing</div>
        <div className="app-desc">Identify and track investment opportunities</div>
      </div>
      
      <div className="app-card">
        <div className="app-icon">📈</div>
        <div className="app-name">Market Intelligence</div>
        <div className="app-desc">Real-time market data and analysis</div>
      </div>
      
      <div className="app-card">
        <div className="app-icon">📚</div>
        <div className="app-name">Prompt Library</div>
        <div className="app-desc">Curated AI prompts for investment analysis</div>
      </div>
      
      <div className="app-card">
        <div className="app-icon">📋</div>
        <div className="app-name">Due Diligence</div>
        <div className="app-desc">Streamlined due diligence workflows</div>
      </div>
      
      <div className="app-card">
        <div className="app-icon">💼</div>
        <div className="app-name">Portfolio Management</div>
        <div className="app-desc">Comprehensive portfolio oversight tools</div>
      </div>
    </div>
  </NavigationModal>
);

export default NavigationModal;