import PromptCard from './PromptCard'
import StreamingAIQueryInterface from '../ai-query/organisms/StreamingAIQueryInterface'
import { Button, Icon, SearchInput, Select } from './atoms'
import './MainContent.css'

function MainContent({ 
  prompts, 
  categories, 
  sortBy, 
  setSortBy, 
  searchTerm, 
  setSearchTerm, 
  onVote, 
  onCardClick, 
  onNewPromptClick, 
  onCitationClick, 
  onMobileMenuToggle,
  messages,
  setMessages,
  query,
  setQuery,
  loading,
  setLoading,
  isExpanded,
  setIsExpanded
}) {
  return (
    <div className="main-content">
      <div className="header">
        <div className="menu-icon" onClick={onMobileMenuToggle}>☰</div>
        <div className="header-icon">
          <svg width="16" height="16" viewBox="0 0 24 24" style={{stroke: '#007B63', fill: 'none', strokeWidth: '1.5'}}>
            <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
            <polyline points="14 2 14 8 20 8"/>
            <line x1="16" y1="13" x2="8" y2="13"/>
            <line x1="16" y1="17" x2="8" y2="17"/>
          </svg>
        </div>
        <h1 className="header-title">Apollo Prompt Library</h1>
        <div className="header-controls">
          <SearchInput
            value={searchTerm}
            onChange={setSearchTerm}
            placeholder="Search prompts..."
            size="md"
            data-testid="search-prompts-input"
          />
          <Select
            value={sortBy}
            onChange={setSortBy}
            options={[
              { value: 'trending', label: 'Trending' },
              { value: 'newest', label: 'Newest' },
              { value: 'votes', label: 'Most Votes' }
            ]}
            size="md"
            data-testid="sort-prompts-select"
          />
          <Button 
            variant="primary" 
            onClick={onNewPromptClick}
            data-testid="submit-idea-button"
          >
            <Icon name="plus" size={16} />
            Submit Idea
          </Button>
        </div>
      </div>

      <div className="content-container">
        <p className="description">
          Curated collection of investment analysis prompts designed for Apollo Global Management's 
          research and due diligence workflows.
        </p>
        
        <div className="prompt-list">
          {prompts.map(prompt => (
            <PromptCard 
              key={prompt.id} 
              prompt={prompt} 
              onVote={onVote}
              onCardClick={onCardClick}
            />
          ))}
        </div>
      </div>

      <div className="ai-query-container">
        <StreamingAIQueryInterface
          prompts={prompts}
          categories={categories}
          onCitationClick={onCitationClick}
          messages={messages}
          setMessages={setMessages}
          query={query}
          setQuery={setQuery}
          loading={loading}
          setLoading={setLoading}
          isExpanded={isExpanded}
          setIsExpanded={setIsExpanded}
        />
      </div>
    </div>
  )
}

export default MainContent