.prompt-card {
  background: var(--apollo-bg-card) !important;
  border: 1px solid transparent;
  border-radius: var(--apollo-radius-md);
  padding: var(--apollo-space-lg);
  display: flex;
  gap: var(--apollo-space-lg);
  transition: all var(--apollo-transition-normal);
  cursor: pointer;
  align-items: flex-start;
  min-width: 0;
}

.prompt-card:hover {
  background-color: var(--apollo-bg-card-hover) !important;
  border-color: var(--apollo-border-medium);
  transform: translateY(-1px);
  box-shadow: var(--apollo-shadow-lg);
}

.vote-section {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 50px;
  flex-shrink: 0;
}

.vote-count {
  font-size: 16px;
  font-weight: 600;
  color: var(--apollo-text-secondary);
  background: var(--apollo-vote-bg);
  border: 1px solid var(--apollo-vote-border);
  border-radius: var(--apollo-radius-md);
  padding: var(--apollo-space-sm) var(--apollo-space-md);
  min-width: 36px;
  text-align: center;
  cursor: pointer;
  transition: all var(--apollo-transition-normal);
  position: relative;
  overflow: hidden;
}

.vote-count::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, var(--apollo-primary-border), transparent);
  transition: left 0.5s ease;
}

.vote-count:hover {
  background: var(--apollo-vote-hover-bg);
  border-color: var(--apollo-vote-hover-border);
  color: var(--apollo-primary);
  transform: translateY(-1px);
  box-shadow: var(--apollo-shadow-md);
}

.vote-count:hover::before {
  left: 100%;
}

.vote-count:active {
  transform: translateY(0);
  transition: transform var(--apollo-transition-fast);
}

.vote-count.voted {
  background: var(--apollo-vote-voted-bg);
  border-color: var(--apollo-primary);
  color: var(--apollo-primary);
  transform: scale(1.05);
  animation: voteSuccess 0.3s ease;
}

@keyframes voteSuccess {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1.05); }
}

.vote-count.voted::after {
  content: '✓';
  position: absolute;
  top: -2px;
  right: -2px;
  font-size: 10px;
  color: var(--apollo-primary);
  background: var(--apollo-primary-light);
  border-radius: 50%;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: checkmarkAppear var(--apollo-transition-slow);
}

@keyframes checkmarkAppear {
  0% { opacity: 0; transform: scale(0); }
  100% { opacity: 1; transform: scale(1); }
}

.card-content {
  flex: 1;
  min-width: 0; /* Prevents overflow */
}

.card-title {
  font-size: var(--apollo-text-lg);
  font-weight: var(--apollo-font-semibold);
  color: var(--apollo-text-primary);
  margin-bottom: var(--apollo-space-sm);
  line-height: var(--apollo-leading-normal);
  font-family: var(--apollo-font-ui);
}

.card-description {
  color: var(--apollo-text-secondary);
  font-size: var(--apollo-text-base);
  line-height: var(--apollo-leading-relaxed);
  margin-bottom: var(--apollo-space-md);
}

.card-meta {
  margin-bottom: var(--apollo-space-md);
  display: flex;
  align-items: center;
  gap: var(--apollo-space-sm);
  flex-wrap: wrap;
  font-size: var(--apollo-text-sm);
}

.author {
  font-size: var(--apollo-text-sm);
  color: var(--apollo-text-tertiary);
}

.category-badge {
  font-size: var(--apollo-text-xs);
  background: var(--apollo-badge-bg);
  color: var(--apollo-badge-text);
  padding: 2px 6px;
  border-radius: var(--apollo-radius-xs);
  border: 1px solid var(--apollo-badge-border);
}

.difficulty-badge {
  font-size: var(--apollo-text-xs);
  background: var(--apollo-primary-light);
  color: var(--apollo-primary);
  padding: 2px 6px;
  border-radius: var(--apollo-radius-xs);
  border: 1px solid var(--apollo-primary-border);
}

.card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 8px;
}

.card-footer-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

.comment-section {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #808080;
}

.comment-icon {
  width: 14px;
  height: 14px;
}

.comment-count {
  font-size: 12px;
  font-weight: 500;
}

.tags {
  display: flex;
  gap: 6px;
  flex-wrap: wrap;
}

.tag {
  background: var(--apollo-primary-light);
  color: var(--apollo-primary);
  padding: 2px 6px;
  border-radius: var(--apollo-radius-xs);
  font-size: var(--apollo-text-sm);
  font-weight: var(--apollo-font-medium);
}

.status-badge {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  white-space: nowrap;
  flex-shrink: 0;
}

.status-approved {
  background: var(--apollo-success-bg);
  color: var(--apollo-success);
}

.status-planned {
  background: var(--apollo-info-bg);
  color: var(--apollo-info);
}

.status-review {
  background: var(--apollo-warning-bg);
  color: var(--apollo-warning);
}

.status-published {
  background: var(--apollo-primary-light);
  color: var(--apollo-primary);
}

.status-development {
  background: var(--apollo-purple-bg);
  color: var(--apollo-purple);
}

.status-suggested {
  background: var(--apollo-neutral-bg);
  color: var(--apollo-neutral);
}

.status-default {
  background: var(--apollo-badge-bg);
  color: var(--apollo-text-tertiary);
}

/* Responsive Design */
@media (max-width: 768px) {
  .prompt-card {
    padding: 12px;
    gap: 12px;
    flex-direction: column;
  }
  
  .vote-section {
    align-self: flex-start;
    margin-bottom: 8px;
  }
  
  .card-content {
    width: 100%;
  }
  
  .card-title {
    font-size: 15px;
    line-height: 1.3;
  }
  
  .card-description {
    font-size: 13px;
    line-height: 1.4;
  }
  
  .card-footer {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .card-footer-right {
    width: 100%;
    justify-content: space-between;
  }
  
  .tags {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .prompt-card {
    padding: 10px;
    gap: 10px;
  }
  
  .card-title {
    font-size: 14px;
  }
  
  .card-description {
    font-size: 12px;
  }
  
  .card-meta {
    font-size: 11px;
    gap: 6px;
  }
  
  .category-badge,
  .difficulty-badge {
    font-size: 10px;
    padding: 1px 4px;
  }
  
  .tag {
    font-size: 11px;
    padding: 1px 4px;
  }
  
  .status-badge {
    font-size: 11px;
    padding: 3px 6px;
  }
}