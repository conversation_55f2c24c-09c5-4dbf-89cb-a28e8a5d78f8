/* AIQueryInterface Component Styles */
.ai-query-interface {
  background: var(--apollo-bg-card);
  border: 1px solid var(--apollo-border-subtle);
  border-radius: var(--apollo-radius-lg);
  display: flex;
  flex-direction: column;
  transition: all var(--apollo-transition-slow);
  font-family: var(--apollo-font-ui);
}

.ai-query-interface.disabled {
  opacity: 0.6;
  pointer-events: none;
}

/* Variants */
.ai-query-interface.default {
  width: 100%;
  max-width: 600px;
}

.ai-query-interface.compact {
  max-width: 400px;
}

.ai-query-interface.inline {
  border: none;
  border-radius: 0;
  background: transparent;
}

/* Expanded/Collapsed states */
.ai-query-interface.collapsed {
  height: auto;
}

.ai-query-interface.expanded {
  min-height: 400px;
  max-height: 80vh;
}

/* Header */
.ai-query-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--apollo-space-md) var(--apollo-space-lg);
  border-bottom: 1px solid var(--apollo-border-subtle);
  background: var(--apollo-bg-input);
  border-radius: var(--apollo-radius-lg) var(--apollo-radius-lg) 0 0;
  cursor: pointer;
}

.ai-query-title {
  display: flex;
  align-items: center;
  gap: var(--apollo-space-sm);
  font-weight: var(--apollo-font-semibold);
  color: var(--apollo-text-primary);
  font-size: var(--apollo-text-sm);
}

.message-count {
  color: var(--apollo-text-muted);
  font-weight: var(--apollo-font-normal);
  font-size: var(--apollo-text-xs);
}

.ai-query-controls {
  display: flex;
  align-items: center;
  gap: var(--apollo-space-xs);
}

/* Messages Container */
.ai-messages-container {
  flex: 1;
  overflow-y: auto;
  padding: var(--apollo-space-lg);
  min-height: 200px;
}

.ai-messages {
  display: flex;
  flex-direction: column;
  gap: var(--apollo-space-lg);
}

.ai-messages-empty {
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  color: var(--apollo-text-muted);
  font-style: italic;
  min-height: 100px;
}

/* Message */
.ai-message {
  display: flex;
  gap: var(--apollo-space-md);
  align-items: flex-start;
}

.ai-message.user {
  flex-direction: row-reverse;
}

.ai-message-avatar {
  flex-shrink: 0;
  width: var(--apollo-space-2xl);
  height: var(--apollo-space-2xl);
  border-radius: var(--apollo-radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--apollo-text-xs);
  font-weight: var(--apollo-font-semibold);
}

.user-avatar {
  background: var(--apollo-primary);
  color: var(--apollo-text-white);
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--apollo-radius-full);
}

.ai-avatar {
  background: var(--apollo-bg-tertiary);
  color: var(--apollo-text-secondary);
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--apollo-radius-full);
  border: 1px solid var(--apollo-border-subtle);
}

.ai-message-content {
  flex: 1;
  min-width: 0;
}

.ai-message.user .ai-message-content {
  text-align: right;
}

.ai-message-text {
  background: var(--apollo-bg-input);
  border: 1px solid var(--apollo-border-subtle);
  border-radius: var(--apollo-radius-md);
  padding: var(--apollo-space-md) var(--apollo-space-lg);
  color: var(--apollo-text-primary);
  font-size: var(--apollo-text-sm);
  line-height: 1.5;
  word-wrap: break-word;
}

.ai-message.user .ai-message-text {
  background: var(--apollo-primary-light);
  color: var(--apollo-primary);
}

/* Streaming animation */
.streaming-text {
  position: relative;
}

.streaming-cursor {
  animation: blink 1s infinite;
  color: var(--apollo-primary);
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0; }
}

/* Citations */
.ai-message-citations {
  margin-top: var(--apollo-space-sm);
  padding-top: var(--apollo-space-sm);
  border-top: 1px solid var(--apollo-border-subtle);
  font-size: var(--apollo-text-xs);
  color: var(--apollo-text-muted);
}

.citation-link,
.inline-citation {
  background: none;
  border: none;
  color: var(--apollo-primary);
  cursor: pointer;
  text-decoration: underline;
  font-size: inherit;
  margin: 0 var(--apollo-space-xs);
  padding: 0;
}

.citation-link:hover,
.inline-citation:hover {
  color: var(--apollo-primary-hover);
  text-decoration: none;
}

.citation-link {
  display: inline-block;
  margin-right: var(--apollo-space-md);
  margin-bottom: var(--apollo-space-xs);
}

/* Input Form */
.ai-query-form {
  border-top: 1px solid var(--apollo-border-subtle);
  padding: var(--apollo-space-lg);
  background: var(--apollo-bg-input);
  border-radius: 0 0 var(--apollo-radius-lg) var(--apollo-radius-lg);
}

.ai-query-input-container {
  display: flex;
  gap: var(--apollo-space-md);
  align-items: flex-end;
}

.ai-query-input {
  flex: 1;
  min-height: 36px;
  max-height: 120px;
  padding: var(--apollo-space-sm) var(--apollo-space-md);
  border: 1px solid var(--apollo-border-light);
  border-radius: var(--apollo-radius-sm);
  background: var(--apollo-bg-primary);
  color: var(--apollo-text-primary);
  font-size: var(--apollo-text-sm);
  font-family: var(--apollo-font-ui);
  resize: none;
  overflow-y: auto;
  transition: all var(--apollo-transition-slow);
}

.ai-query-input:focus {
  outline: none;
  border-color: var(--apollo-primary);
  box-shadow: 0 0 0 2px rgba(0, 123, 99, 0.25);
}

.ai-query-input::placeholder {
  color: var(--apollo-text-muted);
}

.ai-query-input:disabled {
  background: var(--apollo-bg-input);
  color: var(--apollo-text-muted);
  cursor: not-allowed;
}

.ai-query-submit {
  flex-shrink: 0;
  min-width: 60px;
}

/* Scrollbar styling */
.ai-messages-container::-webkit-scrollbar {
  width: var(--apollo-space-sm);
}

.ai-messages-container::-webkit-scrollbar-track {
  background: var(--apollo-scrollbar-track);
}

.ai-messages-container::-webkit-scrollbar-thumb {
  background: var(--apollo-scrollbar-thumb);
  border-radius: var(--apollo-radius-xs);
}

.ai-messages-container::-webkit-scrollbar-thumb:hover {
  background: var(--apollo-scrollbar-thumb-hover);
}

.ai-query-input::-webkit-scrollbar {
  width: var(--apollo-space-xs);
}

.ai-query-input::-webkit-scrollbar-track {
  background: transparent;
}

.ai-query-input::-webkit-scrollbar-thumb {
  background: var(--apollo-scrollbar-thumb);
  border-radius: var(--apollo-radius-xs);
}

/* Responsive Design */
@media (max-width: 768px) {
  .ai-query-interface {
    max-width: 100%;
  }
  
  .ai-query-header {
    padding: var(--apollo-space-sm) var(--apollo-space-md);
  }
  
  .ai-messages-container {
    padding: var(--apollo-space-md);
    min-height: 150px;
  }
  
  .ai-query-form {
    padding: var(--apollo-space-md);
  }
  
  .ai-query-input-container {
    flex-direction: column;
    gap: var(--apollo-space-sm);
  }
  
  .ai-query-submit {
    width: 100%;
  }
  
  .ai-message-text {
    padding: var(--apollo-space-sm) var(--apollo-space-md);
    font-size: var(--apollo-text-xs);
  }
}

@media (max-width: 480px) {
  .ai-query-interface.expanded {
    min-height: 300px;
    max-height: 70vh;
  }
  
  .ai-messages-container {
    min-height: 120px;
  }
  
  .ai-message {
    gap: var(--apollo-space-sm);
  }
  
  .ai-message-avatar {
    width: var(--apollo-space-xl);
    height: var(--apollo-space-xl);
  }
}