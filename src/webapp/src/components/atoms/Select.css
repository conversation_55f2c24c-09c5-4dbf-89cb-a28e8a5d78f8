/* Apollo Select Component */

/* === BASE SELECT STYLES === */
.apollo-select {
  position: relative;
  display: inline-flex;
  flex-direction: column;
  width: 100%;
}

.apollo-select__trigger {
  display: flex;
  align-items: center;
  justify-content: space-between;
  border: 1px solid var(--apollo-border-medium);
  border-radius: var(--apollo-radius-sm);
  background-color: var(--apollo-bg-input);
  cursor: pointer;
  transition: all var(--apollo-transition-normal);
  outline: none;
}

.apollo-select__trigger:hover:not(.apollo-select--disabled .apollo-select__trigger) {
  border-color: var(--apollo-border-hover);
  background-color: var(--apollo-bg-input-hover);
}

.apollo-select--open .apollo-select__trigger,
.apollo-select__trigger:focus-visible {
  border-color: var(--apollo-primary);
  box-shadow: var(--apollo-shadow-focus);
  background-color: var(--apollo-bg-input-focus);
}

/* === TRIGGER CONTENT === */
.apollo-select__value {
  flex: 1;
  text-align: left;
  font-family: var(--apollo-font-ui);
  font-weight: var(--apollo-font-normal);
  color: var(--apollo-text-primary);
  line-height: var(--apollo-leading-normal);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.apollo-select:not(.apollo-select--has-value) .apollo-select__value {
  color: var(--apollo-text-placeholder);
}

.apollo-select__icons {
  display: flex;
  align-items: center;
  gap: var(--apollo-space-xs);
  flex-shrink: 0;
}

.apollo-select__clear {
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  background: none;
  color: var(--apollo-text-secondary);
  cursor: pointer;
  border-radius: var(--apollo-radius-xs);
  padding: 2px;
  transition: all var(--apollo-transition-fast);
}

.apollo-select__clear:hover {
  color: var(--apollo-text-primary);
  background-color: var(--apollo-bg-card-hover);
}

.apollo-select__arrow {
  display: flex;
  align-items: center;
  color: var(--apollo-text-secondary);
  transition: all var(--apollo-transition-normal);
}

.apollo-select__arrow--open {
  transform: rotate(180deg);
  color: var(--apollo-primary);
}

/* === SIZE VARIANTS === */

/* Small */
.apollo-select--sm .apollo-select__trigger {
  min-height: 28px;
  padding: 4px 8px;
}

.apollo-select--sm .apollo-select__value {
  font-size: var(--apollo-text-sm);
}

/* Medium (default) */
.apollo-select--md .apollo-select__trigger {
  min-height: 32px;
  padding: 6px 12px;
}

.apollo-select--md .apollo-select__value {
  font-size: var(--apollo-text-base);
}

/* Large */
.apollo-select--lg .apollo-select__trigger {
  min-height: 40px;
  padding: 10px 16px;
}

.apollo-select--lg .apollo-select__value {
  font-size: var(--apollo-text-lg);
}

/* === DROPDOWN STYLES === */
.apollo-select__dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  z-index: 1000;
  margin-top: 4px;
  border: 1px solid var(--apollo-border-medium);
  border-radius: var(--apollo-radius-sm);
  background-color: var(--apollo-bg-card);
  box-shadow: var(--apollo-shadow-lg);
  overflow: hidden;
  animation: apollo-select-dropdown-enter 0.15s ease-out;
}

@keyframes apollo-select-dropdown-enter {
  from {
    opacity: 0;
    transform: translateY(-4px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* === SEARCH INPUT === */
.apollo-select__search {
  padding: var(--apollo-space-sm);
  border-bottom: 1px solid var(--apollo-border-light);
}

.apollo-select__search-input {
  width: 100%;
  border: 1px solid var(--apollo-border-medium);
  border-radius: var(--apollo-radius-xs);
  padding: 6px 8px;
  font-size: var(--apollo-text-sm);
  font-family: var(--apollo-font-ui);
  background-color: var(--apollo-bg-input);
  color: var(--apollo-text-primary);
  outline: none;
  transition: border-color var(--apollo-transition-normal);
}

.apollo-select__search-input:focus {
  border-color: var(--apollo-primary);
  box-shadow: var(--apollo-shadow-focus);
}

.apollo-select__search-input::placeholder {
  color: var(--apollo-text-placeholder);
}

/* === OPTIONS LIST === */
.apollo-select__options {
  max-height: 200px;
  overflow-y: auto;
  padding: var(--apollo-space-xs) 0;
}

.apollo-select__option {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  cursor: pointer;
  font-family: var(--apollo-font-ui);
  font-size: var(--apollo-text-sm);
  color: var(--apollo-text-primary);
  background-color: transparent;
  transition: background-color var(--apollo-transition-fast);
}

.apollo-select__option:hover:not(.apollo-select__option--disabled) {
  background-color: var(--apollo-bg-card-hover);
}

.apollo-select__option--highlighted {
  background-color: var(--apollo-bg-card-hover);
}

.apollo-select__option--selected {
  background-color: var(--apollo-primary-light);
  color: var(--apollo-primary);
  font-weight: var(--apollo-font-medium);
}

.apollo-select__option--disabled {
  color: var(--apollo-text-disabled);
  cursor: not-allowed;
  opacity: 0.5;
}

.apollo-select__option-label {
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.apollo-select__option-count {
  font-size: var(--apollo-text-xs);
  color: var(--apollo-text-secondary);
  background-color: var(--apollo-bg-muted);
  border-radius: var(--apollo-radius-full);
  padding: 2px 6px;
  margin-left: var(--apollo-space-sm);
  font-weight: var(--apollo-font-medium);
}

.apollo-select__option--selected .apollo-select__option-count {
  background-color: var(--apollo-primary);
  color: var(--apollo-text-white);
}

.apollo-select__option-check {
  color: var(--apollo-primary);
  margin-left: var(--apollo-space-sm);
}

.apollo-select__no-options {
  padding: 12px;
  text-align: center;
  color: var(--apollo-text-secondary);
  font-size: var(--apollo-text-sm);
  font-style: italic;
}

/* === STATE STYLES === */

/* Disabled */
.apollo-select--disabled .apollo-select__trigger {
  opacity: 0.5;
  cursor: not-allowed;
  background-color: var(--apollo-bg-disabled);
  border-color: var(--apollo-border-disabled);
}

.apollo-select--disabled .apollo-select__value {
  color: var(--apollo-text-disabled);
}

.apollo-select--disabled .apollo-select__trigger:hover {
  border-color: var(--apollo-border-disabled);
  background-color: var(--apollo-bg-disabled);
}

/* Full width */
.apollo-select--full-width {
  width: 100%;
}

/* Has value */
.apollo-select--has-value .apollo-select__arrow {
  color: var(--apollo-primary);
}

/* === RESPONSIVE ADJUSTMENTS === */
@media (max-width: 768px) {
  .apollo-select--lg .apollo-select__trigger {
    min-height: 36px;
    padding: 8px 14px;
  }
  
  .apollo-select--lg .apollo-select__value {
    font-size: var(--apollo-text-base);
  }
  
  .apollo-select--md .apollo-select__trigger {
    min-height: 30px;
    padding: 5px 10px;
  }
  
  .apollo-select--md .apollo-select__value {
    font-size: var(--apollo-text-sm);
  }
  
  .apollo-select--sm .apollo-select__trigger {
    min-height: 26px;
    padding: 3px 6px;
  }
  
  .apollo-select--sm .apollo-select__value {
    font-size: var(--apollo-text-xs);
  }

  .apollo-select__dropdown {
    position: fixed;
    left: 4px;
    right: 4px;
    max-height: 60vh;
  }
  
  .apollo-select__options {
    max-height: none;
  }
}

/* === HIGH CONTRAST MODE === */
@media (prefers-contrast: high) {
  .apollo-select__trigger {
    border-width: 2px;
  }
  
  .apollo-select--open .apollo-select__trigger,
  .apollo-select__trigger:focus-visible {
    border-width: 2px;
  }
  
  .apollo-select__option--selected {
    border: 1px solid var(--apollo-primary);
  }
}

/* === REDUCED MOTION === */
@media (prefers-reduced-motion: reduce) {
  .apollo-select__trigger,
  .apollo-select__arrow,
  .apollo-select__option,
  .apollo-select__clear {
    transition: none;
  }
  
  .apollo-select__dropdown {
    animation: none;
  }
  
  .apollo-select__arrow--open {
    transform: none;
  }
}

/* === PRINT STYLES === */
@media print {
  .apollo-select__trigger {
    border: 1px solid black !important;
    background: white !important;
    box-shadow: none !important;
  }
  
  .apollo-select__dropdown {
    display: none !important;
  }
}