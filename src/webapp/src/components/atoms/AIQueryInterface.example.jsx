import React, { useState } from 'react'
import AIQueryInterface from './AIQueryInterface'

const mockMessages = [
  {
    id: 1,
    type: 'user',
    message: 'Show me prompts related to investment analysis'
  },
  {
    id: 2,
    type: 'ai',
    message: 'I found several prompts related to investment analysis. Here are the most relevant ones: [1] Portfolio Performance Analysis, [5] Due Diligence Framework, and [12] Risk Assessment Template. These prompts can help you structure comprehensive investment evaluations.',
    citations: [
      { id: 1, title: 'Portfolio Performance Analysis' },
      { id: 5, title: 'Due Diligence Framework' },
      { id: 12, title: 'Risk Assessment Template' }
    ]
  },
  {
    id: 3,
    type: 'user',
    message: 'What about prompts for financial modeling?'
  },
  {
    id: 4,
    type: 'ai',
    message: 'For financial modeling, I recommend these prompts: [3] DCF Model Builder, [8] Scenario Analysis Framework, and [15] Sensitivity Analysis Template. These will help you create robust financial models.',
    isStreaming: true
  }
]

function AIQueryInterfaceExample() {
  const [messages, setMessages] = useState(mockMessages)
  const [query, setQuery] = useState('')
  const [loading, setLoading] = useState(false)
  const [isExpanded, setIsExpanded] = useState(false)
  const [variant, setVariant] = useState('default')

  const handleQueryChange = (newQuery) => {
    setQuery(newQuery)
  }

  const handleSubmit = async (submittedQuery) => {
    const userMessage = {
      id: Date.now(),
      type: 'user',
      message: submittedQuery
    }
    
    setMessages(prev => [...prev, userMessage])
    setQuery('')
    setLoading(true)

    // Simulate AI response
    setTimeout(() => {
      const aiMessage = {
        id: Date.now() + 1,
        type: 'ai',
        message: `I can help with "${submittedQuery}". Here are some relevant prompts that might assist you: [7] Strategic Planning Framework, [11] Market Analysis Template, and [16] Competitive Intelligence Guide.`,
        citations: [
          { id: 7, title: 'Strategic Planning Framework' },
          { id: 11, title: 'Market Analysis Template' },
          { id: 16, title: 'Competitive Intelligence Guide' }
        ]
      }
      setMessages(prev => [...prev, aiMessage])
      setLoading(false)
    }, 1500)
  }

  const handleCitationClick = (citationId) => {
    console.log('Citation clicked:', citationId)
    alert(`Navigate to prompt #${citationId}`)
  }

  const handleExpand = () => {
    setIsExpanded(true)
  }

  const handleMinimize = () => {
    setIsExpanded(false)
  }

  return (
    <div style={{ padding: '20px', backgroundColor: '#1a1a1a', minHeight: '100vh' }}>
      <h2 style={{ color: '#e0e0e0', marginBottom: '30px' }}>AIQueryInterface Component Examples</h2>
      
      <div style={{ marginBottom: '30px' }}>
        <label style={{ color: '#b0b0b0', marginRight: '10px' }}>
          Variant:
          <select 
            value={variant} 
            onChange={(e) => setVariant(e.target.value)}
            style={{ marginLeft: '8px', padding: '4px', backgroundColor: '#333', color: '#e0e0e0', border: '1px solid #555' }}
          >
            <option value="default">Default</option>
            <option value="compact">Compact</option>
            <option value="inline">Inline</option>
          </select>
        </label>
      </div>

      <div style={{ marginBottom: '40px' }}>
        <h3 style={{ color: '#b0b0b0', marginBottom: '20px' }}>Interactive Chat Interface</h3>
        <AIQueryInterface
          messages={messages}
          query={query}
          onQueryChange={handleQueryChange}
          onSubmit={handleSubmit}
          loading={loading}
          isExpanded={isExpanded}
          onExpand={handleExpand}
          onMinimize={handleMinimize}
          onCitationClick={handleCitationClick}
          placeholder="Ask about investment prompts..."
          variant={variant}
          showToggle={true}
        />
      </div>

      <div style={{ marginBottom: '40px' }}>
        <h3 style={{ color: '#b0b0b0', marginBottom: '20px' }}>Always Expanded (Inline)</h3>
        <AIQueryInterface
          messages={messages.slice(0, 2)}
          query=""
          onQueryChange={handleQueryChange}
          onSubmit={handleSubmit}
          loading={false}
          isExpanded={true}
          onCitationClick={handleCitationClick}
          placeholder="Always visible chat..."
          variant="inline"
          showToggle={false}
          maxHeight={300}
        />
      </div>

      <div style={{ marginBottom: '40px' }}>
        <h3 style={{ color: '#b0b0b0', marginBottom: '20px' }}>Compact Version</h3>
        <AIQueryInterface
          messages={[]}
          query=""
          onQueryChange={handleQueryChange}
          onSubmit={handleSubmit}
          loading={false}
          isExpanded={false}
          onExpand={handleExpand}
          onMinimize={handleMinimize}
          onCitationClick={handleCitationClick}
          placeholder="Compact interface..."
          variant="compact"
          showToggle={true}
        />
      </div>

      <div style={{ marginBottom: '40px' }}>
        <h3 style={{ color: '#b0b0b0', marginBottom: '20px' }}>Disabled State</h3>
        <AIQueryInterface
          messages={messages.slice(0, 1)}
          query="This is disabled"
          onQueryChange={handleQueryChange}
          onSubmit={handleSubmit}
          loading={false}
          isExpanded={true}
          disabled={true}
          placeholder="Disabled interface..."
          variant="default"
          showToggle={false}
        />
      </div>
    </div>
  )
}

export default AIQueryInterfaceExample