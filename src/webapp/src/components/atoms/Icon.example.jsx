import { Icon } from './index'

function IconExample() {
  return (
    <div style={{ padding: '20px', backgroundColor: '#1a1a1a', color: 'white' }}>
      <h2>Icon System Examples</h2>
      
      <section style={{ marginBottom: '20px' }}>
        <h3>Basic Icons</h3>
        <div style={{ display: 'flex', gap: '10px', alignItems: 'center' }}>
          <Icon name="book" />
          <Icon name="home" />
          <Icon name="chat" />
          <Icon name="users" />
          <Icon name="search" />
          <Icon name="settings" />
        </div>
      </section>

      <section style={{ marginBottom: '20px' }}>
        <h3>Different Sizes</h3>
        <div style={{ display: 'flex', gap: '10px', alignItems: 'center' }}>
          <Icon name="book" size="xs" />
          <Icon name="book" size="sm" />
          <Icon name="book" size="md" />
          <Icon name="book" size="lg" />
          <Icon name="book" size="xl" />
          <Icon name="book" size="2xl" />
        </div>
        <div style={{ display: 'flex', gap: '10px', alignItems: 'center', marginTop: '10px' }}>
          <Icon name="book" size={12} />
          <Icon name="book" size={20} />
          <Icon name="book" size={28} />
          <Icon name="book" size={36} />
        </div>
      </section>

      <section style={{ marginBottom: '20px' }}>
        <h3>Custom Colors</h3>
        <div style={{ display: 'flex', gap: '10px', alignItems: 'center' }}>
          <Icon name="book" color="#4A90E2" />
          <Icon name="book" color="#28a745" />
          <Icon name="book" color="#ffc107" />
          <Icon name="book" color="#dc3545" />
          <Icon name="book" color="#6f42c1" />
        </div>
      </section>

      <section style={{ marginBottom: '20px' }}>
        <h3>Interactive Icons</h3>
        <div style={{ display: 'flex', gap: '10px', alignItems: 'center' }}>
          <Icon 
            name="vote-up" 
            onClick={() => alert('Vote up clicked!')} 
            style={{ cursor: 'pointer' }}
            title="Vote Up"
          />
          <Icon 
            name="vote-down" 
            onClick={() => alert('Vote down clicked!')} 
            style={{ cursor: 'pointer' }}
            title="Vote Down"
          />
          <Icon 
            name="settings" 
            onClick={() => alert('Settings clicked!')} 
            style={{ cursor: 'pointer' }}
            title="Settings"
          />
        </div>
      </section>

      <section style={{ marginBottom: '20px' }}>
        <h3>Icon Aliases</h3>
        <div style={{ display: 'flex', gap: '10px', alignItems: 'center' }}>
          <div style={{ textAlign: 'center' }}>
            <Icon name="people" />
            <div style={{ fontSize: '12px', marginTop: '4px' }}>people</div>
          </div>
          <div style={{ textAlign: 'center' }}>
            <Icon name="users" />
            <div style={{ fontSize: '12px', marginTop: '4px' }}>users</div>
          </div>
          <div style={{ textAlign: 'center' }}>
            <Icon name="content" />
            <div style={{ fontSize: '12px', marginTop: '4px' }}>content</div>
          </div>
          <div style={{ textAlign: 'center' }}>
            <Icon name="apps" />
            <div style={{ fontSize: '12px', marginTop: '4px' }}>apps</div>
          </div>
        </div>
      </section>

      <section>
        <h3>Available Icons</h3>
        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(120px, 1fr))', gap: '10px' }}>
          {['book', 'home', 'chat', 'users', 'chart', 'grid', 'menu', 'arrow-down', 'plus', 'user', 'comment', 'search', 'filter', 'settings', 'close', 'vote-up', 'vote-down'].map(iconName => (
            <div key={iconName} style={{ textAlign: 'center', padding: '10px', border: '1px solid #333', borderRadius: '4px' }}>
              <Icon name={iconName} size="lg" />
              <div style={{ fontSize: '12px', marginTop: '4px' }}>{iconName}</div>
            </div>
          ))}
        </div>
      </section>
    </div>
  )
}

export default IconExample