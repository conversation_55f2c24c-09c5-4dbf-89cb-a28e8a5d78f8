import React from 'react'
import ExampleCard from './ExampleCard'

const mockExamples = [
  {
    input: "Review this Python API endpoint that handles user authentication for a portfolio company's customer portal.",
    output: "**Security Analysis:**\n- ✅ Proper password hashing using bcrypt\n- ❌ Missing rate limiting - implement 5 requests/minute\n- ❌ JWT tokens lack expiration time\n\n**Performance:**\n- ❌ Database query not optimized - add index on email field\n- ✅ Appropriate caching headers\n\n**Recommendations:**\n1. Add rate limiting middleware\n2. Set JWT expiration to 1 hour\n3. Create database index: CREATE INDEX idx_users_email ON users(email)"
  },
  {
    input: "Evaluate this React component for a financial dashboard showing portfolio metrics.",
    output: "**Security Analysis:**\n- ✅ Proper data sanitization\n- ❌ API keys exposed in frontend code\n\n**Performance:**\n- ❌ Component re-renders on every state change\n- ❌ Large data sets loaded without pagination\n\n**Best Practices:**\n- ❌ No error boundaries\n- ❌ Missing TypeScript types\n\n**Recommendations:**\n1. Move API keys to environment variables\n2. Implement React.memo() and useMemo()\n3. Add pagination for data tables\n4. Add error boundaries and TypeScript"
  }
]

function ExampleCardExample() {
  const handleExampleEdit = (index, example) => {
    console.log('Edit example:', index, example)
  }

  const handleExampleDelete = (index, example) => {
    console.log('Delete example:', index, example)
  }

  return (
    <div style={{ padding: '20px', backgroundColor: '#1a1a1a', minHeight: '100vh' }}>
      <h2 style={{ color: '#e0e0e0', marginBottom: '30px' }}>ExampleCard Component Examples</h2>
      
      <div style={{ marginBottom: '40px' }}>
        <h3 style={{ color: '#b0b0b0', marginBottom: '20px' }}>Default Examples</h3>
        <ExampleCard examples={mockExamples} />
      </div>

      <div style={{ marginBottom: '40px' }}>
        <h3 style={{ color: '#b0b0b0', marginBottom: '20px' }}>With Actions (Edit/Delete)</h3>
        <ExampleCard 
          examples={mockExamples} 
          showActions={true}
          onExampleEdit={handleExampleEdit}
          onExampleDelete={handleExampleDelete}
        />
      </div>

      <div style={{ marginBottom: '40px' }}>
        <h3 style={{ color: '#b0b0b0', marginBottom: '20px' }}>Compact Variant</h3>
        <ExampleCard 
          examples={mockExamples.slice(0, 1)} 
          variant="compact"
        />
      </div>

      <div style={{ marginBottom: '40px' }}>
        <h3 style={{ color: '#b0b0b0', marginBottom: '20px' }}>Empty State</h3>
        <ExampleCard examples={[]} />
      </div>
    </div>
  )
}

export default ExampleCardExample