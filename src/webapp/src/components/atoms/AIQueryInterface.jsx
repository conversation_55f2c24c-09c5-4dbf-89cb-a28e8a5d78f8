import React, { useState, useRef, useEffect } from 'react'
import { <PERSON><PERSON>, Loading } from './index'
import './AIQueryInterface.css'

/**
 * AIQueryInterface - Standardized chat interface for AI query interactions
 * 
 * @param {Object} props
 * @param {Array} props.messages - Array of message objects with type and content
 * @param {string} props.query - Current query input value
 * @param {Function} props.onQueryChange - Callback when query input changes
 * @param {Function} props.onSubmit - Callback when query is submitted
 * @param {boolean} props.loading - Whether AI is processing a query
 * @param {boolean} props.isExpanded - Whether interface is in expanded mode
 * @param {Function} props.onExpand - Callback to expand interface
 * @param {Function} props.onMinimize - Callback to minimize interface
 * @param {Function} props.onCitationClick - Callback when citation is clicked
 * @param {string} props.placeholder - Input placeholder text
 * @param {boolean} props.disabled - Whether interface is disabled
 * @param {string} props.variant - Interface variant: 'default' | 'compact' | 'inline'
 * @param {boolean} props.showToggle - Whether to show expand/minimize toggle
 * @param {number} props.maxHeight - Maximum height for message container
 */
function AIQueryInterface({
  messages = [],
  query = '',
  onQueryChange,
  onSubmit,
  loading = false,
  isExpanded = false,
  onExpand,
  onMinimize,
  onCitationClick,
  placeholder = 'Ask about prompts...',
  disabled = false,
  variant = 'default',
  showToggle = true,
  maxHeight = 400
}) {
  const containerRef = useRef(null)
  const messagesEndRef = useRef(null)
  const inputRef = useRef(null)

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }, [messages])

  // Focus input when expanded
  useEffect(() => {
    if (isExpanded && inputRef.current) {
      inputRef.current.focus()
    }
  }, [isExpanded])

  const handleSubmit = (e) => {
    e?.preventDefault()
    if (!query.trim() || loading || disabled) return
    onSubmit?.(query)
  }

  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSubmit()
    }
  }

  const handleCitationClick = (citationId) => {
    onCitationClick?.(citationId)
  }

  const renderMessage = (message, index) => {
    const isUser = message.type === 'user'
    
    return (
      <div key={message.id || index} className={`ai-message ${isUser ? 'user' : 'ai'}`}>
        <div className="ai-message-avatar">
          {isUser ? (
            <div className="user-avatar">U</div>
          ) : (
            <div className="ai-avatar">AI</div>
          )}
        </div>
        <div className="ai-message-content">
          <div className="ai-message-text">
            {message.isStreaming ? (
              <span className="streaming-text">
                {message.message}
                <span className="streaming-cursor">|</span>
              </span>
            ) : (
              renderMessageContent(message.message)
            )}
          </div>
          {message.citations && message.citations.length > 0 && (
            <div className="ai-message-citations">
              <strong>Sources:</strong>
              {message.citations.map((citation, idx) => (
                <button
                  key={idx}
                  className="citation-link"
                  onClick={() => handleCitationClick(citation.id)}
                >
                  [{idx + 1}] {citation.title}
                </button>
              ))}
            </div>
          )}
        </div>
      </div>
    )
  }

  const renderMessageContent = (content) => {
    // Handle citation links in text like [1], [5], [12]
    const citationRegex = /\[(\d+)\]/g
    const parts = content.split(citationRegex)
    
    return parts.map((part, index) => {
      if (index % 2 === 1) {
        // This is a citation number
        return (
          <button
            key={index}
            className="inline-citation"
            onClick={() => handleCitationClick(parseInt(part))}
          >
            [{part}]
          </button>
        )
      }
      return part
    })
  }

  const containerClass = `ai-query-interface ${variant} ${isExpanded ? 'expanded' : 'collapsed'} ${disabled ? 'disabled' : ''}`

  return (
    <div className={containerClass} ref={containerRef}>
      {showToggle && (
        <div className="ai-query-header">
          <div className="ai-query-title">
            <span>AI Assistant</span>
            {messages.length > 0 && (
              <span className="message-count">({messages.length})</span>
            )}
          </div>
          <div className="ai-query-controls">
            {isExpanded ? (
              <Button
                variant="ghost"
                size="sm"
                onClick={onMinimize}
                disabled={disabled}
                aria-label="Minimize chat"
              >
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                  <path d="M19 9l-7 7-7-7" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
              </Button>
            ) : (
              <Button
                variant="ghost"
                size="sm"
                onClick={onExpand}
                disabled={disabled}
                aria-label="Expand chat"
              >
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                  <path d="M5 15l7-7 7 7" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
              </Button>
            )}
          </div>
        </div>
      )}

      {(isExpanded || variant === 'inline') && (
        <>
          <div 
            className="ai-messages-container"
            style={{ maxHeight: maxHeight }}
          >
            {messages.length === 0 ? (
              <div className="ai-messages-empty">
                <p>Ask me anything about the prompt library!</p>
              </div>
            ) : (
              <div className="ai-messages">
                {messages.map(renderMessage)}
                <div ref={messagesEndRef} />
              </div>
            )}
          </div>

          <form className="ai-query-form" onSubmit={handleSubmit}>
            <div className="ai-query-input-container">
              <textarea
                ref={inputRef}
                value={query}
                onChange={(e) => onQueryChange?.(e.target.value)}
                onKeyPress={handleKeyPress}
                placeholder={placeholder}
                disabled={disabled || loading}
                className="ai-query-input"
                rows={1}
                style={{ resize: 'none' }}
              />
              <Button
                type="submit"
                variant="primary"
                size="sm"
                disabled={!query.trim() || loading || disabled}
                className="ai-query-submit"
              >
                {loading ? <Loading size="sm" /> : 'Send'}
              </Button>
            </div>
          </form>
        </>
      )}
    </div>
  )
}

export default AIQueryInterface