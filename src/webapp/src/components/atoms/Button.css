/* Apollo Button Component */

/* === BASE BUTTON STYLES === */
.apollo-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--apollo-space-sm);
  position: relative;
  
  /* Typography */
  font-family: var(--apollo-font-ui);
  font-weight: var(--apollo-font-medium);
  line-height: var(--apollo-leading-tight);
  text-decoration: none;
  white-space: nowrap;
  
  /* Layout */
  border: 1px solid transparent;
  border-radius: var(--apollo-radius-sm);
  cursor: pointer;
  
  /* Transitions */
  transition: all var(--apollo-transition-normal);
  
  /* Focus */
  outline: none;
}

.apollo-button:focus-visible {
  box-shadow: var(--apollo-shadow-focus);
  outline: none;
}

/* === LOADING STATE === */
.apollo-button--loading {
  cursor: not-allowed;
  pointer-events: none;
}

.apollo-button__loading-icon {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  align-items: center;
  justify-content: center;
}

.apollo-button__content {
  transition: opacity var(--apollo-transition-normal);
}

.apollo-button__content--loading {
  opacity: 0;
}

/* === DISABLED STATE === */
.apollo-button--disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

/* === FULL WIDTH === */
.apollo-button--full-width {
  width: 100%;
}

/* === SIZE VARIANTS === */

/* Small */
.apollo-button--sm {
  padding: 6px 12px;
  font-size: var(--apollo-text-sm);
  min-height: 28px;
}

/* Medium (default) */
.apollo-button--md {
  padding: 8px 16px;
  font-size: var(--apollo-text-base);
  min-height: 32px;
}

/* Large */
.apollo-button--lg {
  padding: 12px 20px;
  font-size: var(--apollo-text-lg);
  min-height: 40px;
}

/* === VARIANT STYLES === */

/* PRIMARY - Apollo green for main actions */
.apollo-button--primary {
  background-color: var(--apollo-primary);
  color: var(--apollo-text-white);
  border-color: var(--apollo-primary);
}

.apollo-button--primary:hover:not(.apollo-button--disabled):not(.apollo-button--loading) {
  background-color: var(--apollo-primary-hover);
  border-color: var(--apollo-primary-hover);
  transform: translateY(-1px);
  box-shadow: var(--apollo-shadow-md);
}

.apollo-button--primary:active:not(.apollo-button--disabled):not(.apollo-button--loading) {
  transform: translateY(0);
  box-shadow: var(--apollo-shadow-sm);
}

/* SECONDARY - Outlined button with hover effects */
.apollo-button--secondary {
  background-color: transparent;
  color: var(--apollo-text-primary);
  border-color: var(--apollo-border-medium);
}

.apollo-button--secondary:hover:not(.apollo-button--disabled):not(.apollo-button--loading) {
  background-color: var(--apollo-bg-card-hover);
  border-color: var(--apollo-primary);
  color: var(--apollo-primary);
  transform: translateY(-1px);
  box-shadow: var(--apollo-shadow-md);
}

.apollo-button--secondary:active:not(.apollo-button--disabled):not(.apollo-button--loading) {
  transform: translateY(0);
  box-shadow: var(--apollo-shadow-sm);
}

/* GHOST - Text-only button for subtle actions */
.apollo-button--ghost {
  background-color: transparent;
  color: var(--apollo-text-secondary);
  border-color: transparent;
}

.apollo-button--ghost:hover:not(.apollo-button--disabled):not(.apollo-button--loading) {
  background-color: var(--apollo-bg-card-hover);
  color: var(--apollo-text-primary);
  transform: translateY(-1px);
}

.apollo-button--ghost:active:not(.apollo-button--disabled):not(.apollo-button--loading) {
  transform: translateY(0);
}

/* DANGER - Red styling for destructive actions */
.apollo-button--danger {
  background-color: var(--apollo-error);
  color: var(--apollo-text-white);
  border-color: var(--apollo-error);
}

.apollo-button--danger:hover:not(.apollo-button--disabled):not(.apollo-button--loading) {
  background-color: var(--apollo-error-hover);
  border-color: var(--apollo-error-hover);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(255, 107, 107, 0.3);
}

.apollo-button--danger:active:not(.apollo-button--disabled):not(.apollo-button--loading) {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(255, 107, 107, 0.3);
}

/* SUCCESS - Green styling for positive actions */
.apollo-button--success {
  background-color: var(--apollo-success);
  color: var(--apollo-text-white);
  border-color: var(--apollo-success);
}

.apollo-button--success:hover:not(.apollo-button--disabled):not(.apollo-button--loading) {
  background-color: #0ea670;
  border-color: #0ea670;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(16, 185, 129, 0.3);
}

.apollo-button--success:active:not(.apollo-button--disabled):not(.apollo-button--loading) {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(16, 185, 129, 0.3);
}

/* === ICON-ONLY BUTTONS === */
.apollo-button--icon-only {
  aspect-ratio: 1;
  padding: 0;
}

.apollo-button--icon-only.apollo-button--sm {
  width: 28px;
  height: 28px;
}

.apollo-button--icon-only.apollo-button--md {
  width: 32px;
  height: 32px;
}

.apollo-button--icon-only.apollo-button--lg {
  width: 40px;
  height: 40px;
}

/* === LOADING ANIMATION === */
.loading-icon {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* === RESPONSIVE ADJUSTMENTS === */
@media (max-width: 768px) {
  .apollo-button--lg {
    padding: 10px 16px;
    font-size: var(--apollo-text-base);
    min-height: 36px;
  }
  
  .apollo-button--md {
    padding: 7px 14px;
    font-size: var(--apollo-text-sm);
    min-height: 30px;
  }
  
  .apollo-button--sm {
    padding: 5px 10px;
    font-size: var(--apollo-text-xs);
    min-height: 26px;
  }
}

/* === HIGH CONTRAST MODE === */
@media (prefers-contrast: high) {
  .apollo-button--primary {
    border-width: 2px;
  }
  
  .apollo-button--secondary {
    border-width: 2px;
  }
  
  .apollo-button--ghost:hover {
    border: 1px solid var(--apollo-text-primary);
  }
}

/* === REDUCED MOTION === */
@media (prefers-reduced-motion: reduce) {
  .apollo-button {
    transition: none;
  }
  
  .apollo-button:hover {
    transform: none;
  }
  
  .apollo-button:active {
    transform: none;
  }
  
  .loading-icon {
    animation: none;
  }
}

/* === PRINT STYLES === */
@media print {
  .apollo-button {
    background: transparent !important;
    color: black !important;
    border: 1px solid black !important;
    box-shadow: none !important;
    transform: none !important;
  }
}