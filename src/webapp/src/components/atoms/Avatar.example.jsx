import React, { useState } from 'react'
import Avatar from './Avatar'

const AvatarExample = () => {
  const [brokenImage, setBrokenImage] = useState(false)

  return (
    <div style={{ padding: '2rem', backgroundColor: '#1F2025', color: '#e0e0e0' }}>
      <h2>Avatar Component Examples</h2>
      
      <section style={{ marginBottom: '2rem' }}>
        <h3>Basic Usage</h3>
        <div style={{ display: 'flex', gap: '1rem', alignItems: 'center', flexWrap: 'wrap' }}>
          <Avatar name="<PERSON>" />
          <Avatar email="<EMAIL>" />
          <Avatar fallbackText="AB" />
          <Avatar />
        </div>
      </section>

      <section style={{ marginBottom: '2rem' }}>
        <h3>With Images</h3>
        <div style={{ display: 'flex', gap: '1rem', alignItems: 'center', flexWrap: 'wrap' }}>
          <Avatar 
            src="https://picsum.photos/40/40?random=1" 
            name="<PERSON>" 
            alt="Profile picture" 
          />
          <Avatar 
            src={brokenImage ? "broken-url" : "https://picsum.photos/40/40?random=2"} 
            name="Jane Smith" 
            alt="Profile picture" 
          />
          <button 
            onClick={() => setBrokenImage(!brokenImage)}
            style={{ 
              padding: '0.5rem 1rem', 
              backgroundColor: '#007B63', 
              color: 'white', 
              border: 'none', 
              borderRadius: '4px',
              cursor: 'pointer'
            }}
          >
            {brokenImage ? 'Fix Image' : 'Break Image'}
          </button>
        </div>
      </section>

      <section style={{ marginBottom: '2rem' }}>
        <h3>Sizes</h3>
        <div style={{ display: 'flex', gap: '1rem', alignItems: 'center', flexWrap: 'wrap' }}>
          <Avatar name="John Doe" size="xs" />
          <Avatar name="John Doe" size="sm" />
          <Avatar name="John Doe" size="md" />
          <Avatar name="John Doe" size="lg" />
          <Avatar name="John Doe" size="xl" />
        </div>
      </section>

      <section style={{ marginBottom: '2rem' }}>
        <h3>Variants</h3>
        <div style={{ display: 'flex', gap: '1rem', alignItems: 'center', flexWrap: 'wrap' }}>
          <Avatar name="John Doe" variant="circle" />
          <Avatar name="John Doe" variant="square" />
          <Avatar name="John Doe" variant="rounded" />
        </div>
      </section>

      <section style={{ marginBottom: '2rem' }}>
        <h3>Interactive (Clickable)</h3>
        <div style={{ display: 'flex', gap: '1rem', alignItems: 'center', flexWrap: 'wrap' }}>
          <Avatar 
            name="John Doe" 
            className="apollo-avatar--clickable" 
            onClick={() => alert('Avatar clicked!')}
            tabIndex={0}
            role="button"
            aria-label="John Doe - Click to view profile"
          />
          <Avatar 
            src="https://picsum.photos/40/40?random=3" 
            name="Jane Smith" 
            className="apollo-avatar--clickable apollo-avatar--online" 
            onClick={() => alert('Jane Smith clicked!')}
            tabIndex={0}
            role="button"
            aria-label="Jane Smith - Online - Click to view profile"
          />
        </div>
      </section>

      <section style={{ marginBottom: '2rem' }}>
        <h3>Status Indicators</h3>
        <div style={{ display: 'flex', gap: '1rem', alignItems: 'center', flexWrap: 'wrap' }}>
          <Avatar name="Online User" className="apollo-avatar--online" />
          <Avatar name="Offline User" className="apollo-avatar--offline" />
        </div>
      </section>

      <section style={{ marginBottom: '2rem' }}>
        <h3>Avatar Group</h3>
        <div className="apollo-avatar-group">
          <Avatar name="John Doe" size="sm" />
          <Avatar name="Jane Smith" size="sm" />
          <Avatar name="Bob Johnson" size="sm" />
          <Avatar fallbackText="+5" size="sm" />
        </div>
      </section>

      <section style={{ marginBottom: '2rem' }}>
        <h3>Complex Names</h3>
        <div style={{ display: 'flex', gap: '1rem', alignItems: 'center', flexWrap: 'wrap' }}>
          <Avatar name="José María García" />
          <Avatar name="李小明" />
          <Avatar name="محمد عبدالله" />
          <Avatar name="Jean-Pierre Dupont" />
          <Avatar name="O'Connor" />
        </div>
      </section>

      <section>
        <h3>In TwoTierSidebar Context</h3>
        <div style={{ 
          width: '80px', 
          backgroundColor: '#2F3135', 
          padding: '1rem', 
          display: 'flex', 
          justifyContent: 'center',
          borderRadius: '8px'
        }}>
          <Avatar 
            name="Alex Foster" 
            size="md"
            variant="circle"
            className="apollo-avatar--clickable"
            onClick={() => alert('User menu would open')}
            tabIndex={0}
            role="button"
            aria-label="Alex Foster - Open user menu"
          />
        </div>
      </section>
    </div>
  )
}

export default AvatarExample