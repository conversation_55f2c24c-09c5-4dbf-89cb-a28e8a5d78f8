import { useMemo } from 'react'
import Select from './Select'
import './FilterSelect.css'

function FilterSelect({
  value,
  onChange,
  options = [],
  showAll = true,
  allLabel = 'All',
  showCounts = true,
  multiple = false,
  placeholder,
  className = '',
  ...props
}) {
  // Process options to add count information and All option
  const processedOptions = useMemo(() => {
    let processedOpts = [...options]
    
    // Normalize option structure
    processedOpts = processedOpts.map((option, index) => {
      if (typeof option === 'string') {
        return { 
          value: option, 
          label: option, 
          count: 0,
          id: `filter-option-${index}`
        }
      }
      return {
        id: option.id || `filter-option-${index}`,
        value: option.value,
        label: option.label || option.value,
        count: option.count || 0,
        disabled: option.disabled || false
      }
    })

    // Add "All" option if enabled
    if (showAll) {
      const totalCount = processedOpts.reduce((sum, opt) => sum + (opt.count || 0), 0)
      processedOpts.unshift({
        id: 'filter-all',
        value: '',
        label: allLabel,
        count: showCounts ? totalCount : undefined,
        isAllOption: true
      })
    }

    return processedOpts
  }, [options, showAll, allLabel, showCounts])

  // Handle selection changes with All option logic
  const handleChange = (selectedValue) => {
    if (multiple) {
      // For multiple selection
      if (Array.isArray(selectedValue)) {
        // If "All" (empty string) is selected, clear all other selections
        if (selectedValue.includes('')) {
          onChange?.([])
        } else {
          onChange?.(selectedValue)
        }
      } else {
        // Single value being toggled
        if (selectedValue === '') {
          // "All" selected - clear everything
          onChange?.([])
        } else {
          // Regular option selected
          const currentValue = Array.isArray(value) ? value : []
          const newValue = currentValue.includes(selectedValue)
            ? currentValue.filter(v => v !== selectedValue)
            : [...currentValue, selectedValue]
          onChange?.(newValue)
        }
      }
    } else {
      // For single selection
      if (selectedValue === '') {
        // "All" selected
        onChange?.('')
      } else {
        onChange?.(selectedValue)
      }
    }
  }

  // Determine display value
  const getDisplayValue = () => {
    if (multiple) {
      const valueArray = Array.isArray(value) ? value : []
      // If no items selected, show as "All"
      if (valueArray.length === 0) {
        return showAll ? '' : valueArray
      }
      return valueArray
    }
    return value
  }

  // Custom placeholder logic
  const getPlaceholder = () => {
    if (placeholder) return placeholder
    
    if (multiple) {
      return 'Select filters...'
    }
    return showAll ? allLabel : 'Select an option...'
  }

  // Determine if All option should appear selected
  const isAllSelected = () => {
    if (multiple) {
      const valueArray = Array.isArray(value) ? value : []
      return valueArray.length === 0
    }
    return !value || value === ''
  }

  // Custom option rendering to show proper selection state
  const enhancedOptions = useMemo(() => {
    return processedOptions.map(option => ({
      ...option,
      // For display purposes, show All as selected when no other filters are active
      selected: option.isAllOption ? isAllSelected() : undefined
    }))
  }, [processedOptions, isAllSelected])

  const filterSelectClasses = [
    'apollo-filter-select',
    className
  ].filter(Boolean).join(' ')

  return (
    <Select
      value={getDisplayValue()}
      onChange={handleChange}
      options={enhancedOptions}
      placeholder={getPlaceholder()}
      multiple={multiple}
      clearable={!showAll} // Don't show clear if we have "All" option
      className={filterSelectClasses}
      {...props}
    />
  )
}

export default FilterSelect