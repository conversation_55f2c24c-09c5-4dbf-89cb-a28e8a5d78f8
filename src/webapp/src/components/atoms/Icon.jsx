import React from 'react'
import './Icon.css'
import {
  BookIcon,
  HomeIcon,
  ChatIcon,
  UsersIcon,
  ChartIcon,
  GridIcon,
  MenuIcon,
  ArrowDownIcon,
  PlusIcon,
  UserIcon,
  CommentIcon,
  SearchIcon,
  FilterIcon,
  SettingsIcon,
  CloseIcon,
  VoteUpIcon,
  VoteDownIcon,
  CheckIcon
} from './icons'

const iconMap = {
  'book': BookIcon,
  'home': HomeIcon,
  'chat': ChatIcon,
  'users': UsersIcon,
  'people': UsersIcon, // Alias for users
  'chart': ChartIcon,
  'content': ChartIcon, // Alias for chart
  'analytics': ChartIcon, // Alias for chart
  'grid': GridIcon,
  'apps': GridIcon, // Alias for grid
  'menu': MenuIcon,
  'arrow-down': ArrowDownIcon,
  'plus': PlusIcon,
  'user': UserIcon,
  'comment': CommentIcon,
  'search': SearchIcon,
  'filter': FilterIcon,
  'settings': SettingsIcon,
  'close': CloseIcon,
  'vote-up': VoteUpIcon,
  'vote-down': VoteDownIcon,
  'check': CheckIcon
}

const sizeMap = {
  'xs': 12,
  'sm': 16,
  'md': 20,
  'lg': 24,
  'xl': 32,
  '2xl': 48
}

const Icon = React.memo(function Icon({
  name,
  size = 'md',
  color = 'currentColor',
  className = '',
  onClick,
  ...props
}) {
  const IconComponent = iconMap[name]
  
  if (!IconComponent) {
    console.warn(`Icon "${name}" not found. Available icons: ${Object.keys(iconMap).join(', ')}`)
    return null
  }

  const iconSize = typeof size === 'number' ? size : sizeMap[size] || sizeMap.md
  const isInteractive = !!onClick
  
  const iconClasses = [
    'apollo-icon',
    className,
    isInteractive && 'apollo-icon--interactive'
  ].filter(Boolean).join(' ')

  return (
    <IconComponent
      size={iconSize}
      color={color}
      className={iconClasses}
      onClick={onClick}
      {...props}
    />
  )
})

export default Icon