/* Icon Component Styles */
.apollo-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  transition: all var(--apollo-transition-normal, 0.2s ease);
  vertical-align: middle;
}

/* Interactive Icons */
.apollo-icon--interactive {
  cursor: pointer;
}

.apollo-icon--interactive:hover {
  opacity: 0.8;
  transform: translateY(-1px);
}

.apollo-icon--interactive:active {
  transform: translateY(0);
  opacity: 0.6;
}

/* Size Variants */
.apollo-icon--xs {
  width: 12px;
  height: 12px;
}

.apollo-icon--sm {
  width: 16px;
  height: 16px;
}

.apollo-icon--md {
  width: 20px;
  height: 20px;
}

.apollo-icon--lg {
  width: 24px;
  height: 24px;
}

.apollo-icon--xl {
  width: 32px;
  height: 32px;
}

.apollo-icon--2xl {
  width: 48px;
  height: 48px;
}

/* Color Variants */
.apollo-icon--primary {
  color: var(--apollo-primary, #4A90E2);
}

.apollo-icon--secondary {
  color: var(--apollo-text-secondary, #808080);
}

.apollo-icon--success {
  color: var(--apollo-success, #28a745);
}

.apollo-icon--warning {
  color: var(--apollo-warning, #ffc107);
}

.apollo-icon--error {
  color: var(--apollo-error, #dc3545);
}

.apollo-icon--muted {
  color: var(--apollo-text-tertiary, #606060);
}

/* Context-specific styles */
.apollo-icon--button {
  padding: var(--apollo-space-xs, 4px);
  border-radius: var(--apollo-radius-sm, 4px);
  transition: background-color var(--apollo-transition-normal, 0.2s ease);
}

.apollo-icon--button:hover {
  background-color: var(--apollo-bg-hover, rgba(255, 255, 255, 0.05));
}

.apollo-icon--sidebar {
  color: var(--apollo-text-secondary, #808080);
}

.apollo-icon--sidebar:hover {
  color: var(--apollo-text-primary, #e0e0e0);
}

/* Focus states for accessibility */
.apollo-icon--interactive:focus {
  outline: 2px solid var(--apollo-primary, #4A90E2);
  outline-offset: 2px;
  border-radius: var(--apollo-radius-sm, 4px);
}

.apollo-icon--interactive:focus:not(:focus-visible) {
  outline: none;
}

/* Disabled state */
.apollo-icon--disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

/* Loading state */
.apollo-icon--loading {
  animation: apollo-icon-spin 1s linear infinite;
}

@keyframes apollo-icon-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .apollo-icon {
    filter: contrast(1.2);
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .apollo-icon {
    transition: none;
  }
  
  .apollo-icon--loading {
    animation: none;
  }
  
  .apollo-icon--interactive:hover {
    transform: none;
  }
}