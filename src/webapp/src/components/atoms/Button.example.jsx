import { useState } from 'react'
import But<PERSON> from './Button'
import { Icon } from '.'

/**
 * Button Component Examples
 * 
 * This file demonstrates all the variants, sizes, and states of the Apollo Button component.
 * Use this as a reference for implementing buttons throughout the application.
 */

function ButtonExamples() {
  const [loadingStates, setLoadingStates] = useState({})

  const handleAsyncAction = (buttonId) => {
    setLoadingStates(prev => ({ ...prev, [buttonId]: true }))
    
    // Simulate async operation
    setTimeout(() => {
      setLoadingStates(prev => ({ ...prev, [buttonId]: false }))
    }, 2000)
  }

  return (
    <div style={{ 
      padding: '2rem', 
      backgroundColor: 'var(--apollo-bg-primary)', 
      color: 'var(--apollo-text-primary)',
      fontFamily: 'var(--apollo-font-ui)'
    }}>
      <h1>Apollo Button Component Examples</h1>
      
      {/* Variants Section */}
      <section style={{ marginBottom: '2rem' }}>
        <h2>Variants</h2>
        <div style={{ display: 'flex', gap: '1rem', flexWrap: 'wrap', alignItems: 'center' }}>
          <Button variant="primary">Primary</Button>
          <Button variant="secondary">Secondary</Button>
          <Button variant="ghost">Ghost</Button>
          <Button variant="danger">Danger</Button>
          <Button variant="success">Success</Button>
        </div>
      </section>

      {/* Sizes Section */}
      <section style={{ marginBottom: '2rem' }}>
        <h2>Sizes</h2>
        <div style={{ display: 'flex', gap: '1rem', alignItems: 'center' }}>
          <Button size="sm">Small</Button>
          <Button size="md">Medium</Button>
          <Button size="lg">Large</Button>
        </div>
      </section>

      {/* States Section */}
      <section style={{ marginBottom: '2rem' }}>
        <h2>States</h2>
        <div style={{ display: 'flex', gap: '1rem', flexWrap: 'wrap', alignItems: 'center' }}>
          <Button>Normal</Button>
          <Button disabled>Disabled</Button>
          <Button 
            loading={loadingStates.async1} 
            onClick={() => handleAsyncAction('async1')}
          >
            {loadingStates.async1 ? 'Processing...' : 'Click to Load'}
          </Button>
        </div>
      </section>

      {/* With Icons Section */}
      <section style={{ marginBottom: '2rem' }}>
        <h2>With Icons</h2>
        <div style={{ display: 'flex', gap: '1rem', flexWrap: 'wrap', alignItems: 'center' }}>
          <Button variant="primary">
            <Icon name="plus" size={16} />
            Add New
          </Button>
          <Button variant="secondary">
            <Icon name="settings" size={16} />
            Settings
          </Button>
          <Button variant="ghost">
            <Icon name="search" size={16} />
            Search
          </Button>
        </div>
      </section>

      {/* Full Width Section */}
      <section style={{ marginBottom: '2rem' }}>
        <h2>Full Width</h2>
        <div style={{ maxWidth: '400px' }}>
          <Button fullWidth variant="primary" style={{ marginBottom: '0.5rem' }}>
            Full Width Primary
          </Button>
          <Button fullWidth variant="secondary">
            Full Width Secondary
          </Button>
        </div>
      </section>

      {/* Loading States in Different Variants */}
      <section style={{ marginBottom: '2rem' }}>
        <h2>Loading States</h2>
        <div style={{ display: 'flex', gap: '1rem', flexWrap: 'wrap', alignItems: 'center' }}>
          <Button 
            variant="primary" 
            loading={loadingStates.primaryLoad}
            onClick={() => handleAsyncAction('primaryLoad')}
          >
            Primary Load
          </Button>
          <Button 
            variant="secondary" 
            loading={loadingStates.secondaryLoad}
            onClick={() => handleAsyncAction('secondaryLoad')}
          >
            Secondary Load
          </Button>
          <Button 
            variant="danger" 
            loading={loadingStates.dangerLoad}
            onClick={() => handleAsyncAction('dangerLoad')}
          >
            Delete Item
          </Button>
        </div>
      </section>

      {/* Different Sizes with Loading */}
      <section style={{ marginBottom: '2rem' }}>
        <h2>Loading Sizes</h2>
        <div style={{ display: 'flex', gap: '1rem', alignItems: 'center' }}>
          <Button 
            size="sm" 
            loading={loadingStates.smallLoad}
            onClick={() => handleAsyncAction('smallLoad')}
          >
            Small Load
          </Button>
          <Button 
            size="md" 
            loading={loadingStates.mediumLoad}
            onClick={() => handleAsyncAction('mediumLoad')}
          >
            Medium Load
          </Button>
          <Button 
            size="lg" 
            loading={loadingStates.largeLoad}
            onClick={() => handleAsyncAction('largeLoad')}
          >
            Large Load
          </Button>
        </div>
      </section>

      {/* Form Types */}
      <section style={{ marginBottom: '2rem' }}>
        <h2>Form Types</h2>
        <form onSubmit={(e) => { e.preventDefault(); alert('Form submitted!') }}>
          <div style={{ display: 'flex', gap: '1rem', alignItems: 'center' }}>
            <Button type="submit" variant="primary">Submit</Button>
            <Button type="reset" variant="secondary">Reset</Button>
            <Button type="button" variant="ghost">Cancel</Button>
          </div>
        </form>
      </section>

      {/* Accessibility Examples */}
      <section style={{ marginBottom: '2rem' }}>
        <h2>Accessibility</h2>
        <div style={{ display: 'flex', gap: '1rem', flexWrap: 'wrap', alignItems: 'center' }}>
          <Button aria-label="Close dialog">×</Button>
          <Button aria-label="Refresh page">
            <Icon name="settings" size={16} />
          </Button>
          <Button data-testid="special-action">
            Testable Button
          </Button>
        </div>
      </section>

      {/* Real-world Examples */}
      <section style={{ marginBottom: '2rem' }}>
        <h2>Real-world Examples</h2>
        
        {/* Action Bar */}
        <div style={{ marginBottom: '1rem' }}>
          <h3>Action Bar</h3>
          <div style={{ display: 'flex', gap: '0.5rem', alignItems: 'center' }}>
            <Button variant="primary" size="sm">
              <Icon name="plus" size={14} />
              New
            </Button>
            <Button variant="ghost" size="sm">
              <Icon name="filter" size={14} />
            </Button>
            <Button variant="ghost" size="sm">
              <Icon name="search" size={14} />
            </Button>
          </div>
        </div>

        {/* Modal Actions */}
        <div style={{ marginBottom: '1rem' }}>
          <h3>Modal Actions</h3>
          <div style={{ display: 'flex', gap: '0.5rem', justifyContent: 'flex-end' }}>
            <Button variant="ghost">Cancel</Button>
            <Button variant="primary">Save Changes</Button>
          </div>
        </div>

        {/* Destructive Action */}
        <div style={{ marginBottom: '1rem' }}>
          <h3>Destructive Action</h3>
          <Button 
            variant="danger" 
            loading={loadingStates.deleteAction}
            onClick={() => handleAsyncAction('deleteAction')}
          >
            <Icon name="close" size={16} />
            Delete Account
          </Button>
        </div>

        {/* Success Action */}
        <div>
          <h3>Success Action</h3>
          <Button 
            variant="success" 
            loading={loadingStates.publishAction}
            onClick={() => handleAsyncAction('publishAction')}
          >
            <Icon name="plus" size={16} />
            Publish Prompt
          </Button>
        </div>
      </section>

      {/* Custom Styling */}
      <section style={{ marginBottom: '2rem' }}>
        <h2>Custom Styling</h2>
        <div style={{ display: 'flex', gap: '1rem', alignItems: 'center' }}>
          <Button 
            className="custom-button"
            style={{ 
              background: 'linear-gradient(45deg, #007B63, #00A578)',
              border: 'none'
            }}
          >
            Custom Gradient
          </Button>
          <Button 
            variant="ghost"
            style={{ 
              textTransform: 'uppercase',
              letterSpacing: '0.05em'
            }}
          >
            Styled Text
          </Button>
        </div>
      </section>
    </div>
  )
}

export default ButtonExamples