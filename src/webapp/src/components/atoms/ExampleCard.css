/* ExampleCard Component Styles */
.examples-section {
  margin-bottom: var(--apollo-space-2xl);
}

.examples-section h3 {
  font-size: var(--apollo-text-lg);
  font-weight: var(--apollo-font-semibold);
  color: var(--apollo-text-primary);
  margin: 0 0 var(--apollo-space-lg) 0;
  font-family: var(--apollo-font-ui);
}

.examples-container {
  display: flex;
  flex-direction: column;
  gap: var(--apollo-space-xl);
}

.examples-empty {
  background: var(--apollo-bg-card);
  border: 1px solid var(--apollo-border-subtle);
  border-radius: var(--apollo-radius-lg);
  padding: var(--apollo-space-2xl);
  text-align: center;
}

.examples-empty p {
  color: var(--apollo-text-muted);
  font-size: var(--apollo-text-base);
  margin: 0;
}

.example-item {
  background: var(--apollo-bg-card);
  border: 1px solid var(--apollo-border-subtle);
  border-radius: var(--apollo-radius-lg);
  padding: var(--apollo-space-xl);
  position: relative;
  transition: all var(--apollo-transition-slow);
}

.example-item:hover {
  border-color: var(--apollo-border-light);
  box-shadow: var(--apollo-shadow-sm);
}

.example-item.compact {
  padding: var(--apollo-space-lg);
}

.example-actions {
  position: absolute;
  top: var(--apollo-space-md);
  right: var(--apollo-space-md);
  display: flex;
  gap: var(--apollo-space-xs);
  opacity: 0;
  transition: opacity var(--apollo-transition-slow);
}

.example-item:hover .example-actions {
  opacity: 1;
}

.example-action-button {
  background: var(--apollo-bg-input);
  border: 1px solid var(--apollo-border-light);
  border-radius: var(--apollo-radius-sm);
  padding: var(--apollo-space-xs);
  cursor: pointer;
  color: var(--apollo-text-secondary);
  transition: all var(--apollo-transition-slow);
  display: flex;
  align-items: center;
  justify-content: center;
}

.example-action-button:hover {
  background: var(--apollo-bg-card-hover);
  color: var(--apollo-text-primary);
}

.example-action-button.edit:hover {
  color: var(--apollo-primary);
  border-color: var(--apollo-primary);
}

.example-action-button.delete:hover {
  color: var(--apollo-error);
  border-color: var(--apollo-error);
}

.example-content-two-column {
  display: grid;
  grid-template-columns: 1fr 1px 1fr;
  gap: var(--apollo-space-xl);
  align-items: start;
}

.example-input-column,
.example-output-column {
  display: flex;
  flex-direction: column;
  gap: var(--apollo-space-sm);
}

.example-divider {
  width: 1px;
  background: var(--apollo-border-subtle);
  min-height: 200px;
  opacity: 0.5;
}

.example-input-column h5,
.example-output-column h5 {
  font-size: var(--apollo-text-sm);
  font-weight: var(--apollo-font-semibold);
  color: var(--apollo-text-secondary);
  margin: 0;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.example-text {
  background: var(--apollo-bg-input);
  border: 1px solid var(--apollo-border-subtle);
  border-radius: var(--apollo-radius-sm);
  padding: var(--apollo-space-lg);
  font-size: var(--apollo-text-sm);
  line-height: 1.6;
  color: var(--apollo-text-primary);
  min-height: 100px;
}

.example-text pre {
  margin: 0;
  white-space: pre-wrap;
  word-wrap: break-word;
  font-family: var(--apollo-font-mono);
  font-size: var(--apollo-text-sm);
}

/* Compact variant styles */
.example-item.compact .example-content-two-column {
  gap: var(--apollo-space-lg);
}

.example-item.compact .example-text {
  padding: var(--apollo-space-md);
  min-height: 60px;
}

.example-item.compact .example-divider {
  min-height: 120px;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .example-content-two-column {
    grid-template-columns: 1fr;
    grid-template-rows: auto auto;
    gap: var(--apollo-space-lg);
  }
  
  .example-divider {
    display: none;
  }
  
  .example-actions {
    position: static;
    justify-content: flex-end;
    opacity: 1;
    margin-bottom: var(--apollo-space-md);
  }
}

@media (max-width: 768px) {
  .examples-section h3 {
    font-size: var(--apollo-text-base);
  }
  
  .example-item {
    padding: var(--apollo-space-lg);
  }
  
  .example-item.compact {
    padding: var(--apollo-space-md);
  }
  
  .example-text {
    padding: var(--apollo-space-md);
    font-size: var(--apollo-text-xs);
  }
  
  .example-text pre {
    font-size: var(--apollo-text-xs);
  }
}

@media (max-width: 480px) {
  .example-item {
    padding: var(--apollo-space-md);
  }
  
  .example-actions {
    gap: var(--apollo-space-xs);
  }
  
  .example-action-button {
    padding: var(--apollo-space-xs);
  }
}