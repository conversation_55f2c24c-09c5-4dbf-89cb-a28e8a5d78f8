import { useState, useEffect, useRef, forwardRef, useCallback } from 'react'
import { Icon } from './index'
import './SearchInput.css'

const SearchInput = forwardRef(function SearchInput({
  value = '',
  onChange,
  onClear,
  placeholder = 'Search...',
  disabled = false,
  autoFocus = false,
  debounceMs = 300,
  size = 'md',
  fullWidth = false,
  className = '',
  'data-testid': dataTestId,
  'aria-label': ariaLabel,
  ...props
}, ref) {
  const [localValue, setLocalValue] = useState(value)
  const [isFocused, setIsFocused] = useState(false)
  const inputRef = useRef(null)
  const debounceTimeoutRef = useRef(null)

  // Merge refs
  const mergedRef = useCallback((node) => {
    inputRef.current = node
    if (typeof ref === 'function') {
      ref(node)
    } else if (ref) {
      ref.current = node
    }
  }, [ref])

  // Sync external value changes
  useEffect(() => {
    setLocalValue(value)
  }, [value])

  // Debounced onChange handler
  useEffect(() => {
    if (debounceTimeoutRef.current) {
      clearTimeout(debounceTimeoutRef.current)
    }

    debounceTimeoutRef.current = setTimeout(() => {
      if (onChange && localValue !== value) {
        onChange(localValue)
      }
    }, debounceMs)

    return () => {
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current)
      }
    }
  }, [localValue, onChange, debounceMs, value])

  // Auto focus
  useEffect(() => {
    if (autoFocus && inputRef.current) {
      inputRef.current.focus()
    }
  }, [autoFocus])

  const handleInputChange = (e) => {
    setLocalValue(e.target.value)
  }

  const handleClear = () => {
    setLocalValue('')
    if (onClear) {
      onClear()
    } else if (onChange) {
      onChange('')
    }
    if (inputRef.current) {
      inputRef.current.focus()
    }
  }

  const handleFocus = (e) => {
    setIsFocused(true)
    props.onFocus?.(e)
  }

  const handleBlur = (e) => {
    setIsFocused(false)
    props.onBlur?.(e)
  }

  const handleKeyDown = (e) => {
    if (e.key === 'Escape') {
      if (localValue) {
        handleClear()
      } else {
        inputRef.current?.blur()
      }
    }
    props.onKeyDown?.(e)
  }

  // Build CSS classes
  const baseClass = 'apollo-search-input'
  const sizeClass = `apollo-search-input--${size}`
  const stateClasses = [
    disabled && 'apollo-search-input--disabled',
    isFocused && 'apollo-search-input--focused',
    fullWidth && 'apollo-search-input--full-width',
    localValue && 'apollo-search-input--has-value'
  ].filter(Boolean)

  const containerClasses = [baseClass, sizeClass, ...stateClasses, className]
    .filter(Boolean)
    .join(' ')

  return (
    <div className={containerClasses} data-testid={dataTestId}>
      <div className="apollo-search-input__icon apollo-search-input__icon--search">
        <Icon name="search" size={size === 'sm' ? 14 : size === 'lg' ? 18 : 16} />
      </div>
      
      <input
        ref={mergedRef}
        type="text"
        className="apollo-search-input__input"
        value={localValue}
        onChange={handleInputChange}
        onFocus={handleFocus}
        onBlur={handleBlur}
        onKeyDown={handleKeyDown}
        placeholder={placeholder}
        disabled={disabled}
        aria-label={ariaLabel || placeholder}
        {...props}
      />
      
      {localValue && !disabled && (
        <button
          type="button"
          className="apollo-search-input__icon apollo-search-input__icon--clear"
          onClick={handleClear}
          aria-label="Clear search"
          tabIndex={-1}
        >
          <Icon name="close" size={size === 'sm' ? 14 : size === 'lg' ? 18 : 16} />
        </button>
      )}
    </div>
  )
})

SearchInput.displayName = 'SearchInput'

export default SearchInput