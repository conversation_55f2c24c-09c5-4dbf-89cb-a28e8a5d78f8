import Badge from './Badge.jsx'

// Usage example demonstrating the Badge component
function BadgeExample() {
  return (
    <div style={{ padding: '20px', display: 'flex', flexDirection: 'column', gap: '20px' }}>
      <h2>Badge Component Examples</h2>
      
      <div>
        <h3>Status Variants</h3>
        <div style={{ display: 'flex', gap: '10px', flexWrap: 'wrap' }}>
          <Badge variant="approved">Approved</Badge>
          <Badge variant="planned">Planned</Badge>
          <Badge variant="review">Under Review</Badge>
          <Badge variant="development">In Development</Badge>
          <Badge variant="suggested">Suggested</Badge>
          <Badge variant="default">Default Status</Badge>
        </div>
      </div>

      <div>
        <h3>Size Options</h3>
        <div style={{ display: 'flex', gap: '10px', alignItems: 'center' }}>
          <Badge variant="approved" size="sm">Small</Badge>
          <Badge variant="approved" size="md">Medium</Badge>
          <Badge variant="approved" size="lg">Large</Badge>
        </div>
      </div>

      <div>
        <h3>Real-world Examples</h3>
        <div style={{ display: 'flex', gap: '10px', flexWrap: 'wrap' }}>
          <Badge variant="approved" size="sm">✅ Published</Badge>
          <Badge variant="review" size="sm">⏳ Pending Review</Badge>
          <Badge variant="development" size="sm">🔧 In Progress</Badge>
          <Badge variant="suggested" size="sm">💡 Draft</Badge>
        </div>
      </div>

      <div>
        <h3>With Custom Classes</h3>
        <div style={{ display: 'flex', gap: '10px' }}>
          <Badge variant="approved" className="custom-badge">Custom Styled</Badge>
          <Badge variant="planned" size="lg" className="highlight">Large Highlight</Badge>
        </div>
      </div>
    </div>
  )
}

export default BadgeExample