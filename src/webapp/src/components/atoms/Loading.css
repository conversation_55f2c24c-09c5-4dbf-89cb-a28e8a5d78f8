/* Loading Component Styles */
.apollo-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: var(--apollo-space-md);
  font-family: var(--apollo-font-ui);
}

/* Size Variants */
.apollo-loading--xs {
  --loading-size: 16px;
  --loading-text-size: var(--apollo-text-xs);
  gap: var(--apollo-space-xs);
}

.apollo-loading--sm {
  --loading-size: 20px;
  --loading-text-size: var(--apollo-text-sm);
  gap: var(--apollo-space-sm);
}

.apollo-loading--md {
  --loading-size: 24px;
  --loading-text-size: var(--apollo-text-base);
  gap: var(--apollo-space-md);
}

.apollo-loading--lg {
  --loading-size: 32px;
  --loading-text-size: var(--apollo-text-lg);
  gap: var(--apollo-space-lg);
}

.apollo-loading--xl {
  --loading-size: 40px;
  --loading-text-size: var(--apollo-text-xl);
  gap: var(--apollo-space-xl);
}

/* Color Variants */
.apollo-loading--primary {
  --loading-color: var(--apollo-primary);
  --loading-text-color: var(--apollo-text-primary);
}

.apollo-loading--secondary {
  --loading-color: var(--apollo-text-secondary);
  --loading-text-color: var(--apollo-text-secondary);
}

.apollo-loading--success {
  --loading-color: var(--apollo-success);
  --loading-text-color: var(--apollo-text-primary);
}

.apollo-loading--warning {
  --loading-color: var(--apollo-warning);
  --loading-text-color: var(--apollo-text-primary);
}

.apollo-loading--error {
  --loading-color: var(--apollo-error);
  --loading-text-color: var(--apollo-text-primary);
}

/* Loading Text */
.apollo-loading__text {
  font-size: var(--loading-text-size);
  color: var(--loading-text-color);
  font-weight: var(--apollo-font-medium);
  text-align: center;
}

/* Spinner Variant */
.apollo-loading__spinner {
  width: var(--loading-size);
  height: var(--loading-size);
  position: relative;
}

.apollo-loading__spinner-circle {
  width: 100%;
  height: 100%;
  border: 2px solid var(--apollo-border-light);
  border-top: 2px solid var(--loading-color);
  border-radius: 50%;
  animation: apollo-loading-spin 1s linear infinite;
}

@keyframes apollo-loading-spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Dots Variant */
.apollo-loading__dots {
  display: flex;
  gap: calc(var(--loading-size) * 0.2);
  align-items: center;
}

.apollo-loading__dot {
  width: calc(var(--loading-size) * 0.3);
  height: calc(var(--loading-size) * 0.3);
  background: var(--loading-color);
  border-radius: 50%;
  animation: apollo-loading-dots 1.4s ease-in-out infinite both;
}

.apollo-loading__dot:nth-child(1) {
  animation-delay: -0.32s;
}

.apollo-loading__dot:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes apollo-loading-dots {
  0%, 80%, 100% {
    transform: scale(0);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Pulse Variant */
.apollo-loading__pulse {
  width: var(--loading-size);
  height: var(--loading-size);
  position: relative;
}

.apollo-loading__pulse-circle {
  width: 100%;
  height: 100%;
  background: var(--loading-color);
  border-radius: 50%;
  animation: apollo-loading-pulse 2s ease-in-out infinite;
}

@keyframes apollo-loading-pulse {
  0% {
    transform: scale(0);
    opacity: 1;
  }
  100% {
    transform: scale(1);
    opacity: 0;
  }
}

/* Skeleton Variant */
.apollo-loading__skeleton {
  display: flex;
  flex-direction: column;
  gap: calc(var(--loading-size) * 0.3);
  width: 100%;
  max-width: 200px;
}

.apollo-loading__skeleton-line {
  height: calc(var(--loading-size) * 0.4);
  background: linear-gradient(
    90deg,
    var(--apollo-bg-tertiary) 25%,
    var(--apollo-bg-card-hover) 50%,
    var(--apollo-bg-tertiary) 75%
  );
  background-size: 200% 100%;
  border-radius: var(--apollo-radius-xs);
  animation: apollo-loading-skeleton 2s ease-in-out infinite;
}

.apollo-loading__skeleton-line--short {
  width: 60%;
}

.apollo-loading__skeleton-line--medium {
  width: 80%;
}

@keyframes apollo-loading-skeleton {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Overlay Styles */
.apollo-loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: var(--apollo-z-overlay);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--apollo-space-2xl);
}

.apollo-loading-overlay__backdrop {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(2px);
}

.apollo-loading-overlay__content {
  position: relative;
  background: var(--apollo-bg-card);
  border: 1px solid var(--apollo-border-light);
  border-radius: var(--apollo-radius-lg);
  padding: var(--apollo-space-2xl);
  box-shadow: var(--apollo-shadow-xl);
  max-width: 90vw;
  max-height: 90vh;
}

/* Inline Loading (for forms, buttons, etc.) */
.apollo-loading--inline {
  display: inline-flex;
  vertical-align: middle;
}

/* Button Loading State */
.apollo-loading--button {
  --loading-size: 16px;
  --loading-text-size: inherit;
  gap: var(--apollo-space-sm);
}

/* Card Loading State */
.apollo-loading--card {
  padding: var(--apollo-space-2xl);
  min-height: 120px;
}

/* Table Loading State */
.apollo-loading--table {
  padding: var(--apollo-space-xl);
  width: 100%;
}

.apollo-loading--table .apollo-loading__skeleton {
  max-width: 100%;
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
  .apollo-loading__spinner-circle {
    border-width: 3px;
  }
  
  .apollo-loading__dot {
    outline: 1px solid var(--apollo-text-primary);
  }
  
  .apollo-loading__skeleton-line {
    border: 1px solid var(--apollo-border-medium);
  }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  .apollo-loading__spinner-circle,
  .apollo-loading__dot,
  .apollo-loading__pulse-circle,
  .apollo-loading__skeleton-line {
    animation: none;
  }
  
  /* Provide static alternative for reduced motion */
  .apollo-loading--spinner .apollo-loading__spinner-circle {
    border-top-color: var(--loading-color);
    border-right-color: var(--loading-color);
  }
  
  .apollo-loading--dots .apollo-loading__dot {
    opacity: 0.6;
  }
  
  .apollo-loading--dots .apollo-loading__dot:nth-child(2) {
    opacity: 0.8;
  }
  
  .apollo-loading--dots .apollo-loading__dot:nth-child(3) {
    opacity: 1;
  }
  
  .apollo-loading--pulse .apollo-loading__pulse-circle {
    opacity: 0.8;
  }
  
  .apollo-loading--skeleton .apollo-loading__skeleton-line {
    background: var(--apollo-bg-card-hover);
  }
}

/* Focus Management for Overlay */
.apollo-loading-overlay {
  outline: none;
}

.apollo-loading-overlay:focus-visible {
  outline: 2px solid var(--apollo-primary);
  outline-offset: -2px;
}