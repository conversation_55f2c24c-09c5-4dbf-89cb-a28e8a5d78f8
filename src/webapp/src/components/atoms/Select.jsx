import { useState, useEffect, useRef, forwardRef, useCallback } from 'react'
import { Icon } from './index'
import './Select.css'

const Select = forwardRef(function Select({
  value,
  onChange,
  options = [],
  placeholder = 'Select an option...',
  disabled = false,
  multiple = false,
  searchable = false,
  clearable = false,
  size = 'md',
  fullWidth = false,
  className = '',
  'data-testid': dataTestId,
  'aria-label': ariaLabel,
  ...props
}, ref) {
  const [isOpen, setIsOpen] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')
  const [focusedIndex, setFocusedIndex] = useState(-1)
  const [highlightedIndex, setHighlightedIndex] = useState(-1)
  
  const containerRef = useRef(null)
  const inputRef = useRef(null)
  const optionsRef = useRef(null)

  // Merge refs
  const mergedRef = useCallback((node) => {
    containerRef.current = node
    if (typeof ref === 'function') {
      ref(node)
    } else if (ref) {
      ref.current = node
    }
  }, [ref])

  // Normalize options to have consistent structure
  const normalizedOptions = options.map((option, index) => {
    if (typeof option === 'string') {
      return { value: option, label: option, id: `option-${index}` }
    }
    return { 
      id: option.id || `option-${index}`,
      value: option.value,
      label: option.label || option.value,
      disabled: option.disabled || false,
      count: option.count
    }
  })

  // Filter options based on search term
  const filteredOptions = searchable 
    ? normalizedOptions.filter(option => 
        option.label.toLowerCase().includes(searchTerm.toLowerCase())
      )
    : normalizedOptions

  // Get selected option(s) for display
  const getSelectedOptions = () => {
    if (multiple) {
      const valueArray = Array.isArray(value) ? value : (value ? [value] : [])
      return normalizedOptions.filter(option => valueArray.includes(option.value))
    } else {
      return normalizedOptions.find(option => option.value === value) || null
    }
  }

  const selectedOptions = getSelectedOptions()

  // Handle outside click
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (containerRef.current && !containerRef.current.contains(event.target)) {
        setIsOpen(false)
        setSearchTerm('')
        setHighlightedIndex(-1)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  // Handle escape key
  useEffect(() => {
    const handleEscape = (event) => {
      if (event.key === 'Escape' && isOpen) {
        setIsOpen(false)
        setSearchTerm('')
        setHighlightedIndex(-1)
        containerRef.current?.focus()
      }
    }

    document.addEventListener('keydown', handleEscape)
    return () => document.removeEventListener('keydown', handleEscape)
  }, [isOpen])

  // Reset highlighted index when options change
  useEffect(() => {
    setHighlightedIndex(-1)
  }, [searchTerm])

  const handleToggle = () => {
    if (disabled) return
    setIsOpen(!isOpen)
    if (!isOpen) {
      setSearchTerm('')
      setHighlightedIndex(-1)
    }
  }

  const handleOptionClick = (option) => {
    if (option.disabled) return

    if (multiple) {
      const valueArray = Array.isArray(value) ? value : (value ? [value] : [])
      const newValue = valueArray.includes(option.value)
        ? valueArray.filter(v => v !== option.value)
        : [...valueArray, option.value]
      onChange?.(newValue)
    } else {
      onChange?.(option.value)
      setIsOpen(false)
      setSearchTerm('')
    }
    setHighlightedIndex(-1)
  }

  const handleClear = (e) => {
    e.stopPropagation()
    onChange?.(multiple ? [] : '')
    setSearchTerm('')
  }

  const handleKeyDown = (e) => {
    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault()
        if (!isOpen) {
          setIsOpen(true)
        } else {
          setHighlightedIndex(prev => 
            prev < filteredOptions.length - 1 ? prev + 1 : 0
          )
        }
        break
      
      case 'ArrowUp':
        e.preventDefault()
        if (isOpen) {
          setHighlightedIndex(prev => 
            prev > 0 ? prev - 1 : filteredOptions.length - 1
          )
        }
        break
      
      case 'Enter':
        e.preventDefault()
        if (isOpen && highlightedIndex >= 0) {
          handleOptionClick(filteredOptions[highlightedIndex])
        } else if (!isOpen) {
          setIsOpen(true)
        }
        break
      
      case 'Tab':
        if (isOpen) {
          setIsOpen(false)
          setSearchTerm('')
        }
        break
      
      default:
        if (searchable && isOpen && e.key.length === 1) {
          setSearchTerm(prev => prev + e.key)
        }
        break
    }
  }

  const handleSearchChange = (e) => {
    setSearchTerm(e.target.value)
    setHighlightedIndex(-1)
  }

  // Build display text
  const getDisplayText = () => {
    if (multiple) {
      if (!selectedOptions || selectedOptions.length === 0) {
        return placeholder
      }
      if (selectedOptions.length === 1) {
        return selectedOptions[0].label
      }
      return `${selectedOptions.length} selected`
    } else {
      return selectedOptions ? selectedOptions.label : placeholder
    }
  }

  // Build CSS classes
  const baseClass = 'apollo-select'
  const sizeClass = `apollo-select--${size}`
  const stateClasses = [
    disabled && 'apollo-select--disabled',
    isOpen && 'apollo-select--open',
    fullWidth && 'apollo-select--full-width',
    (multiple ? selectedOptions.length > 0 : selectedOptions) && 'apollo-select--has-value'
  ].filter(Boolean)

  const containerClasses = [baseClass, sizeClass, ...stateClasses, className]
    .filter(Boolean)
    .join(' ')

  return (
    <div 
      ref={mergedRef}
      className={containerClasses}
      data-testid={dataTestId}
      {...props}
    >
      <div
        className="apollo-select__trigger"
        onClick={handleToggle}
        onKeyDown={handleKeyDown}
        tabIndex={disabled ? -1 : 0}
        aria-label={ariaLabel}
        aria-expanded={isOpen}
        aria-haspopup="listbox"
        role="combobox"
      >
        <span className="apollo-select__value">
          {getDisplayText()}
        </span>
        
        <div className="apollo-select__icons">
          {clearable && (multiple ? selectedOptions.length > 0 : selectedOptions) && !disabled && (
            <button
              type="button"
              className="apollo-select__clear"
              onClick={handleClear}
              aria-label="Clear selection"
              tabIndex={-1}
            >
              <Icon name="close" size={size === 'sm' ? 14 : size === 'lg' ? 18 : 16} />
            </button>
          )}
          
          <div className={`apollo-select__arrow ${isOpen ? 'apollo-select__arrow--open' : ''}`}>
            <Icon name="arrow-down" size={size === 'sm' ? 14 : size === 'lg' ? 18 : 16} />
          </div>
        </div>
      </div>

      {isOpen && (
        <div className="apollo-select__dropdown" ref={optionsRef}>
          {searchable && (
            <div className="apollo-select__search">
              <input
                ref={inputRef}
                type="text"
                className="apollo-select__search-input"
                placeholder="Search options..."
                value={searchTerm}
                onChange={handleSearchChange}
                autoFocus
              />
            </div>
          )}
          
          <div className="apollo-select__options" role="listbox" aria-multiselectable={multiple}>
            {filteredOptions.length === 0 ? (
              <div className="apollo-select__no-options">
                {searchTerm ? 'No matches found' : 'No options available'}
              </div>
            ) : (
              filteredOptions.map((option, index) => {
                const isSelected = multiple 
                  ? (Array.isArray(value) ? value : []).includes(option.value)
                  : value === option.value
                const isHighlighted = index === highlightedIndex

                return (
                  <div
                    key={option.id}
                    className={`apollo-select__option ${
                      isSelected ? 'apollo-select__option--selected' : ''
                    } ${
                      isHighlighted ? 'apollo-select__option--highlighted' : ''
                    } ${
                      option.disabled ? 'apollo-select__option--disabled' : ''
                    }`}
                    onClick={() => handleOptionClick(option)}
                    role="option"
                    aria-selected={isSelected}
                    aria-disabled={option.disabled}
                  >
                    <span className="apollo-select__option-label">
                      {option.label}
                    </span>
                    {option.count !== undefined && (
                      <span className="apollo-select__option-count">
                        {option.count}
                      </span>
                    )}
                    {multiple && isSelected && (
                      <div className="apollo-select__option-check">
                        <Icon name="check" size={16} />
                      </div>
                    )}
                  </div>
                )
              })
            )}
          </div>
        </div>
      )}
    </div>
  )
})

Select.displayName = 'Select'

export default Select