import './Loading.css'

const Loading = ({ 
  size = 'md', 
  variant = 'spinner', 
  color = 'primary', 
  overlay = false, 
  text = '', 
  className = '',
  ...props 
}) => {
  const loadingClasses = [
    'apollo-loading',
    `apollo-loading--${variant}`,
    `apollo-loading--${size}`,
    `apollo-loading--${color}`,
    overlay && 'apollo-loading--overlay',
    className
  ].filter(Boolean).join(' ')

  const renderSpinner = () => (
    <div className="apollo-loading__spinner" aria-hidden="true">
      <div className="apollo-loading__spinner-circle"></div>
    </div>
  )

  const renderSkeleton = () => (
    <div className="apollo-loading__skeleton" aria-hidden="true">
      <div className="apollo-loading__skeleton-line"></div>
      <div className="apollo-loading__skeleton-line apollo-loading__skeleton-line--short"></div>
      <div className="apollo-loading__skeleton-line apollo-loading__skeleton-line--medium"></div>
    </div>
  )

  const renderDots = () => (
    <div className="apollo-loading__dots" aria-hidden="true">
      <div className="apollo-loading__dot"></div>
      <div className="apollo-loading__dot"></div>
      <div className="apollo-loading__dot"></div>
    </div>
  )

  const renderPulse = () => (
    <div className="apollo-loading__pulse" aria-hidden="true">
      <div className="apollo-loading__pulse-circle"></div>
    </div>
  )

  const renderLoadingIndicator = () => {
    switch (variant) {
      case 'skeleton':
        return renderSkeleton()
      case 'dots':
        return renderDots()
      case 'pulse':
        return renderPulse()
      case 'spinner':
      default:
        return renderSpinner()
    }
  }

  const loadingContent = (
    <div className={loadingClasses}>
      {renderLoadingIndicator()}
      {text && (
        <div className="apollo-loading__text" aria-live="polite">
          {text}
        </div>
      )}
    </div>
  )

  // If overlay mode, wrap in overlay container
  if (overlay) {
    return (
      <div 
        className="apollo-loading-overlay" 
        role="status" 
        aria-label={text || 'Loading'}
        aria-live="polite"
        {...props}
      >
        <div className="apollo-loading-overlay__backdrop"></div>
        <div className="apollo-loading-overlay__content">
          {loadingContent}
        </div>
      </div>
    )
  }

  return (
    <div role="status" aria-label={text || 'Loading'} aria-live="polite" {...props}>
      {loadingContent}
    </div>
  )
}

export default Loading