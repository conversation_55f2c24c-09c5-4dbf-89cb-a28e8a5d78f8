/* Apollo Badge Component */
.apollo-badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-weight: var(--apollo-font-medium);
  border-radius: var(--apollo-radius-xs);
  border: 1px solid transparent;
  white-space: nowrap;
  flex-shrink: 0;
  font-family: var(--apollo-font-ui);
  transition: all var(--apollo-transition-normal);
}

/* Size Variants */
.apollo-badge--sm {
  font-size: var(--apollo-text-xs);
  padding: 2px 6px;
  line-height: var(--apollo-leading-tight);
}

.apollo-badge--md {
  font-size: var(--apollo-text-sm);
  padding: 4px 8px;
  line-height: var(--apollo-leading-snug);
}

.apollo-badge--lg {
  font-size: var(--apollo-text-base);
  padding: 6px 12px;
  line-height: var(--apollo-leading-normal);
}

/* Status Variants */
.apollo-badge--approved {
  background: var(--apollo-success-bg);
  color: var(--apollo-success);
  border-color: var(--apollo-success);
}

.apollo-badge--planned {
  background: var(--apollo-info-bg);
  color: var(--apollo-info);
  border-color: var(--apollo-info);
}

.apollo-badge--review {
  background: var(--apollo-warning-bg);
  color: var(--apollo-warning);
  border-color: var(--apollo-warning);
}

.apollo-badge--development {
  background: var(--apollo-purple-bg);
  color: var(--apollo-purple);
  border-color: var(--apollo-purple);
}

.apollo-badge--suggested {
  background: var(--apollo-neutral-bg);
  color: var(--apollo-neutral);
  border-color: var(--apollo-neutral);
}

.apollo-badge--default {
  background: var(--apollo-badge-bg);
  color: var(--apollo-badge-text);
  border-color: var(--apollo-badge-border);
}

/* Hover Effects */
.apollo-badge:hover {
  transform: translateY(-1px);
  box-shadow: var(--apollo-shadow-sm);
}

/* Responsive Sizing */
@media (max-width: 768px) {
  .apollo-badge--lg {
    font-size: var(--apollo-text-sm);
    padding: 4px 8px;
  }
  
  .apollo-badge--md {
    font-size: var(--apollo-text-xs);
    padding: 3px 6px;
  }
  
  .apollo-badge--sm {
    font-size: 10px;
    padding: 2px 4px;
  }
}

@media (max-width: 480px) {
  .apollo-badge--lg {
    font-size: var(--apollo-text-xs);
    padding: 3px 6px;
  }
  
  .apollo-badge--md {
    font-size: 11px;
    padding: 2px 5px;
  }
  
  .apollo-badge--sm {
    font-size: 10px;
    padding: 1px 4px;
  }
}