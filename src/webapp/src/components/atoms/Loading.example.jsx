import React, { useState } from 'react'
import Loading from './Loading'

const LoadingExample = () => {
  const [showOverlay, setShowOverlay] = useState(false)

  return (
    <div style={{ padding: '2rem', backgroundColor: '#1F2025', color: '#e0e0e0' }}>
      <h2>Loading Component Examples</h2>
      
      <section style={{ marginBottom: '2rem' }}>
        <h3>Basic Spinner</h3>
        <div style={{ display: 'flex', gap: '2rem', alignItems: 'center', flexWrap: 'wrap' }}>
          <Loading />
          <Loading text="Loading..." />
          <Loading text="Processing data..." />
        </div>
      </section>

      <section style={{ marginBottom: '2rem' }}>
        <h3>Sizes</h3>
        <div style={{ display: 'flex', gap: '2rem', alignItems: 'center', flexWrap: 'wrap' }}>
          <Loading size="xs" text="XS" />
          <Loading size="sm" text="SM" />
          <Loading size="md" text="MD" />
          <Loading size="lg" text="LG" />
          <Loading size="xl" text="XL" />
        </div>
      </section>

      <section style={{ marginBottom: '2rem' }}>
        <h3>Colors</h3>
        <div style={{ display: 'flex', gap: '2rem', alignItems: 'center', flexWrap: 'wrap' }}>
          <Loading color="primary" text="Primary" />
          <Loading color="secondary" text="Secondary" />
          <Loading color="success" text="Success" />
          <Loading color="warning" text="Warning" />
          <Loading color="error" text="Error" />
        </div>
      </section>

      <section style={{ marginBottom: '2rem' }}>
        <h3>Variants</h3>
        <div style={{ display: 'flex', gap: '2rem', alignItems: 'center', flexWrap: 'wrap' }}>
          <Loading variant="spinner" text="Spinner" />
          <Loading variant="dots" text="Dots" />
          <Loading variant="pulse" text="Pulse" />
        </div>
      </section>

      <section style={{ marginBottom: '2rem' }}>
        <h3>Skeleton Variant</h3>
        <div style={{ display: 'flex', gap: '2rem', alignItems: 'center', flexWrap: 'wrap' }}>
          <Loading variant="skeleton" />
          <Loading variant="skeleton" text="Loading content..." />
        </div>
      </section>

      <section style={{ marginBottom: '2rem' }}>
        <h3>In Button Context</h3>
        <div style={{ display: 'flex', gap: '1rem', alignItems: 'center', flexWrap: 'wrap' }}>
          <button 
            style={{ 
              padding: '0.75rem 1.5rem', 
              backgroundColor: '#007B63', 
              color: 'white', 
              border: 'none', 
              borderRadius: '6px',
              display: 'flex',
              alignItems: 'center',
              gap: '0.5rem',
              cursor: 'pointer'
            }}
          >
            <Loading variant="spinner" size="xs" color="secondary" className="apollo-loading--button" />
            Creating...
          </button>
          
          <button 
            style={{ 
              padding: '0.75rem 1.5rem', 
              backgroundColor: '#10b981', 
              color: 'white', 
              border: 'none', 
              borderRadius: '6px',
              display: 'flex',
              alignItems: 'center',
              gap: '0.5rem',
              cursor: 'pointer'
            }}
          >
            <Loading variant="dots" size="xs" color="secondary" className="apollo-loading--button" />
            Saving...
          </button>
        </div>
      </section>

      <section style={{ marginBottom: '2rem' }}>
        <h3>Card Loading</h3>
        <div style={{ 
          backgroundColor: '#282A2C', 
          border: '1px solid rgba(255, 255, 255, 0.1)',
          borderRadius: '8px',
          padding: '1rem',
          minHeight: '200px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center'
        }}>
          <Loading variant="skeleton" text="Loading card content..." className="apollo-loading--card" />
        </div>
      </section>

      <section style={{ marginBottom: '2rem' }}>
        <h3>Table Loading</h3>
        <div style={{ 
          backgroundColor: '#282A2C', 
          border: '1px solid rgba(255, 255, 255, 0.1)',
          borderRadius: '8px',
          padding: '1rem',
          minHeight: '150px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center'
        }}>
          <Loading variant="spinner" text="Loading table data..." className="apollo-loading--table" />
        </div>
      </section>

      <section style={{ marginBottom: '2rem' }}>
        <h3>Inline Loading</h3>
        <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>
          <span>Processing your request</span>
          <Loading variant="dots" size="sm" className="apollo-loading--inline" />
        </div>
      </section>

      <section style={{ marginBottom: '2rem' }}>
        <h3>Overlay Mode</h3>
        <button 
          onClick={() => setShowOverlay(true)}
          style={{ 
            padding: '0.75rem 1.5rem', 
            backgroundColor: '#8b5cf6', 
            color: 'white', 
            border: 'none', 
            borderRadius: '6px',
            cursor: 'pointer'
          }}
        >
          Show Overlay Loading
        </button>
        
        {showOverlay && (
          <Loading 
            overlay={true} 
            variant="spinner" 
            size="lg" 
            color="primary" 
            text="Processing your request..." 
          />
        )}
        
        {showOverlay && (
          <div style={{ 
            position: 'fixed', 
            top: '20px', 
            right: '20px', 
            zIndex: 1001 
          }}>
            <button 
              onClick={() => setShowOverlay(false)}
              style={{ 
                padding: '0.5rem 1rem', 
                backgroundColor: '#ff6b6b', 
                color: 'white', 
                border: 'none', 
                borderRadius: '4px',
                cursor: 'pointer'
              }}
            >
              Close Overlay
            </button>
          </div>
        )}
      </section>

      <section style={{ marginBottom: '2rem' }}>
        <h3>Real-world Examples</h3>
        <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
          {/* Login Form Example */}
          <div style={{ 
            backgroundColor: '#282A2C', 
            padding: '1.5rem', 
            borderRadius: '8px',
            border: '1px solid rgba(255, 255, 255, 0.1)',
            position: 'relative'
          }}>
            <h4>Login Form</h4>
            <div style={{ 
              display: 'flex', 
              flexDirection: 'column', 
              gap: '0.5rem',
              opacity: 0.3
            }}>
              <div style={{ padding: '0.5rem', backgroundColor: '#1F2025', borderRadius: '4px' }}>User 1</div>
              <div style={{ padding: '0.5rem', backgroundColor: '#1F2025', borderRadius: '4px' }}>User 2</div>
              <div style={{ padding: '0.5rem', backgroundColor: '#1F2025', borderRadius: '4px' }}>User 3</div>
            </div>
            <div style={{
              position: 'absolute',
              top: '50%',
              left: '50%',
              transform: 'translate(-50%, -50%)'
            }}>
              <Loading variant="spinner" size="md" color="primary" text="Signing you in..." />
            </div>
          </div>

          {/* API Request Example */}
          <div style={{ 
            backgroundColor: '#282A2C', 
            padding: '1.5rem', 
            borderRadius: '8px',
            border: '1px solid rgba(255, 255, 255, 0.1)'
          }}>
            <h4>API Data Fetch</h4>
            <Loading variant="skeleton" text="Fetching prompts..." />
          </div>
        </div>
      </section>

      <section>
        <h3>Accessibility Features</h3>
        <div style={{ 
          backgroundColor: '#282A2C', 
          padding: '1rem', 
          borderRadius: '8px',
          border: '1px solid rgba(255, 255, 255, 0.1)'
        }}>
          <p style={{ fontSize: '0.9rem', color: '#b0b0b0', marginBottom: '1rem' }}>
            All loading components include proper ARIA attributes:
          </p>
          <ul style={{ fontSize: '0.9rem', color: '#b0b0b0' }}>
            <li>role="status" for screen readers</li>
            <li>aria-label with descriptive text</li>
            <li>aria-live="polite" for dynamic updates</li>
            <li>aria-hidden="true" on decorative elements</li>
          </ul>
          <Loading variant="pulse" text="Screen readers will announce this loading state" />
        </div>
      </section>
    </div>
  )
}

export default LoadingExample