/* Apollo Filter Select Component */

/* === FILTER SELECT SPECIFIC STYLES === */
.apollo-filter-select {
  /* Inherits all styles from apollo-select */
}

/* === ALL OPTION STYLING === */
.apollo-filter-select .apollo-select__option[aria-selected="true"] {
  /* Override selected styling for "All" option when it represents no selection */
  background-color: var(--apollo-bg-card-hover);
  color: var(--apollo-text-primary);
  font-weight: var(--apollo-font-normal);
}

/* Enhanced option styling for filter context */
.apollo-filter-select .apollo-select__option {
  position: relative;
}

/* Count badges get special treatment in filter context */
.apollo-filter-select .apollo-select__option-count {
  font-weight: var(--apollo-font-semibold);
  min-width: 20px;
  text-align: center;
}

/* "All" option gets special visual treatment */
.apollo-filter-select .apollo-select__option[data-is-all="true"] {
  border-bottom: 1px solid var(--apollo-border-light);
  margin-bottom: 4px;
  padding-bottom: 12px;
}

.apollo-filter-select .apollo-select__option[data-is-all="true"] .apollo-select__option-count {
  background-color: var(--apollo-primary-light);
  color: var(--apollo-primary);
}

/* Enhanced count display for active filters */
.apollo-filter-select .apollo-select__option--selected .apollo-select__option-count {
  background-color: var(--apollo-primary);
  color: var(--apollo-text-white);
  font-weight: var(--apollo-font-bold);
}

/* Multi-select specific styling */
.apollo-filter-select.apollo-select--multiple .apollo-select__value {
  /* Custom display for multiple filter selections */
}

/* Filter summary in trigger */
.apollo-filter-select .apollo-select__value--summary {
  color: var(--apollo-primary);
  font-weight: var(--apollo-font-medium);
}

/* Zero state when no filters applied */
.apollo-filter-select:not(.apollo-select--has-value) .apollo-select__value {
  color: var(--apollo-text-secondary);
  font-style: italic;
}

/* Active filter state */
.apollo-filter-select.apollo-select--has-value .apollo-select__trigger {
  border-color: var(--apollo-primary-light);
  background-color: var(--apollo-primary-lightest);
}

.apollo-filter-select.apollo-select--has-value .apollo-select__value {
  color: var(--apollo-primary);
  font-weight: var(--apollo-font-medium);
}

/* Filter badge in trigger for multiple selections */
.apollo-filter-select__active-count {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 18px;
  height: 18px;
  background-color: var(--apollo-primary);
  color: var(--apollo-text-white);
  border-radius: var(--apollo-radius-full);
  font-size: var(--apollo-text-xs);
  font-weight: var(--apollo-font-bold);
  margin-left: var(--apollo-space-xs);
}

/* === RESPONSIVE ADJUSTMENTS === */
@media (max-width: 768px) {
  .apollo-filter-select .apollo-select__option-count {
    font-size: 10px;
    padding: 1px 4px;
    min-width: 16px;
  }
  
  .apollo-filter-select__active-count {
    min-width: 16px;
    height: 16px;
    font-size: 10px;
  }
}

/* === HIGH CONTRAST MODE === */
@media (prefers-contrast: high) {
  .apollo-filter-select.apollo-select--has-value .apollo-select__trigger {
    border-width: 2px;
  }
  
  .apollo-filter-select .apollo-select__option[data-is-all="true"] {
    border-bottom-width: 2px;
  }
}

/* === PRINT STYLES === */
@media print {
  .apollo-filter-select__active-count {
    background: black !important;
    color: white !important;
  }
}