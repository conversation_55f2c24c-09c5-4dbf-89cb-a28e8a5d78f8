import React from 'react'
import './ExampleCard.css'

/**
 * ExampleCard - Displays input/output examples in a structured format
 * 
 * @param {Object} props
 * @param {Array} props.examples - Array of example objects with input/output
 * @param {Function} props.onExampleEdit - Optional callback when example is edited (future use)
 * @param {Function} props.onExampleDelete - Optional callback when example is deleted (future use)
 * @param {boolean} props.showActions - Whether to show edit/delete actions
 * @param {string} props.variant - Display variant: 'default' | 'compact'
 */
const ExampleCard = React.memo(function ExampleCard({
  examples = [],
  onExampleEdit,
  onExampleDelete,
  showActions = false,
  variant = 'default'
}) {
  if (!examples || examples.length === 0) {
    return (
      <div className="examples-section">
        <h3>Input Output Examples</h3>
        <div className="examples-empty">
          <p>No examples available for this prompt.</p>
        </div>
      </div>
    )
  }

  return (
    <div className="examples-section">
      <h3>Input Output Examples</h3>
      <div className="examples-container">
        {examples.map((example, index) => (
          <div key={index} className={`example-item ${variant}`}>
            {showActions && (
              <div className="example-actions">
                {onExampleEdit && (
                  <button 
                    className="example-action-button edit"
                    onClick={() => onExampleEdit(index, example)}
                    aria-label="Edit example"
                  >
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                      <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                      <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    </svg>
                  </button>
                )}
                {onExampleDelete && (
                  <button 
                    className="example-action-button delete"
                    onClick={() => onExampleDelete(index, example)}
                    aria-label="Delete example"
                  >
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                      <path d="M3 6h18M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    </svg>
                  </button>
                )}
              </div>
            )}
            
            <div className="example-content-two-column">
              <div className="example-input-column">
                <h5>Input</h5>
                <div className="example-text">{example.input}</div>
              </div>
              <div className="example-divider"></div>
              <div className="example-output-column">
                <h5>Output</h5>
                <div className="example-text">
                  <pre>{example.output}</pre>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  )
})

export default ExampleCard