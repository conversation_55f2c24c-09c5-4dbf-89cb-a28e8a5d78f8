import './Badge.css'

function Badge({ variant = 'default', size = 'md', children, className = '' }) {
  const baseClass = 'apollo-badge'
  const variantClass = `apollo-badge--${variant}`
  const sizeClass = `apollo-badge--${size}`
  const allClasses = [baseClass, variantClass, sizeClass, className].filter(Boolean).join(' ')

  return (
    <span className={allClasses}>
      {children}
    </span>
  )
}

export default Badge