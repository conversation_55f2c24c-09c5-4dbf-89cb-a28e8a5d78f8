import { useState, useMemo } from 'react'
import './Avatar.css'

const Avatar = ({ 
  src, 
  alt, 
  size = 'md', 
  fallbackText = '', 
  variant = 'circle', 
  className = '',
  name = '',
  email = '',
  ...props 
}) => {
  const [imageError, setImageError] = useState(false)
  const [imageLoading, setImageLoading] = useState(true)

  const displayText = useMemo(() => {
    if (fallbackText) return fallbackText
    
    if (name) {
      const nameParts = name.trim().split(' ')
      if (nameParts.length >= 2) {
        return `${nameParts[0][0]}${nameParts[nameParts.length - 1][0]}`.toUpperCase()
      }
      return nameParts[0].substring(0, 2).toUpperCase()
    }
    
    if (email) {
      const emailParts = email.split('@')[0]
      return emailParts.substring(0, 2).toUpperCase()
    }
    
    return '?'
  }, [fallbackText, name, email])

  const handleImageLoad = () => {
    setImageLoading(false)
    setImageError(false)
  }

  const handleImageError = () => {
    setImageLoading(false)
    setImageError(true)
  }

  const showImage = src && !imageError
  const showLoading = src && imageLoading && !imageError

  const avatarClasses = [
    'apollo-avatar',
    `apollo-avatar--${size}`,
    `apollo-avatar--${variant}`,
    showLoading && 'apollo-avatar--loading',
    className
  ].filter(Boolean).join(' ')

  return (
    <div 
      className={avatarClasses} 
      aria-label={alt || `Avatar for ${name || email || 'user'}`}
      role="img"
      {...props}
    >
      {showImage && (
        <img
          src={src}
          alt={alt || `Avatar for ${name || email || 'user'}`}
          onLoad={handleImageLoad}
          onError={handleImageError}
          className={`apollo-avatar__image ${imageLoading ? 'apollo-avatar__image--loading' : ''}`}
        />
      )}
      
      {showLoading && (
        <div className="apollo-avatar__loading-indicator" aria-hidden="true">
          <div className="apollo-avatar__spinner"></div>
        </div>
      )}
      
      {!showImage && !showLoading && (
        <span className="apollo-avatar__text" aria-hidden="true">
          {displayText}
        </span>
      )}
    </div>
  )
}

export default Avatar