/* Apollo Search Input Component */

/* === BASE SEARCH INPUT STYLES === */
.apollo-search-input {
  position: relative;
  display: inline-flex;
  align-items: center;
  width: 100%;
  border: 1px solid var(--apollo-border-medium);
  border-radius: var(--apollo-radius-sm);
  background-color: var(--apollo-bg-input);
  transition: all var(--apollo-transition-normal);
}

.apollo-search-input:hover:not(.apollo-search-input--disabled) {
  border-color: var(--apollo-border-hover);
  background-color: var(--apollo-bg-input-hover);
}

.apollo-search-input--focused {
  border-color: var(--apollo-primary);
  box-shadow: var(--apollo-shadow-focus);
  background-color: var(--apollo-bg-input-focus);
}

/* === INPUT ELEMENT === */
.apollo-search-input__input {
  flex: 1;
  border: none;
  outline: none;
  background: transparent;
  font-family: var(--apollo-font-ui);
  font-weight: var(--apollo-font-normal);
  color: var(--apollo-text-primary);
  line-height: var(--apollo-leading-normal);
}

.apollo-search-input__input::placeholder {
  color: var(--apollo-text-placeholder);
  opacity: 1;
}

.apollo-search-input__input:disabled {
  color: var(--apollo-text-disabled);
  cursor: not-allowed;
}

/* === ICONS === */
.apollo-search-input__icon {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  pointer-events: none;
  color: var(--apollo-text-secondary);
  transition: color var(--apollo-transition-normal);
}

.apollo-search-input__icon--search {
  margin-left: var(--apollo-space-sm);
}

.apollo-search-input__icon--clear {
  margin-right: var(--apollo-space-sm);
  cursor: pointer;
  pointer-events: auto;
  border: none;
  background: none;
  color: var(--apollo-text-secondary);
  border-radius: var(--apollo-radius-xs);
  padding: 2px;
  transition: all var(--apollo-transition-fast);
}

.apollo-search-input__icon--clear:hover {
  color: var(--apollo-text-primary);
  background-color: var(--apollo-bg-card-hover);
}

.apollo-search-input__icon--clear:focus-visible {
  outline: 2px solid var(--apollo-primary);
  outline-offset: 2px;
}

/* === SIZE VARIANTS === */

/* Small */
.apollo-search-input--sm {
  min-height: 28px;
}

.apollo-search-input--sm .apollo-search-input__input {
  padding: 4px 8px;
  font-size: var(--apollo-text-sm);
}

.apollo-search-input--sm .apollo-search-input__icon--search {
  margin-left: 8px;
}

.apollo-search-input--sm .apollo-search-input__icon--clear {
  margin-right: 8px;
}

/* Medium (default) */
.apollo-search-input--md {
  min-height: 32px;
}

.apollo-search-input--md .apollo-search-input__input {
  padding: 6px 12px;
  font-size: var(--apollo-text-base);
}

.apollo-search-input--md .apollo-search-input__icon--search {
  margin-left: 12px;
}

.apollo-search-input--md .apollo-search-input__icon--clear {
  margin-right: 12px;
}

/* Large */
.apollo-search-input--lg {
  min-height: 40px;
}

.apollo-search-input--lg .apollo-search-input__input {
  padding: 10px 16px;
  font-size: var(--apollo-text-lg);
}

.apollo-search-input--lg .apollo-search-input__icon--search {
  margin-left: 16px;
}

.apollo-search-input--lg .apollo-search-input__icon--clear {
  margin-right: 16px;
}

/* === STATE STYLES === */

/* Disabled */
.apollo-search-input--disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background-color: var(--apollo-bg-disabled);
  border-color: var(--apollo-border-disabled);
}

.apollo-search-input--disabled:hover {
  border-color: var(--apollo-border-disabled);
  background-color: var(--apollo-bg-disabled);
}

.apollo-search-input--disabled .apollo-search-input__icon {
  color: var(--apollo-text-disabled);
}

/* Full width */
.apollo-search-input--full-width {
  width: 100%;
}

/* Has value */
.apollo-search-input--has-value .apollo-search-input__icon--search {
  color: var(--apollo-primary);
}

/* === FOCUS STYLES === */
.apollo-search-input--focused .apollo-search-input__icon--search {
  color: var(--apollo-primary);
}

/* === RESPONSIVE ADJUSTMENTS === */
@media (max-width: 768px) {
  .apollo-search-input--lg {
    min-height: 36px;
  }
  
  .apollo-search-input--lg .apollo-search-input__input {
    padding: 8px 14px;
    font-size: var(--apollo-text-base);
  }
  
  .apollo-search-input--md {
    min-height: 30px;
  }
  
  .apollo-search-input--md .apollo-search-input__input {
    padding: 5px 10px;
    font-size: var(--apollo-text-sm);
  }
  
  .apollo-search-input--sm {
    min-height: 26px;
  }
  
  .apollo-search-input--sm .apollo-search-input__input {
    padding: 3px 6px;
    font-size: var(--apollo-text-xs);
  }
}

/* === HIGH CONTRAST MODE === */
@media (prefers-contrast: high) {
  .apollo-search-input {
    border-width: 2px;
  }
  
  .apollo-search-input--focused {
    border-width: 2px;
  }
  
  .apollo-search-input__icon--clear:hover {
    border: 1px solid var(--apollo-text-primary);
  }
}

/* === REDUCED MOTION === */
@media (prefers-reduced-motion: reduce) {
  .apollo-search-input,
  .apollo-search-input__icon,
  .apollo-search-input__icon--clear {
    transition: none;
  }
}

/* === PRINT STYLES === */
@media print {
  .apollo-search-input {
    border: 1px solid black !important;
    background: white !important;
    box-shadow: none !important;
  }
  
  .apollo-search-input__icon--clear {
    display: none !important;
  }
}