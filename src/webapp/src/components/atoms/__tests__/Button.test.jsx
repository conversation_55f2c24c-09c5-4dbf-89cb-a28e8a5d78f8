import { render, screen, fireEvent } from '@testing-library/react'
import { describe, it, expect, vi } from 'vitest'
import Button from '../Button'

describe('Button Component', () => {
  describe('Basic Rendering', () => {
    it('renders button with text content', () => {
      render(<Button>Click me</Button>)
      expect(screen.getByRole('button', { name: 'Click me' })).toBeInTheDocument()
    })

    it('applies default classes', () => {
      render(<Button>Default</Button>)
      const button = screen.getByRole('button')
      expect(button).toHaveClass('apollo-button', 'apollo-button--primary', 'apollo-button--md')
    })

    it('accepts custom className', () => {
      render(<Button className="custom-class">Custom</Button>)
      const button = screen.getByRole('button')
      expect(button).toHaveClass('custom-class')
    })

    it('sets data-testid when provided', () => {
      render(<Button data-testid="test-button">Test</Button>)
      expect(screen.getByTestId('test-button')).toBeInTheDocument()
    })
  })

  describe('Variants', () => {
    it('renders primary variant', () => {
      render(<Button variant="primary">Primary</Button>)
      expect(screen.getByRole('button')).toHaveClass('apollo-button--primary')
    })

    it('renders secondary variant', () => {
      render(<Button variant="secondary">Secondary</Button>)
      expect(screen.getByRole('button')).toHaveClass('apollo-button--secondary')
    })

    it('renders ghost variant', () => {
      render(<Button variant="ghost">Ghost</Button>)
      expect(screen.getByRole('button')).toHaveClass('apollo-button--ghost')
    })

    it('renders danger variant', () => {
      render(<Button variant="danger">Danger</Button>)
      expect(screen.getByRole('button')).toHaveClass('apollo-button--danger')
    })

    it('renders success variant', () => {
      render(<Button variant="success">Success</Button>)
      expect(screen.getByRole('button')).toHaveClass('apollo-button--success')
    })
  })

  describe('Sizes', () => {
    it('renders small size', () => {
      render(<Button size="sm">Small</Button>)
      expect(screen.getByRole('button')).toHaveClass('apollo-button--sm')
    })

    it('renders medium size (default)', () => {
      render(<Button size="md">Medium</Button>)
      expect(screen.getByRole('button')).toHaveClass('apollo-button--md')
    })

    it('renders large size', () => {
      render(<Button size="lg">Large</Button>)
      expect(screen.getByRole('button')).toHaveClass('apollo-button--lg')
    })
  })

  describe('States', () => {
    it('handles disabled state', () => {
      render(<Button disabled>Disabled</Button>)
      const button = screen.getByRole('button')
      expect(button).toBeDisabled()
      expect(button).toHaveClass('apollo-button--disabled')
      expect(button).toHaveAttribute('aria-disabled', 'true')
    })

    it('handles loading state', () => {
      render(<Button loading>Loading</Button>)
      const button = screen.getByRole('button')
      expect(button).toBeDisabled()
      expect(button).toHaveClass('apollo-button--loading')
      expect(button).toHaveAttribute('aria-disabled', 'true')
      
      // Check for loading icon
      const loadingIcon = button.querySelector('.apollo-button__loading-icon')
      expect(loadingIcon).toBeInTheDocument()
      
      // Check content is hidden
      const content = button.querySelector('.apollo-button__content')
      expect(content).toHaveClass('apollo-button__content--loading')
    })

    it('handles full width', () => {
      render(<Button fullWidth>Full Width</Button>)
      expect(screen.getByRole('button')).toHaveClass('apollo-button--full-width')
    })
  })

  describe('Click Handling', () => {
    it('calls onClick when clicked', () => {
      const handleClick = vi.fn()
      render(<Button onClick={handleClick}>Click me</Button>)
      
      fireEvent.click(screen.getByRole('button'))
      expect(handleClick).toHaveBeenCalledTimes(1)
    })

    it('does not call onClick when disabled', () => {
      const handleClick = vi.fn()
      render(<Button onClick={handleClick} disabled>Disabled</Button>)
      
      fireEvent.click(screen.getByRole('button'))
      expect(handleClick).not.toHaveBeenCalled()
    })

    it('does not call onClick when loading', () => {
      const handleClick = vi.fn()
      render(<Button onClick={handleClick} loading>Loading</Button>)
      
      fireEvent.click(screen.getByRole('button'))
      expect(handleClick).not.toHaveBeenCalled()
    })
  })

  describe('Button Types', () => {
    it('defaults to button type', () => {
      render(<Button>Default</Button>)
      expect(screen.getByRole('button')).toHaveAttribute('type', 'button')
    })

    it('accepts submit type', () => {
      render(<Button type="submit">Submit</Button>)
      expect(screen.getByRole('button')).toHaveAttribute('type', 'submit')
    })

    it('accepts reset type', () => {
      render(<Button type="reset">Reset</Button>)
      expect(screen.getByRole('button')).toHaveAttribute('type', 'reset')
    })
  })

  describe('Accessibility', () => {
    it('sets aria-label when provided', () => {
      render(<Button aria-label="Custom label">Button</Button>)
      expect(screen.getByRole('button')).toHaveAttribute('aria-label', 'Custom label')
    })

    it('uses text content as aria-label fallback', () => {
      render(<Button>Click me</Button>)
      expect(screen.getByRole('button')).toHaveAttribute('aria-label', 'Click me')
    })

    it('sets aria-disabled for disabled state', () => {
      render(<Button disabled>Disabled</Button>)
      expect(screen.getByRole('button')).toHaveAttribute('aria-disabled', 'true')
    })

    it('sets aria-disabled for loading state', () => {
      render(<Button loading>Loading</Button>)
      expect(screen.getByRole('button')).toHaveAttribute('aria-disabled', 'true')
    })

    it('hides loading icon from screen readers', () => {
      render(<Button loading>Loading</Button>)
      const loadingIcon = screen.getByRole('button').querySelector('.apollo-button__loading-icon')
      expect(loadingIcon).toHaveAttribute('aria-hidden', 'true')
    })
  })

  describe('Loading Icon Sizes', () => {
    it('uses correct loading icon size for small button', () => {
      render(<Button size="sm" loading>Loading</Button>)
      const loadingIcon = screen.getByRole('button').querySelector('.apollo-button__loading-icon svg')
      expect(loadingIcon).toHaveAttribute('width', '14')
      expect(loadingIcon).toHaveAttribute('height', '14')
    })

    it('uses correct loading icon size for medium button', () => {
      render(<Button size="md" loading>Loading</Button>)
      const loadingIcon = screen.getByRole('button').querySelector('.apollo-button__loading-icon svg')
      expect(loadingIcon).toHaveAttribute('width', '16')
      expect(loadingIcon).toHaveAttribute('height', '16')
    })

    it('uses correct loading icon size for large button', () => {
      render(<Button size="lg" loading>Loading</Button>)
      const loadingIcon = screen.getByRole('button').querySelector('.apollo-button__loading-icon svg')
      expect(loadingIcon).toHaveAttribute('width', '18')
      expect(loadingIcon).toHaveAttribute('height', '18')
    })
  })

  describe('Forward Ref', () => {
    it('forwards ref to button element', () => {
      const ref = { current: null }
      render(<Button ref={ref}>Ref test</Button>)
      expect(ref.current).toBeInstanceOf(HTMLButtonElement)
      expect(ref.current.textContent).toBe('Ref test')
    })
  })

  describe('Event Prevention', () => {
    it('prevents default when disabled and clicked', () => {
      const mockEvent = { preventDefault: vi.fn() }
      const handleClick = vi.fn()
      
      render(<Button onClick={handleClick} disabled>Disabled</Button>)
      const button = screen.getByRole('button')
      
      // Simulate click with preventDefault
      fireEvent.click(button, mockEvent)
      // Note: jsdom doesn't prevent default automatically, so we test our component logic
      expect(handleClick).not.toHaveBeenCalled()
    })

    it('prevents default when loading and clicked', () => {
      const handleClick = vi.fn()
      
      render(<Button onClick={handleClick} loading>Loading</Button>)
      const button = screen.getByRole('button')
      
      fireEvent.click(button)
      expect(handleClick).not.toHaveBeenCalled()
    })
  })

  describe('Component Display Name', () => {
    it('has correct display name', () => {
      expect(Button.displayName).toBe('Button')
    })
  })
})

// Integration tests for specific use cases
describe('Button Integration Tests', () => {
  it('if Button renders without props then basic button works', () => {
    render(<Button>Test</Button>)
    const button = screen.getByRole('button')
    expect(button).toBeInTheDocument()
    expect(button).toHaveTextContent('Test')
  })

  it('if Button variant is invalid then defaults to primary', () => {
    render(<Button variant="invalid">Invalid</Button>)
    const button = screen.getByRole('button')
    expect(button).toHaveClass('apollo-button--invalid')
  })

  it('if Button is both disabled and loading then disabled takes precedence', () => {
    render(<Button disabled loading>Both States</Button>)
    const button = screen.getByRole('button')
    expect(button).toBeDisabled()
    expect(button).toHaveClass('apollo-button--disabled', 'apollo-button--loading')
  })

  it('if Button onClick handler is called then it executes properly', () => {
    const clickHandler = vi.fn()
    
    render(<Button onClick={clickHandler}>Normal Button</Button>)
    
    fireEvent.click(screen.getByRole('button'))
    
    expect(clickHandler).toHaveBeenCalledTimes(1)
  })

  it('if Button receives complex children then renders properly', () => {
    render(
      <Button>
        <span>Complex</span>
        <strong>Children</strong>
      </Button>
    )
    
    const button = screen.getByRole('button')
    expect(button).toBeInTheDocument()
    expect(button.querySelector('span')).toHaveTextContent('Complex')
    expect(button.querySelector('strong')).toHaveTextContent('Children')
  })
})