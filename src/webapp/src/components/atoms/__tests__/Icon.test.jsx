import { render, screen, fireEvent } from '@testing-library/react'
import '@testing-library/jest-dom'
import { vi } from 'vitest'
import Icon from '../Icon'

describe('Icon Component', () => {
  describe('Basic Rendering', () => {
    test('renders book icon correctly', () => {
      render(<Icon name="book" data-testid="book-icon" />)
      const icon = screen.getByTestId('book-icon')
      expect(icon).toBeInTheDocument()
      expect(icon).toHaveAttribute('width', '20') // Default md size
      expect(icon).toHaveAttribute('height', '20')
    })

    test('renders home icon correctly', () => {
      render(<Icon name="home" data-testid="home-icon" />)
      const icon = screen.getByTestId('home-icon')
      expect(icon).toBeInTheDocument()
    })

    test('renders all available icons without error', () => {
      const iconNames = [
        'book', 'home', 'chat', 'users', 'people', 'chart', 'content', 
        'analytics', 'grid', 'apps', 'menu', 'arrow-down', 'plus', 
        'user', 'comment', 'search', 'filter', 'settings', 'close', 
        'vote-up', 'vote-down'
      ]

      iconNames.forEach(iconName => {
        render(<Icon name={iconName} data-testid={`${iconName}-icon`} />)
        expect(screen.getByTestId(`${iconName}-icon`)).toBeInTheDocument()
      })
    })

    test('returns null and warns for invalid icon name', () => {
      const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {})
      
      const { container } = render(<Icon name="invalid-icon" />)
      
      expect(container.firstChild).toBeNull()
      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('Icon "invalid-icon" not found')
      )
      
      consoleSpy.mockRestore()
    })
  })

  describe('Size Prop', () => {
    test('handles numeric size correctly', () => {
      render(<Icon name="book" size={32} data-testid="book-icon" />)
      const icon = screen.getByTestId('book-icon')
      expect(icon).toHaveAttribute('width', '32')
      expect(icon).toHaveAttribute('height', '32')
    })

    test('handles string size presets correctly', () => {
      const sizeTests = [
        { size: 'xs', expected: '12' },
        { size: 'sm', expected: '16' },
        { size: 'md', expected: '20' },
        { size: 'lg', expected: '24' },
        { size: 'xl', expected: '32' },
        { size: '2xl', expected: '48' }
      ]

      sizeTests.forEach(({ size, expected }) => {
        render(<Icon name="book" size={size} data-testid={`book-${size}`} />)
        const icon = screen.getByTestId(`book-${size}`)
        expect(icon).toHaveAttribute('width', expected)
        expect(icon).toHaveAttribute('height', expected)
      })
    })

    test('defaults to md size for invalid size string', () => {
      render(<Icon name="book" size="invalid" data-testid="book-icon" />)
      const icon = screen.getByTestId('book-icon')
      expect(icon).toHaveAttribute('width', '20') // md default
      expect(icon).toHaveAttribute('height', '20')
    })
  })

  describe('Color Prop', () => {
    test('applies custom color correctly', () => {
      render(<Icon name="book" color="#ff0000" data-testid="book-icon" />)
      const icon = screen.getByTestId('book-icon')
      expect(icon).toHaveAttribute('stroke', '#ff0000')
    })

    test('defaults to currentColor', () => {
      render(<Icon name="book" data-testid="book-icon" />)
      const icon = screen.getByTestId('book-icon')
      expect(icon).toHaveAttribute('stroke', 'currentColor')
    })
  })

  describe('className Prop', () => {
    test('applies custom className correctly', () => {
      render(<Icon name="book" className="custom-class" data-testid="book-icon" />)
      const icon = screen.getByTestId('book-icon')
      expect(icon).toHaveClass('apollo-icon')
      expect(icon).toHaveClass('custom-class')
    })

    test('applies interactive class when onClick is provided', () => {
      const handleClick = vi.fn()
      render(<Icon name="book" onClick={handleClick} data-testid="book-icon" />)
      const icon = screen.getByTestId('book-icon')
      expect(icon).toHaveClass('apollo-icon')
      expect(icon).toHaveClass('apollo-icon--interactive')
    })
  })

  describe('Interactive Behavior', () => {
    test('handles click events correctly', () => {
      const handleClick = vi.fn()
      render(<Icon name="book" onClick={handleClick} data-testid="book-icon" />)
      
      const icon = screen.getByTestId('book-icon')
      fireEvent.click(icon)
      
      expect(handleClick).toHaveBeenCalledTimes(1)
    })

    test('does not add interactive class when no onClick provided', () => {
      render(<Icon name="book" data-testid="book-icon" />)
      const icon = screen.getByTestId('book-icon')
      expect(icon).toHaveClass('apollo-icon')
      expect(icon).not.toHaveClass('apollo-icon--interactive')
    })
  })

  describe('Additional Props', () => {
    test('forwards additional props to the icon component', () => {
      render(
        <Icon 
          name="book" 
          data-testid="book-icon"
          aria-label="Book icon"
          title="Book"
        />
      )
      
      const icon = screen.getByTestId('book-icon')
      expect(icon).toHaveAttribute('aria-label', 'Book icon')
      expect(icon).toHaveAttribute('title', 'Book')
    })
  })

  describe('Icon Aliases', () => {
    test('people alias maps to UsersIcon', () => {
      render(<Icon name="people" data-testid="people-icon" />)
      const icon = screen.getByTestId('people-icon')
      expect(icon).toBeInTheDocument()
      // Both should render the same SVG paths
      const { container: peopleContainer } = render(<Icon name="people" />)
      const { container: usersContainer } = render(<Icon name="users" />)
      expect(peopleContainer.innerHTML).toBe(usersContainer.innerHTML)
    })

    test('content alias maps to ChartIcon', () => {
      render(<Icon name="content" data-testid="content-icon" />)
      const icon = screen.getByTestId('content-icon')
      expect(icon).toBeInTheDocument()
      // Both should render the same SVG paths
      const { container: contentContainer } = render(<Icon name="content" />)
      const { container: chartContainer } = render(<Icon name="chart" />)
      expect(contentContainer.innerHTML).toBe(chartContainer.innerHTML)
    })

    test('apps alias maps to GridIcon', () => {
      render(<Icon name="apps" data-testid="apps-icon" />)
      const icon = screen.getByTestId('apps-icon')
      expect(icon).toBeInTheDocument()
      // Both should render the same SVG paths
      const { container: appsContainer } = render(<Icon name="apps" />)
      const { container: gridContainer } = render(<Icon name="grid" />)
      expect(appsContainer.innerHTML).toBe(gridContainer.innerHTML)
    })
  })

  describe('Accessibility', () => {
    test('supports aria-label for screen readers', () => {
      render(<Icon name="book" aria-label="Book icon" data-testid="book-icon" />)
      const icon = screen.getByTestId('book-icon')
      expect(icon).toHaveAttribute('aria-label', 'Book icon')
    })

    test('supports title attribute for tooltips', () => {
      render(<Icon name="book" title="Book" data-testid="book-icon" />)
      const icon = screen.getByTestId('book-icon')
      expect(icon).toHaveAttribute('title', 'Book')
    })
  })
})