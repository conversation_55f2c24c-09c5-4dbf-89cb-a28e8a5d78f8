import React from 'react'
import { render, screen, fireEvent } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { vi } from 'vitest'
import ExampleCard from '../ExampleCard'

const mockExamples = [
  {
    input: "Test input 1",
    output: "Test output 1"
  },
  {
    input: "Test input 2", 
    output: "Test output 2"
  }
]

describe('ExampleCard', () => {
  it('renders examples correctly', () => {
    render(<ExampleCard examples={mockExamples} />)
    
    expect(screen.getByText('Input Output Examples')).toBeInTheDocument()
    expect(screen.getByText('Test input 1')).toBeInTheDocument()
    expect(screen.getByText('Test output 1')).toBeInTheDocument()
    expect(screen.getByText('Test input 2')).toBeInTheDocument()
    expect(screen.getByText('Test output 2')).toBeInTheDocument()
  })

  it('renders empty state when no examples provided', () => {
    render(<ExampleCard examples={[]} />)
    
    expect(screen.getByText('No examples available for this prompt.')).toBeInTheDocument()
  })

  it('renders empty state when examples is undefined', () => {
    render(<ExampleCard />)
    
    expect(screen.getByText('No examples available for this prompt.')).toBeInTheDocument()
  })

  it('shows action buttons when showActions is true', () => {
    const mockEdit = vi.fn()
    const mockDelete = vi.fn()
    
    render(
      <ExampleCard 
        examples={mockExamples} 
        showActions={true}
        onExampleEdit={mockEdit}
        onExampleDelete={mockDelete}
      />
    )
    
    const editButtons = screen.getAllByLabelText('Edit example')
    const deleteButtons = screen.getAllByLabelText('Delete example')
    
    expect(editButtons).toHaveLength(2)
    expect(deleteButtons).toHaveLength(2)
  })

  it('calls onExampleEdit when edit button is clicked', () => {
    const mockEdit = vi.fn()
    
    render(
      <ExampleCard 
        examples={mockExamples} 
        showActions={true}
        onExampleEdit={mockEdit}
      />
    )
    
    const editButton = screen.getAllByLabelText('Edit example')[0]
    fireEvent.click(editButton)
    
    expect(mockEdit).toHaveBeenCalledWith(0, mockExamples[0])
  })

  it('calls onExampleDelete when delete button is clicked', () => {
    const mockDelete = vi.fn()
    
    render(
      <ExampleCard 
        examples={mockExamples} 
        showActions={true}
        onExampleDelete={mockDelete}
      />
    )
    
    const deleteButton = screen.getAllByLabelText('Delete example')[0]
    fireEvent.click(deleteButton)
    
    expect(mockDelete).toHaveBeenCalledWith(0, mockExamples[0])
  })

  it('applies compact variant class when specified', () => {
    const { container } = render(
      <ExampleCard examples={mockExamples} variant="compact" />
    )
    
    const exampleItems = container.querySelectorAll('.example-item')
    expect(exampleItems[0]).toHaveClass('compact')
  })

  it('does not show action buttons when showActions is false', () => {
    render(<ExampleCard examples={mockExamples} showActions={false} />)
    
    expect(screen.queryByLabelText('Edit example')).not.toBeInTheDocument()
    expect(screen.queryByLabelText('Delete example')).not.toBeInTheDocument()
  })

  it('formats pre-formatted output correctly', () => {
    const exampleWithFormatted = [{
      input: "Test input",
      output: "Line 1\nLine 2\nLine 3"
    }]
    
    render(<ExampleCard examples={exampleWithFormatted} />)
    
    const preElement = screen.getByText((content, element) => {
      return element?.tagName.toLowerCase() === 'pre' && 
             content.includes('Line 1') && 
             content.includes('Line 2') && 
             content.includes('Line 3')
    })
    expect(preElement.tagName).toBe('PRE')
  })
})