import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { vi } from 'vitest'
import Select from '../Select'

describe('Select', () => {
  const mockOptions = [
    { value: 'option1', label: 'Option 1' },
    { value: 'option2', label: 'Option 2' },
    { value: 'option3', label: 'Option 3', disabled: true },
    { value: 'option4', label: 'Option 4', count: 5 }
  ]

  const stringOptions = ['Apple', 'Banana', 'Cherry']

  it('renders with default props', () => {
    render(<Select options={mockOptions} />)
    expect(screen.getByRole('combobox')).toBeInTheDocument()
    expect(screen.getByText('Select an option...')).toBeInTheDocument()
  })

  it('displays custom placeholder', () => {
    render(<Select options={mockOptions} placeholder="Choose option..." />)
    expect(screen.getByText('Choose option...')).toBeInTheDocument()
  })

  it('handles string options correctly', () => {
    render(<Select options={stringOptions} />)
    
    fireEvent.click(screen.getByRole('combobox'))
    
    expect(screen.getByText('Apple')).toBeInTheDocument()
    expect(screen.getByText('Banana')).toBeInTheDocument()
    expect(screen.getByText('Cherry')).toBeInTheDocument()
  })

  it('shows selected value', () => {
    render(<Select options={mockOptions} value="option1" />)
    expect(screen.getByText('Option 1')).toBeInTheDocument()
  })

  it('opens dropdown when clicked', async () => {
    const user = userEvent.setup()
    render(<Select options={mockOptions} />)
    
    await user.click(screen.getByRole('combobox'))
    
    expect(screen.getByRole('listbox')).toBeInTheDocument()
    expect(screen.getByText('Option 1')).toBeInTheDocument()
    expect(screen.getByText('Option 2')).toBeInTheDocument()
  })

  it('closes dropdown when option is selected', async () => {
    const mockOnChange = vi.fn()
    const user = userEvent.setup()
    render(<Select options={mockOptions} onChange={mockOnChange} />)
    
    await user.click(screen.getByRole('combobox'))
    await user.click(screen.getByText('Option 1'))
    
    expect(mockOnChange).toHaveBeenCalledWith('option1')
    expect(screen.queryByRole('listbox')).not.toBeInTheDocument()
  })

  it('handles disabled options', async () => {
    const mockOnChange = vi.fn()
    const user = userEvent.setup()
    render(<Select options={mockOptions} onChange={mockOnChange} />)
    
    await user.click(screen.getByRole('combobox'))
    await user.click(screen.getByText('Option 3'))
    
    expect(mockOnChange).not.toHaveBeenCalled()
  })

  it('supports multiple selection', async () => {
    const mockOnChange = vi.fn()
    const user = userEvent.setup()
    render(<Select options={mockOptions} multiple onChange={mockOnChange} />)
    
    await user.click(screen.getByRole('combobox'))
    await user.click(screen.getByText('Option 1'))
    
    expect(mockOnChange).toHaveBeenCalledWith(['option1'])
    
    await user.click(screen.getByText('Option 2'))
    expect(mockOnChange).toHaveBeenCalledWith(['option1', 'option2'])
  })

  it('displays count for multiple selections', async () => {
    const user = userEvent.setup()
    render(<Select options={mockOptions} value={['option1', 'option2']} multiple />)
    
    expect(screen.getByText('2 selected')).toBeInTheDocument()
  })

  it('handles keyboard navigation', async () => {
    const user = userEvent.setup()
    render(<Select options={mockOptions} />)
    
    const combobox = screen.getByRole('combobox')
    combobox.focus()
    
    // Open dropdown with Enter
    await user.keyboard('{Enter}')
    expect(screen.getByRole('listbox')).toBeInTheDocument()
    
    // Navigate with Arrow Down
    await user.keyboard('{ArrowDown}')
    
    // Select with Enter
    await user.keyboard('{Enter}')
    expect(screen.queryByRole('listbox')).not.toBeInTheDocument()
  })

  it('closes dropdown on Escape key', async () => {
    const user = userEvent.setup()
    render(<Select options={mockOptions} />)
    
    await user.click(screen.getByRole('combobox'))
    expect(screen.getByRole('listbox')).toBeInTheDocument()
    
    await user.keyboard('{Escape}')
    expect(screen.queryByRole('listbox')).not.toBeInTheDocument()
  })

  it('supports searchable functionality', async () => {
    const user = userEvent.setup()
    render(<Select options={mockOptions} searchable />)
    
    await user.click(screen.getByRole('combobox'))
    
    const searchInput = screen.getByPlaceholderText('Search options...')
    await user.type(searchInput, 'Option 1')
    
    expect(screen.getByText('Option 1')).toBeInTheDocument()
    expect(screen.queryByText('Option 2')).not.toBeInTheDocument()
  })

  it('shows no options message when search has no results', async () => {
    const user = userEvent.setup()
    render(<Select options={mockOptions} searchable />)
    
    await user.click(screen.getByRole('combobox'))
    
    const searchInput = screen.getByPlaceholderText('Search options...')
    await user.type(searchInput, 'Nonexistent')
    
    expect(screen.getByText('No matches found')).toBeInTheDocument()
  })

  it('displays clear button when clearable and has value', () => {
    render(<Select options={mockOptions} value="option1" clearable />)
    expect(screen.getByLabelText('Clear selection')).toBeInTheDocument()
  })

  it('clears selection when clear button is clicked', async () => {
    const mockOnChange = vi.fn()
    const user = userEvent.setup()
    render(<Select options={mockOptions} value="option1" clearable onChange={mockOnChange} />)
    
    await user.click(screen.getByLabelText('Clear selection'))
    
    expect(mockOnChange).toHaveBeenCalledWith('')
  })

  it('clears multiple selections when clear button is clicked', async () => {
    const mockOnChange = vi.fn()
    const user = userEvent.setup()
    render(<Select options={mockOptions} value={['option1', 'option2']} multiple clearable onChange={mockOnChange} />)
    
    await user.click(screen.getByLabelText('Clear selection'))
    
    expect(mockOnChange).toHaveBeenCalledWith([])
  })

  it('closes dropdown when clicking outside', async () => {
    const user = userEvent.setup()
    render(
      <div>
        <Select options={mockOptions} />
        <button>Outside</button>
      </div>
    )
    
    await user.click(screen.getByRole('combobox'))
    expect(screen.getByRole('listbox')).toBeInTheDocument()
    
    await user.click(screen.getByText('Outside'))
    expect(screen.queryByRole('listbox')).not.toBeInTheDocument()
  })

  it('applies disabled state correctly', () => {
    render(<Select options={mockOptions} disabled />)
    
    const combobox = screen.getByRole('combobox')
    expect(combobox).toHaveAttribute('tabIndex', '-1')
  })

  it('displays option counts when provided', async () => {
    const user = userEvent.setup()
    render(<Select options={mockOptions} />)
    
    await user.click(screen.getByRole('combobox'))
    
    expect(screen.getByText('5')).toBeInTheDocument() // count for option4
  })

  it('applies size classes correctly', () => {
    const { rerender } = render(<Select options={mockOptions} size="sm" data-testid="select" />)
    expect(screen.getByTestId('select')).toHaveClass('apollo-select--sm')
    
    rerender(<Select options={mockOptions} size="lg" data-testid="select" />)
    expect(screen.getByTestId('select')).toHaveClass('apollo-select--lg')
  })

  it('applies full width class when fullWidth is true', () => {
    render(<Select options={mockOptions} fullWidth data-testid="select" />)
    expect(screen.getByTestId('select')).toHaveClass('apollo-select--full-width')
  })

  it('has proper accessibility attributes', async () => {
    const user = userEvent.setup()
    render(<Select options={mockOptions} aria-label="Select option" />)
    
    const combobox = screen.getByRole('combobox')
    expect(combobox).toHaveAttribute('aria-label', 'Select option')
    expect(combobox).toHaveAttribute('aria-expanded', 'false')
    expect(combobox).toHaveAttribute('aria-haspopup', 'listbox')
    
    await user.click(combobox)
    expect(combobox).toHaveAttribute('aria-expanded', 'true')
    
    const listbox = screen.getByRole('listbox')
    expect(listbox).toHaveAttribute('aria-multiselectable', 'false')
  })

  it('has proper accessibility for multiple select', async () => {
    const user = userEvent.setup()
    render(<Select options={mockOptions} multiple />)
    
    await user.click(screen.getByRole('combobox'))
    
    const listbox = screen.getByRole('listbox')
    expect(listbox).toHaveAttribute('aria-multiselectable', 'true')
  })

  it('shows checkmarks for selected options in multiple mode', async () => {
    const user = userEvent.setup()
    render(<Select options={mockOptions} value={['option1']} multiple />)
    
    await user.click(screen.getByRole('combobox'))
    
    // Check that selected option has a checkmark
    const selectedOption = screen.getByText('Option 1').closest('.apollo-select__option')
    expect(selectedOption).toHaveClass('apollo-select__option--selected')
  })
})