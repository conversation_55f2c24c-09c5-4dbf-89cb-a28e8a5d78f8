import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { vi } from 'vitest'
import FilterSelect from '../FilterSelect'

describe('FilterSelect', () => {
  const mockOptions = [
    { value: 'category1', label: 'Category 1', count: 10 },
    { value: 'category2', label: 'Category 2', count: 5 },
    { value: 'category3', label: 'Category 3', count: 0 },
  ]

  const stringOptions = ['Apple', 'Banana', 'Cherry']

  it('renders with default props', () => {
    render(<FilterSelect options={mockOptions} />)
    expect(screen.getByRole('combobox')).toBeInTheDocument()
  })

  it('adds "All" option by default', async () => {
    const user = userEvent.setup()
    render(<FilterSelect options={mockOptions} />)
    
    await user.click(screen.getByRole('combobox'))
    
    expect(screen.getByText('All')).toBeInTheDocument()
  })

  it('uses custom "All" label', async () => {
    const user = userEvent.setup()
    render(<FilterSelect options={mockOptions} allLabel="All Categories" />)
    
    await user.click(screen.getByRole('combobox'))
    
    expect(screen.getByText('All Categories')).toBeInTheDocument()
  })

  it('displays option counts when showCounts is true', async () => {
    const user = userEvent.setup()
    render(<FilterSelect options={mockOptions} showCounts={true} />)
    
    await user.click(screen.getByRole('combobox'))
    
    expect(screen.getByText('10')).toBeInTheDocument()
    expect(screen.getByText('5')).toBeInTheDocument()
    expect(screen.getByText('0')).toBeInTheDocument()
  })

  it('shows total count for "All" option when showCounts is true', async () => {
    const user = userEvent.setup()
    render(<FilterSelect options={mockOptions} showCounts={true} />)
    
    await user.click(screen.getByRole('combobox'))
    
    // Total count should be 10 + 5 + 0 = 15
    const allOption = screen.getByText('All').closest('.apollo-select__option')
    expect(allOption).toHaveTextContent('15')
  })

  it('hides "All" option when showAll is false', async () => {
    const user = userEvent.setup()
    render(<FilterSelect options={mockOptions} showAll={false} />)
    
    await user.click(screen.getByRole('combobox'))
    
    expect(screen.queryByText('All')).not.toBeInTheDocument()
  })

  it('handles single selection with "All" option', async () => {
    const mockOnChange = vi.fn()
    const user = userEvent.setup()
    render(<FilterSelect options={mockOptions} onChange={mockOnChange} />)
    
    await user.click(screen.getByRole('combobox'))
    await user.click(screen.getByText('Category 1'))
    
    expect(mockOnChange).toHaveBeenCalledWith('category1')
  })

  it('handles "All" selection in single mode', async () => {
    const mockOnChange = vi.fn()
    const user = userEvent.setup()
    render(<FilterSelect options={mockOptions} onChange={mockOnChange} />)
    
    await user.click(screen.getByRole('combobox'))
    await user.click(screen.getByText('All'))
    
    expect(mockOnChange).toHaveBeenCalledWith('')
  })

  it('handles multiple selection', async () => {
    const mockOnChange = vi.fn()
    const user = userEvent.setup()
    render(<FilterSelect options={mockOptions} multiple onChange={mockOnChange} />)
    
    await user.click(screen.getByRole('combobox'))
    await user.click(screen.getByText('Category 1'))
    
    expect(mockOnChange).toHaveBeenCalledWith(['category1'])
    
    await user.click(screen.getByText('Category 2'))
    expect(mockOnChange).toHaveBeenCalledWith(['category1', 'category2'])
  })

  it('clears all selections when "All" is selected in multiple mode', async () => {
    const mockOnChange = vi.fn()
    const user = userEvent.setup()
    render(<FilterSelect options={mockOptions} value={['category1']} multiple onChange={mockOnChange} />)
    
    await user.click(screen.getByRole('combobox'))
    await user.click(screen.getByText('All'))
    
    expect(mockOnChange).toHaveBeenCalledWith([])
  })

  it('shows "All" as selected when no filters are active in multiple mode', () => {
    render(<FilterSelect options={mockOptions} value={[]} multiple />)
    expect(screen.getByText('All')).toBeInTheDocument()
  })

  it('shows "All" as selected when no filter is active in single mode', () => {
    render(<FilterSelect options={mockOptions} value="" />)
    expect(screen.getByText('All')).toBeInTheDocument()
  })

  it('displays count for selected items in multiple mode', () => {
    render(<FilterSelect options={mockOptions} value={['category1', 'category2']} multiple />)
    expect(screen.getByText('2 selected')).toBeInTheDocument()
  })

  it('displays single selection label in multiple mode', () => {
    render(<FilterSelect options={mockOptions} value={['category1']} multiple />)
    expect(screen.getByText('Category 1')).toBeInTheDocument()
  })

  it('handles string options correctly', async () => {
    const user = userEvent.setup()
    render(<FilterSelect options={stringOptions} />)
    
    await user.click(screen.getByRole('combobox'))
    
    expect(screen.getByText('Apple')).toBeInTheDocument()
    expect(screen.getByText('Banana')).toBeInTheDocument()
    expect(screen.getByText('Cherry')).toBeInTheDocument()
  })

  it('uses custom placeholder', () => {
    render(<FilterSelect options={mockOptions} placeholder="Choose categories..." />)
    expect(screen.getByText('Choose categories...')).toBeInTheDocument()
  })

  it('applies filter-specific CSS classes', () => {
    render(<FilterSelect options={mockOptions} data-testid="filter-select" />)
    expect(screen.getByTestId('filter-select')).toHaveClass('apollo-filter-select')
  })

  it('does not show clear button when showAll is true', () => {
    render(<FilterSelect options={mockOptions} value="category1" showAll={true} />)
    expect(screen.queryByLabelText('Clear selection')).not.toBeInTheDocument()
  })

  it('shows clear button when showAll is false and has value', () => {
    render(<FilterSelect options={mockOptions} value="category1" showAll={false} clearable />)
    expect(screen.getByLabelText('Clear selection')).toBeInTheDocument()
  })

  it('maintains existing functionality from Select component', async () => {
    const user = userEvent.setup()
    render(<FilterSelect options={mockOptions} disabled />)
    
    const combobox = screen.getByRole('combobox')
    expect(combobox).toHaveAttribute('tabIndex', '-1')
  })

  it('forwards other props to Select component', () => {
    render(<FilterSelect options={mockOptions} size="lg" data-testid="filter-select" />)
    expect(screen.getByTestId('filter-select')).toHaveClass('apollo-select--lg')
  })

  it('handles edge case with empty options array', async () => {
    const user = userEvent.setup()
    render(<FilterSelect options={[]} />)
    
    await user.click(screen.getByRole('combobox'))
    
    // Should still show "All" option
    expect(screen.getByText('All')).toBeInTheDocument()
    expect(screen.getByText('No options available')).toBeInTheDocument()
  })

  it('calculates total count correctly for "All" option', async () => {
    const optionsWithCounts = [
      { value: 'a', label: 'A', count: 3 },
      { value: 'b', label: 'B', count: 7 },
      { value: 'c', label: 'C' }, // No count property
    ]
    const user = userEvent.setup()
    render(<FilterSelect options={optionsWithCounts} showCounts={true} />)
    
    await user.click(screen.getByRole('combobox'))
    
    // Total should be 3 + 7 + 0 = 10
    const allOption = screen.getByText('All').closest('.apollo-select__option')
    expect(allOption).toHaveTextContent('10')
  })
})