import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { describe, it, expect, vi } from 'vitest'
import Avatar from '../Avatar'

// Mock the CSS import
vi.mock('../Avatar.css', () => ({}))

describe('Avatar Component', () => {
  describe('Basic Rendering', () => {
    it('renders with default props', () => {
      render(<Avatar />)
      const avatar = screen.getByRole('img')
      expect(avatar).toBeInTheDocument()
      expect(avatar).toHaveClass('apollo-avatar')
      expect(avatar).toHaveClass('apollo-avatar--md')
      expect(avatar).toHaveClass('apollo-avatar--circle')
    })

    it('renders with custom className', () => {
      render(<Avatar className="custom-class" />)
      const avatar = screen.getByRole('img')
      expect(avatar).toHaveClass('custom-class')
    })

    it('applies custom props to the container', () => {
      render(<Avatar data-testid="avatar-test" />)
      const avatar = screen.getByTestId('avatar-test')
      expect(avatar).toBeInTheDocument()
    })
  })

  describe('Size Variants', () => {
    it('renders with xs size', () => {
      render(<Avatar size="xs" />)
      const avatar = screen.getByRole('img')
      expect(avatar).toHaveClass('apollo-avatar--xs')
    })

    it('renders with sm size', () => {
      render(<Avatar size="sm" />)
      const avatar = screen.getByRole('img')
      expect(avatar).toHaveClass('apollo-avatar--sm')
    })

    it('renders with md size (default)', () => {
      render(<Avatar size="md" />)
      const avatar = screen.getByRole('img')
      expect(avatar).toHaveClass('apollo-avatar--md')
    })

    it('renders with lg size', () => {
      render(<Avatar size="lg" />)
      const avatar = screen.getByRole('img')
      expect(avatar).toHaveClass('apollo-avatar--lg')
    })

    it('renders with xl size', () => {
      render(<Avatar size="xl" />)
      const avatar = screen.getByRole('img')
      expect(avatar).toHaveClass('apollo-avatar--xl')
    })
  })

  describe('Shape Variants', () => {
    it('renders with circle variant (default)', () => {
      render(<Avatar variant="circle" />)
      const avatar = screen.getByRole('img')
      expect(avatar).toHaveClass('apollo-avatar--circle')
    })

    it('renders with square variant', () => {
      render(<Avatar variant="square" />)
      const avatar = screen.getByRole('img')
      expect(avatar).toHaveClass('apollo-avatar--square')
    })

    it('renders with rounded variant', () => {
      render(<Avatar variant="rounded" />)
      const avatar = screen.getByRole('img')
      expect(avatar).toHaveClass('apollo-avatar--rounded')
    })
  })

  describe('Image Handling', () => {
    it('renders image when src is provided', () => {
      render(<Avatar src="https://example.com/avatar.jpg" alt="User avatar" />)
      const image = screen.getByAltText('User avatar')
      expect(image).toBeInTheDocument()
      expect(image).toHaveAttribute('src', 'https://example.com/avatar.jpg')
    })

    it('shows loading state initially when image is provided', () => {
      render(<Avatar src="https://example.com/avatar.jpg" />)
      const container = document.querySelector('.apollo-avatar')
      expect(container).toHaveClass('apollo-avatar--loading')
      const loadingIndicator = container.querySelector('.apollo-avatar__loading-indicator')
      expect(loadingIndicator).toBeInTheDocument()
    })

    it('removes loading state when image loads successfully', async () => {
      render(<Avatar src="https://example.com/avatar.jpg" />)
      const container = document.querySelector('.apollo-avatar')
      const image = container.querySelector('img')
      
      fireEvent.load(image)
      
      await waitFor(() => {
        expect(container).not.toHaveClass('apollo-avatar--loading')
      })
    })

    it('shows fallback text when image fails to load', async () => {
      render(<Avatar src="https://example.com/broken.jpg" name="John Doe" />)
      const container = document.querySelector('.apollo-avatar')
      const image = container.querySelector('img')
      
      fireEvent.error(image)
      
      await waitFor(() => {
        expect(screen.getByText('JD')).toBeInTheDocument()
      })
    })

    it('uses custom alt text when provided', () => {
      render(<Avatar src="https://example.com/avatar.jpg" alt="Custom alt text" />)
      const container = document.querySelector('.apollo-avatar')
      expect(container).toHaveAttribute('aria-label', 'Custom alt text')
    })
  })

  describe('Fallback Text Generation', () => {
    it('uses fallbackText prop when provided', () => {
      render(<Avatar fallbackText="AB" />)
      expect(screen.getByText('AB')).toBeInTheDocument()
    })

    it('generates initials from name', () => {
      render(<Avatar name="John Doe" />)
      expect(screen.getByText('JD')).toBeInTheDocument()
    })

    it('generates initials from single name', () => {
      render(<Avatar name="John" />)
      expect(screen.getByText('JO')).toBeInTheDocument()
    })

    it('generates initials from multiple names (uses first and last)', () => {
      render(<Avatar name="John Michael Doe" />)
      expect(screen.getByText('JD')).toBeInTheDocument()
    })

    it('generates initials from email when no name provided', () => {
      render(<Avatar email="<EMAIL>" />)
      expect(screen.getByText('JO')).toBeInTheDocument()
    })

    it('prioritizes name over email for initials', () => {
      render(<Avatar name="Jane Smith" email="<EMAIL>" />)
      expect(screen.getByText('JS')).toBeInTheDocument()
    })

    it('shows question mark when no fallback data provided', () => {
      render(<Avatar />)
      expect(screen.getByText('?')).toBeInTheDocument()
    })

    it('handles empty strings gracefully', () => {
      render(<Avatar name="" email="" />)
      expect(screen.getByText('?')).toBeInTheDocument()
    })

    it('handles whitespace in names', () => {
      render(<Avatar name="  John   Doe  " />)
      expect(screen.getByText('JD')).toBeInTheDocument()
    })
  })

  describe('Accessibility', () => {
    it('has proper ARIA attributes', () => {
      render(<Avatar name="John Doe" />)
      const avatar = screen.getByRole('img')
      expect(avatar).toHaveAttribute('aria-label', 'Avatar for John Doe')
    })

    it('uses email for aria-label when no name provided', () => {
      render(<Avatar email="<EMAIL>" />)
      const avatar = screen.getByRole('img')
      expect(avatar).toHaveAttribute('aria-label', '<NAME_EMAIL>')
    })

    it('falls back to generic label when no name or email provided', () => {
      render(<Avatar />)
      const avatar = screen.getByRole('img')
      expect(avatar).toHaveAttribute('aria-label', 'Avatar for user')
    })

    it('uses custom alt text for aria-label when provided', () => {
      render(<Avatar alt="Profile picture" />)
      const avatar = screen.getByRole('img')
      expect(avatar).toHaveAttribute('aria-label', 'Profile picture')
    })

    it('sets aria-hidden on fallback text', () => {
      render(<Avatar name="John Doe" />)
      const fallbackText = screen.getByText('JD')
      expect(fallbackText).toHaveAttribute('aria-hidden', 'true')
    })

    it('sets aria-hidden on loading indicator', () => {
      render(<Avatar src="https://example.com/avatar.jpg" />)
      const loadingIndicator = screen.getByText('', { selector: '.apollo-avatar__loading-indicator' })
      expect(loadingIndicator).toHaveAttribute('aria-hidden', 'true')
    })
  })

  describe('Loading States', () => {
    it('shows spinner during image loading', () => {
      render(<Avatar src="https://example.com/avatar.jpg" />)
      const container = document.querySelector('.apollo-avatar')
      const spinner = container.querySelector('.apollo-avatar__spinner')
      expect(spinner).toBeInTheDocument()
    })

    it('hides spinner after image loads', async () => {
      render(<Avatar src="https://example.com/avatar.jpg" />)
      const container = document.querySelector('.apollo-avatar')
      const image = container.querySelector('img')
      
      fireEvent.load(image)
      
      await waitFor(() => {
        const spinner = container.querySelector('.apollo-avatar__spinner')
        expect(spinner).not.toBeInTheDocument()
      })
    })

    it('hides spinner after image error', async () => {
      render(<Avatar src="https://example.com/broken.jpg" name="John Doe" />)
      const container = document.querySelector('.apollo-avatar')
      const image = container.querySelector('img')
      
      fireEvent.error(image)
      
      await waitFor(() => {
        const spinner = container.querySelector('.apollo-avatar__spinner')
        expect(spinner).not.toBeInTheDocument()
      })
    })
  })

  describe('Edge Cases', () => {
    it('handles undefined name gracefully', () => {
      render(<Avatar name={undefined} />)
      expect(screen.getByText('?')).toBeInTheDocument()
    })

    it('handles null name gracefully', () => {
      render(<Avatar name={null} />)
      expect(screen.getByText('?')).toBeInTheDocument()
    })

    it('handles special characters in names', () => {
      render(<Avatar name="José María" />)
      expect(screen.getByText('JM')).toBeInTheDocument()
    })

    it('handles very long names', () => {
      render(<Avatar name="Verylongfirstname Verylonglastname" />)
      expect(screen.getByText('VV')).toBeInTheDocument()
    })

    it('handles names with numbers', () => {
      render(<Avatar name="John2 Doe3" />)
      expect(screen.getByText('JD')).toBeInTheDocument()
    })

    it('handles email with special characters', () => {
      render(<Avatar email="<EMAIL>" />)
      expect(screen.getByText('US')).toBeInTheDocument()
    })
  })

  describe('Integration', () => {
    it('works with complex props combination', () => {
      render(
        <Avatar 
          src="https://example.com/avatar.jpg"
          name="John Doe"
          email="<EMAIL>"
          size="lg"
          variant="rounded"
          alt="User profile picture"
          className="custom-avatar"
          data-testid="complex-avatar"
        />
      )
      
      const avatar = screen.getByTestId('complex-avatar')
      expect(avatar).toHaveClass('apollo-avatar--lg')
      expect(avatar).toHaveClass('apollo-avatar--rounded')
      expect(avatar).toHaveClass('custom-avatar')
      expect(avatar).toHaveAttribute('aria-label', 'User profile picture')
    })
  })
})