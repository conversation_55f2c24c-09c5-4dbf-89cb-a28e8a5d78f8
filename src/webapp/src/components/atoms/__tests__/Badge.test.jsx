import { render, screen } from '@testing-library/react'
import { describe, it, expect } from 'vitest'
import Badge from '../Badge.jsx'

describe('Badge Component', () => {
  describe('Basic Rendering', () => {
    it('renders badge with children text', () => {
      render(<Badge>Test Badge</Badge>)
      expect(screen.getByText('Test Badge')).toBeInTheDocument()
    })

    it('applies default variant and size classes', () => {
      const { container } = render(<Badge>Default Badge</Badge>)
      const badge = container.querySelector('.apollo-badge')
      expect(badge).toHaveClass('apollo-badge')
      expect(badge).toHaveClass('apollo-badge--default')
      expect(badge).toHaveClass('apollo-badge--md')
    })

    it('applies additional className prop', () => {
      const { container } = render(<Badge className="custom-class">Test</Badge>)
      const badge = container.querySelector('.apollo-badge')
      expect(badge).toHaveClass('custom-class')
    })
  })

  describe('Variant Props', () => {
    it('renders approved variant correctly', () => {
      const { container } = render(<Badge variant="approved">Approved</Badge>)
      const badge = container.querySelector('.apollo-badge')
      expect(badge).toHaveClass('apollo-badge--approved')
    })

    it('renders planned variant correctly', () => {
      const { container } = render(<Badge variant="planned">Planned</Badge>)
      const badge = container.querySelector('.apollo-badge')
      expect(badge).toHaveClass('apollo-badge--planned')
    })

    it('renders review variant correctly', () => {
      const { container } = render(<Badge variant="review">Review</Badge>)
      const badge = container.querySelector('.apollo-badge')
      expect(badge).toHaveClass('apollo-badge--review')
    })

    it('renders development variant correctly', () => {
      const { container } = render(<Badge variant="development">Development</Badge>)
      const badge = container.querySelector('.apollo-badge')
      expect(badge).toHaveClass('apollo-badge--development')
    })

    it('renders suggested variant correctly', () => {
      const { container } = render(<Badge variant="suggested">Suggested</Badge>)
      const badge = container.querySelector('.apollo-badge')
      expect(badge).toHaveClass('apollo-badge--suggested')
    })
  })

  describe('Size Props', () => {
    it('renders small size correctly', () => {
      const { container } = render(<Badge size="sm">Small</Badge>)
      const badge = container.querySelector('.apollo-badge')
      expect(badge).toHaveClass('apollo-badge--sm')
    })

    it('renders medium size correctly', () => {
      const { container } = render(<Badge size="md">Medium</Badge>)
      const badge = container.querySelector('.apollo-badge')
      expect(badge).toHaveClass('apollo-badge--md')
    })

    it('renders large size correctly', () => {
      const { container } = render(<Badge size="lg">Large</Badge>)
      const badge = container.querySelector('.apollo-badge')
      expect(badge).toHaveClass('apollo-badge--lg')
    })
  })

  describe('Combined Props', () => {
    it('combines variant and size classes correctly', () => {
      const { container } = render(
        <Badge variant="approved" size="lg" className="extra-class">
          Combined Badge
        </Badge>
      )
      const badge = container.querySelector('.apollo-badge')
      expect(badge).toHaveClass('apollo-badge')
      expect(badge).toHaveClass('apollo-badge--approved')
      expect(badge).toHaveClass('apollo-badge--lg')
      expect(badge).toHaveClass('extra-class')
    })
  })

  describe('Accessibility', () => {
    it('renders as a span element', () => {
      const { container } = render(<Badge>Accessible Badge</Badge>)
      const badge = container.querySelector('span.apollo-badge')
      expect(badge).toBeInTheDocument()
    })

    it('preserves text content for screen readers', () => {
      render(<Badge variant="approved">Status: Approved</Badge>)
      expect(screen.getByText('Status: Approved')).toBeInTheDocument()
    })
  })
})