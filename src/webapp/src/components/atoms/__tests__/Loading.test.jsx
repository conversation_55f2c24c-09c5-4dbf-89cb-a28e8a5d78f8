import { render, screen } from '@testing-library/react'
import { describe, it, expect, vi } from 'vitest'
import Loading from '../Loading'

// Mock the CSS import
vi.mock('../Loading.css', () => ({}))

describe('Loading Component', () => {
  describe('Basic Rendering', () => {
    it('renders with default props', () => {
      render(<Loading />)
      const loading = screen.getByRole('status')
      expect(loading).toBeInTheDocument()
      expect(loading).toHaveAttribute('aria-label', 'Loading')
      
      const container = loading.querySelector('.apollo-loading')
      expect(container).toHaveClass('apollo-loading--spinner')
      expect(container).toHaveClass('apollo-loading--md')
      expect(container).toHaveClass('apollo-loading--primary')
    })

    it('renders with custom className', () => {
      render(<Loading className="custom-loading" />)
      const container = screen.getByRole('status').querySelector('.apollo-loading')
      expect(container).toHaveClass('custom-loading')
    })

    it('applies custom props', () => {
      render(<Loading data-testid="loading-test" />)
      const loading = screen.getByTestId('loading-test')
      expect(loading).toBeInTheDocument()
    })
  })

  describe('Size Variants', () => {
    it('renders with xs size', () => {
      render(<Loading size="xs" />)
      const container = screen.getByRole('status').querySelector('.apollo-loading')
      expect(container).toHaveClass('apollo-loading--xs')
    })

    it('renders with sm size', () => {
      render(<Loading size="sm" />)
      const container = screen.getByRole('status').querySelector('.apollo-loading')
      expect(container).toHaveClass('apollo-loading--sm')
    })

    it('renders with md size (default)', () => {
      render(<Loading size="md" />)
      const container = screen.getByRole('status').querySelector('.apollo-loading')
      expect(container).toHaveClass('apollo-loading--md')
    })

    it('renders with lg size', () => {
      render(<Loading size="lg" />)
      const container = screen.getByRole('status').querySelector('.apollo-loading')
      expect(container).toHaveClass('apollo-loading--lg')
    })

    it('renders with xl size', () => {
      render(<Loading size="xl" />)
      const container = screen.getByRole('status').querySelector('.apollo-loading')
      expect(container).toHaveClass('apollo-loading--xl')
    })
  })

  describe('Color Variants', () => {
    it('renders with primary color (default)', () => {
      render(<Loading color="primary" />)
      const container = screen.getByRole('status').querySelector('.apollo-loading')
      expect(container).toHaveClass('apollo-loading--primary')
    })

    it('renders with secondary color', () => {
      render(<Loading color="secondary" />)
      const container = screen.getByRole('status').querySelector('.apollo-loading')
      expect(container).toHaveClass('apollo-loading--secondary')
    })

    it('renders with success color', () => {
      render(<Loading color="success" />)
      const container = screen.getByRole('status').querySelector('.apollo-loading')
      expect(container).toHaveClass('apollo-loading--success')
    })

    it('renders with warning color', () => {
      render(<Loading color="warning" />)
      const container = screen.getByRole('status').querySelector('.apollo-loading')
      expect(container).toHaveClass('apollo-loading--warning')
    })

    it('renders with error color', () => {
      render(<Loading color="error" />)
      const container = screen.getByRole('status').querySelector('.apollo-loading')
      expect(container).toHaveClass('apollo-loading--error')
    })
  })

  describe('Spinner Variant', () => {
    it('renders spinner variant (default)', () => {
      render(<Loading variant="spinner" />)
      const container = screen.getByRole('status').querySelector('.apollo-loading')
      expect(container).toHaveClass('apollo-loading--spinner')
      
      const spinner = container.querySelector('.apollo-loading__spinner')
      expect(spinner).toBeInTheDocument()
      
      const spinnerCircle = spinner.querySelector('.apollo-loading__spinner-circle')
      expect(spinnerCircle).toBeInTheDocument()
      expect(spinner).toHaveAttribute('aria-hidden', 'true')
    })
  })

  describe('Dots Variant', () => {
    it('renders dots variant', () => {
      render(<Loading variant="dots" />)
      const container = screen.getByRole('status').querySelector('.apollo-loading')
      expect(container).toHaveClass('apollo-loading--dots')
      
      const dots = container.querySelector('.apollo-loading__dots')
      expect(dots).toBeInTheDocument()
      expect(dots).toHaveAttribute('aria-hidden', 'true')
      
      const dotElements = dots.querySelectorAll('.apollo-loading__dot')
      expect(dotElements).toHaveLength(3)
    })
  })

  describe('Pulse Variant', () => {
    it('renders pulse variant', () => {
      render(<Loading variant="pulse" />)
      const container = screen.getByRole('status').querySelector('.apollo-loading')
      expect(container).toHaveClass('apollo-loading--pulse')
      
      const pulse = container.querySelector('.apollo-loading__pulse')
      expect(pulse).toBeInTheDocument()
      expect(pulse).toHaveAttribute('aria-hidden', 'true')
      
      const pulseCircle = pulse.querySelector('.apollo-loading__pulse-circle')
      expect(pulseCircle).toBeInTheDocument()
    })
  })

  describe('Skeleton Variant', () => {
    it('renders skeleton variant', () => {
      render(<Loading variant="skeleton" />)
      const container = screen.getByRole('status').querySelector('.apollo-loading')
      expect(container).toHaveClass('apollo-loading--skeleton')
      
      const skeleton = container.querySelector('.apollo-loading__skeleton')
      expect(skeleton).toBeInTheDocument()
      expect(skeleton).toHaveAttribute('aria-hidden', 'true')
      
      const skeletonLines = skeleton.querySelectorAll('.apollo-loading__skeleton-line')
      expect(skeletonLines).toHaveLength(3)
      
      // Check for different line variants
      expect(skeleton.querySelector('.apollo-loading__skeleton-line--short')).toBeInTheDocument()
      expect(skeleton.querySelector('.apollo-loading__skeleton-line--medium')).toBeInTheDocument()
    })
  })

  describe('Text Prop', () => {
    it('displays loading text when provided', () => {
      render(<Loading text="Loading data..." />)
      const text = screen.getByText('Loading data...')
      expect(text).toBeInTheDocument()
      expect(text).toHaveClass('apollo-loading__text')
      expect(text).toHaveAttribute('aria-live', 'polite')
    })

    it('updates aria-label when text is provided', () => {
      render(<Loading text="Loading data..." />)
      const loading = screen.getByRole('status')
      expect(loading).toHaveAttribute('aria-label', 'Loading data...')
    })

    it('renders without text when not provided', () => {
      render(<Loading />)
      const text = screen.queryByText('', { selector: '.apollo-loading__text' })
      expect(text).not.toBeInTheDocument()
    })
  })

  describe('Overlay Mode', () => {
    it('renders overlay mode correctly', () => {
      render(<Loading overlay={true} text="Processing..." />)
      
      const overlay = screen.getByRole('status')
      expect(overlay).toHaveClass('apollo-loading-overlay')
      expect(overlay).toHaveAttribute('aria-label', 'Processing...')
      expect(overlay).toHaveAttribute('aria-live', 'polite')
      
      const backdrop = overlay.querySelector('.apollo-loading-overlay__backdrop')
      expect(backdrop).toBeInTheDocument()
      
      const content = overlay.querySelector('.apollo-loading-overlay__content')
      expect(content).toBeInTheDocument()
      
      const loadingContainer = content.querySelector('.apollo-loading')
      expect(loadingContainer).toBeInTheDocument()
      expect(loadingContainer).toHaveClass('apollo-loading--overlay')
    })

    it('renders overlay without text', () => {
      render(<Loading overlay={true} />)
      
      const overlay = screen.getByRole('status')
      expect(overlay).toHaveAttribute('aria-label', 'Loading')
    })
  })

  describe('Accessibility', () => {
    it('has proper ARIA attributes', () => {
      render(<Loading />)
      const loading = screen.getByRole('status')
      expect(loading).toHaveAttribute('aria-live', 'polite')
    })

    it('sets aria-hidden on loading indicators', () => {
      render(<Loading variant="spinner" />)
      const spinner = screen.getByRole('status').querySelector('.apollo-loading__spinner')
      expect(spinner).toHaveAttribute('aria-hidden', 'true')
    })

    it('provides meaningful aria-label', () => {
      render(<Loading text="Saving changes..." />)
      const loading = screen.getByRole('status')
      expect(loading).toHaveAttribute('aria-label', 'Saving changes...')
    })

    it('has aria-live for dynamic text updates', () => {
      render(<Loading text="Loading..." />)
      const text = screen.getByText('Loading...')
      expect(text).toHaveAttribute('aria-live', 'polite')
    })
  })

  describe('Variant Combinations', () => {
    it('combines all props correctly', () => {
      render(
        <Loading 
          variant="dots"
          size="lg"
          color="success"
          text="Processing data..."
          className="custom-loading"
          data-testid="complex-loading"
        />
      )
      
      const statusWrapper = screen.getByTestId('complex-loading')
      expect(statusWrapper).toHaveAttribute('aria-label', 'Processing data...')
      
      const container = statusWrapper.querySelector('.apollo-loading')
      expect(container).toHaveClass('apollo-loading--dots')
      expect(container).toHaveClass('apollo-loading--lg')
      expect(container).toHaveClass('apollo-loading--success')
      expect(container).toHaveClass('custom-loading')
      
      expect(screen.getByText('Processing data...')).toBeInTheDocument()
    })

    it('works with overlay and custom text', () => {
      render(
        <Loading 
          variant="pulse"
          size="xl"
          color="warning"
          overlay={true}
          text="Please wait..."
          className="overlay-loading"
        />
      )
      
      const overlay = screen.getByRole('status')
      expect(overlay).toHaveClass('apollo-loading-overlay')
      expect(overlay).toHaveAttribute('aria-label', 'Please wait...')
      
      const container = overlay.querySelector('.apollo-loading')
      expect(container).toHaveClass('apollo-loading--pulse')
      expect(container).toHaveClass('apollo-loading--xl')
      expect(container).toHaveClass('apollo-loading--warning')
      expect(container).toHaveClass('apollo-loading--overlay')
      expect(container).toHaveClass('overlay-loading')
    })
  })

  describe('Edge Cases', () => {
    it('handles empty text string', () => {
      render(<Loading text="" />)
      const loading = screen.getByRole('status')
      expect(loading).toHaveAttribute('aria-label', 'Loading')
      
      const text = screen.queryByText('', { selector: '.apollo-loading__text' })
      expect(text).not.toBeInTheDocument()
    })

    it('handles null text', () => {
      render(<Loading text={null} />)
      const loading = screen.getByRole('status')
      expect(loading).toHaveAttribute('aria-label', 'Loading')
    })

    it('handles undefined text', () => {
      render(<Loading text={undefined} />)
      const loading = screen.getByRole('status')
      expect(loading).toHaveAttribute('aria-label', 'Loading')
    })

    it('handles invalid variant gracefully', () => {
      render(<Loading variant="invalid" />)
      const container = screen.getByRole('status').querySelector('.apollo-loading')
      // Should fallback to spinner
      expect(container).toHaveClass('apollo-loading--invalid')
      const spinner = container.querySelector('.apollo-loading__spinner')
      expect(spinner).toBeInTheDocument()
    })

    it('handles invalid size gracefully', () => {
      render(<Loading size="invalid" />)
      const container = screen.getByRole('status').querySelector('.apollo-loading')
      expect(container).toHaveClass('apollo-loading--invalid')
    })

    it('handles invalid color gracefully', () => {
      render(<Loading color="invalid" />)
      const container = screen.getByRole('status').querySelector('.apollo-loading')
      expect(container).toHaveClass('apollo-loading--invalid')
    })
  })

  describe('Performance', () => {
    it('renders quickly with minimal DOM elements', () => {
      render(<Loading />)
      const loading = screen.getByRole('status')
      
      // Should have a reasonable number of DOM elements
      const allElements = loading.querySelectorAll('*')
      expect(allElements.length).toBeLessThan(10)
    })

    it('skeleton variant renders efficiently', () => {
      render(<Loading variant="skeleton" />)
      const skeleton = screen.getByRole('status').querySelector('.apollo-loading__skeleton')
      const lines = skeleton.querySelectorAll('.apollo-loading__skeleton-line')
      
      // Should have exactly 3 skeleton lines
      expect(lines).toHaveLength(3)
    })
  })
})