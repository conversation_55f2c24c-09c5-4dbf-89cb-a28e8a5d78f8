import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { vi } from 'vitest'
import SearchInput from '../SearchInput'

describe('SearchInput', () => {
  beforeEach(() => {
    vi.clearAllTimers()
    vi.useFakeTimers()
  })

  afterEach(() => {
    vi.runOnlyPendingTimers()
    vi.useRealTimers()
  })

  it('renders with default props', () => {
    render(<SearchInput data-testid="search-input" />)
    expect(screen.getByRole('textbox')).toBeInTheDocument()
    expect(screen.getByLabelText('Search...')).toBeInTheDocument()
    expect(screen.getByTestId('search-input')).toHaveClass('apollo-search-input')
  })

  it('displays custom placeholder', () => {
    render(<SearchInput placeholder="Find prompts..." />)
    expect(screen.getByPlaceholderText('Find prompts...')).toBeInTheDocument()
  })

  it('shows initial value', () => {
    render(<SearchInput value="test query" />)
    expect(screen.getByDisplayValue('test query')).toBeInTheDocument()
  })

  it('calls onChange with debounced value', async () => {
    const mockOnChange = vi.fn()
    const user = userEvent.setup({ advanceTimers: vi.advanceTimersByTime })
    render(<SearchInput onChange={mockOnChange} debounceMs={100} />)
    
    const input = screen.getByRole('textbox')
    await user.type(input, 'test')
    
    // Should not call onChange immediately
    expect(mockOnChange).not.toHaveBeenCalled()
    
    // Fast-forward time to trigger debounce
    vi.advanceTimersByTime(100)
    
    await waitFor(() => {
      expect(mockOnChange).toHaveBeenCalledWith('test')
    })
  })

  it('shows clear button when input has value', async () => {
    const user = userEvent.setup({ advanceTimers: vi.advanceTimersByTime })
    render(<SearchInput />)
    
    const input = screen.getByRole('textbox')
    await user.type(input, 'test')
    
    expect(screen.getByLabelText('Clear search')).toBeInTheDocument()
  })

  it('clears input when clear button is clicked', async () => {
    const mockOnChange = vi.fn()
    const mockOnClear = vi.fn()
    const user = userEvent.setup({ advanceTimers: vi.advanceTimersByTime })
    
    render(<SearchInput onChange={mockOnChange} onClear={mockOnClear} />)
    
    const input = screen.getByRole('textbox')
    await user.type(input, 'test')
    
    const clearButton = screen.getByLabelText('Clear search')
    await user.click(clearButton)
    
    expect(mockOnClear).toHaveBeenCalled()
    expect(input.value).toBe('')
  })

  it('clears input on Escape key when input has value', async () => {
    const user = userEvent.setup({ advanceTimers: vi.advanceTimersByTime })
    render(<SearchInput />)
    
    const input = screen.getByRole('textbox')
    await user.type(input, 'test')
    
    await user.keyboard('{Escape}')
    
    expect(input.value).toBe('')
  })

  it('blurs input on Escape key when input is empty', async () => {
    const user = userEvent.setup({ advanceTimers: vi.advanceTimersByTime })
    render(<SearchInput />)
    
    const input = screen.getByRole('textbox')
    input.focus()
    
    await user.keyboard('{Escape}')
    
    expect(input).not.toHaveFocus()
  })

  it('applies disabled state correctly', () => {
    render(<SearchInput disabled />)
    
    const input = screen.getByRole('textbox')
    expect(input).toBeDisabled()
    expect(screen.queryByLabelText('Clear search')).not.toBeInTheDocument()
  })

  it('auto-focuses when autoFocus is true', () => {
    render(<SearchInput autoFocus />)
    
    const input = screen.getByRole('textbox')
    expect(input).toHaveFocus()
  })

  it('applies size classes correctly', () => {
    const { rerender } = render(<SearchInput size="sm" data-testid="search-input" />)
    expect(screen.getByTestId('search-input')).toHaveClass('apollo-search-input--sm')
    
    rerender(<SearchInput size="lg" data-testid="search-input" />)
    expect(screen.getByTestId('search-input')).toHaveClass('apollo-search-input--lg')
  })

  it('applies full width class when fullWidth is true', () => {
    render(<SearchInput fullWidth data-testid="search-input" />)
    expect(screen.getByTestId('search-input')).toHaveClass('apollo-search-input--full-width')
  })

  it('applies custom className', () => {
    render(<SearchInput className="custom-class" data-testid="search-input" />)
    expect(screen.getByTestId('search-input')).toHaveClass('custom-class')
  })

  it('forwards ref to input element', () => {
    const ref = vi.fn()
    render(<SearchInput ref={ref} />)
    
    expect(ref).toHaveBeenCalledWith(expect.any(HTMLInputElement))
  })

  it('handles focus and blur events', async () => {
    const mockOnFocus = vi.fn()
    const mockOnBlur = vi.fn()
    const user = userEvent.setup()
    
    render(<SearchInput onFocus={mockOnFocus} onBlur={mockOnBlur} data-testid="search-input" />)
    
    const input = screen.getByRole('textbox')
    
    await user.click(input)
    expect(mockOnFocus).toHaveBeenCalled()
    expect(screen.getByTestId('search-input')).toHaveClass('apollo-search-input--focused')
    
    await user.tab()
    expect(mockOnBlur).toHaveBeenCalled()
    expect(screen.getByTestId('search-input')).not.toHaveClass('apollo-search-input--focused')
  })

  it('synchronizes with external value changes', async () => {
    const { rerender } = render(<SearchInput value="initial" />)
    expect(screen.getByDisplayValue('initial')).toBeInTheDocument()
    
    rerender(<SearchInput value="updated" />)
    expect(screen.getByDisplayValue('updated')).toBeInTheDocument()
  })

  it('calls onClear instead of onChange when onClear is provided', async () => {
    const mockOnChange = vi.fn()
    const mockOnClear = vi.fn()
    const user = userEvent.setup({ advanceTimers: vi.advanceTimersByTime })
    
    render(<SearchInput onChange={mockOnChange} onClear={mockOnClear} />)
    
    const input = screen.getByRole('textbox')
    await user.type(input, 'test')
    
    const clearButton = screen.getByLabelText('Clear search')
    await user.click(clearButton)
    
    expect(mockOnClear).toHaveBeenCalled()
    // Should not call onChange for clear action when onClear is provided
    expect(mockOnChange).toHaveBeenCalledWith('test') // Only from typing
  })

  it('has proper accessibility attributes', () => {
    render(<SearchInput aria-label="Search prompts" />)
    
    const input = screen.getByRole('textbox')
    expect(input).toHaveAttribute('aria-label', 'Search prompts')
    
    const clearButton = screen.queryByLabelText('Clear search')
    if (clearButton) {
      expect(clearButton).toHaveAttribute('tabIndex', '-1')
    }
  })
})