import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import AIQueryInterface from '../AIQueryInterface'

const mockMessages = [
  {
    id: 1,
    type: 'user',
    message: 'Test user message'
  },
  {
    id: 2,
    type: 'ai',
    message: 'Test AI response with citation [1]',
    citations: [{ id: 1, title: 'Test Citation' }]
  }
]

describe('AIQueryInterface', () => {
  const defaultProps = {
    messages: [],
    query: '',
    onQueryChange: jest.fn(),
    onSubmit: jest.fn(),
    loading: false,
    isExpanded: false,
    onExpand: jest.fn(),
    onMinimize: jest.fn(),
    onCitationClick: jest.fn()
  }

  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('renders chat interface correctly', () => {
    render(<AIQueryInterface {...defaultProps} isExpanded={true} />)
    
    expect(screen.getByText('AI Assistant')).toBeInTheDocument()
    expect(screen.getByPlaceholderText('Ask about prompts...')).toBeInTheDocument()
    expect(screen.getByText('Send')).toBeInTheDocument()
  })

  it('displays messages correctly', () => {
    render(<AIQueryInterface {...defaultProps} messages={mockMessages} isExpanded={true} />)
    
    expect(screen.getByText('Test user message')).toBeInTheDocument()
    expect(screen.getByText(/Test AI response with citation/)).toBeInTheDocument()
  })

  it('calls onSubmit when form is submitted', async () => {
    const user = userEvent.setup()
    const mockSubmit = jest.fn()
    
    render(
      <AIQueryInterface 
        {...defaultProps} 
        query="test query" 
        onSubmit={mockSubmit}
        isExpanded={true}
      />
    )
    
    const sendButton = screen.getByText('Send')
    await user.click(sendButton)
    
    expect(mockSubmit).toHaveBeenCalledWith('test query')
  })

  it('calls onSubmit when Enter is pressed', async () => {
    const user = userEvent.setup()
    const mockSubmit = jest.fn()
    
    render(
      <AIQueryInterface 
        {...defaultProps} 
        query="test query" 
        onSubmit={mockSubmit}
        isExpanded={true}
      />
    )
    
    const input = screen.getByPlaceholderText('Ask about prompts...')
    await user.type(input, '{enter}')
    
    expect(mockSubmit).toHaveBeenCalledWith('test query')
  })

  it('does not submit when Shift+Enter is pressed', async () => {
    const user = userEvent.setup()
    const mockSubmit = jest.fn()
    
    render(
      <AIQueryInterface 
        {...defaultProps} 
        query="test query" 
        onSubmit={mockSubmit}
        isExpanded={true}
      />
    )
    
    const input = screen.getByPlaceholderText('Ask about prompts...')
    await user.type(input, '{shift}{enter}')
    
    expect(mockSubmit).not.toHaveBeenCalled()
  })

  it('calls onQueryChange when input changes', async () => {
    const user = userEvent.setup()
    const mockQueryChange = jest.fn()
    
    render(
      <AIQueryInterface 
        {...defaultProps} 
        onQueryChange={mockQueryChange}
        isExpanded={true}
      />
    )
    
    const input = screen.getByPlaceholderText('Ask about prompts...')
    await user.type(input, 'new query')
    
    expect(mockQueryChange).toHaveBeenCalledWith('new query')
  })

  it('calls onExpand when expand button is clicked', async () => {
    const user = userEvent.setup()
    const mockExpand = jest.fn()
    
    render(
      <AIQueryInterface 
        {...defaultProps} 
        onExpand={mockExpand}
        isExpanded={false}
      />
    )
    
    const expandButton = screen.getByLabelText('Expand chat')
    await user.click(expandButton)
    
    expect(mockExpand).toHaveBeenCalled()
  })

  it('calls onMinimize when minimize button is clicked', async () => {
    const user = userEvent.setup()
    const mockMinimize = jest.fn()
    
    render(
      <AIQueryInterface 
        {...defaultProps} 
        onMinimize={mockMinimize}
        isExpanded={true}
      />
    )
    
    const minimizeButton = screen.getByLabelText('Minimize chat')
    await user.click(minimizeButton)
    
    expect(mockMinimize).toHaveBeenCalled()
  })

  it('calls onCitationClick when citation is clicked', async () => {
    const user = userEvent.setup()
    const mockCitationClick = jest.fn()
    
    render(
      <AIQueryInterface 
        {...defaultProps} 
        messages={mockMessages}
        onCitationClick={mockCitationClick}
        isExpanded={true}
      />
    )
    
    const citationButton = screen.getByText('[1] Test Citation')
    await user.click(citationButton)
    
    expect(mockCitationClick).toHaveBeenCalledWith(1)
  })

  it('disables input and button when loading', () => {
    render(
      <AIQueryInterface 
        {...defaultProps} 
        loading={true}
        query="test"
        isExpanded={true}
      />
    )
    
    const input = screen.getByPlaceholderText('Ask about prompts...')
    const sendButton = screen.getByRole('button', { name: /send/i })
    
    expect(input).toBeDisabled()
    expect(sendButton).toBeDisabled()
  })

  it('disables input and button when disabled prop is true', () => {
    render(
      <AIQueryInterface 
        {...defaultProps} 
        disabled={true}
        query="test"
        isExpanded={true}
      />
    )
    
    const input = screen.getByPlaceholderText('Ask about prompts...')
    const sendButton = screen.getByRole('button', { name: /send/i })
    
    expect(input).toBeDisabled()
    expect(sendButton).toBeDisabled()
  })

  it('shows empty state when no messages', () => {
    render(<AIQueryInterface {...defaultProps} isExpanded={true} />)
    
    expect(screen.getByText('Ask me anything about the prompt library!')).toBeInTheDocument()
  })

  it('applies variant classes correctly', () => {
    const { container } = render(
      <AIQueryInterface {...defaultProps} variant="compact" />
    )
    
    expect(container.firstChild).toHaveClass('compact')
  })

  it('shows message count in header', () => {
    render(
      <AIQueryInterface 
        {...defaultProps} 
        messages={mockMessages}
        isExpanded={true}
      />
    )
    
    expect(screen.getByText('(2)')).toBeInTheDocument()
  })

  it('does not show toggle when showToggle is false', () => {
    render(
      <AIQueryInterface 
        {...defaultProps} 
        showToggle={false}
        isExpanded={true}
      />
    )
    
    expect(screen.queryByLabelText('Minimize chat')).not.toBeInTheDocument()
    expect(screen.queryByLabelText('Expand chat')).not.toBeInTheDocument()
  })

  it('renders streaming message with cursor', () => {
    const streamingMessage = {
      id: 3,
      type: 'ai',
      message: 'Streaming response...',
      isStreaming: true
    }
    
    render(
      <AIQueryInterface 
        {...defaultProps} 
        messages={[streamingMessage]}
        isExpanded={true}
      />
    )
    
    expect(screen.getByText('Streaming response...')).toBeInTheDocument()
    expect(document.querySelector('.streaming-cursor')).toBeInTheDocument()
  })
})