import { forwardRef } from 'react'
import { LoadingIcon } from './icons'
import './Button.css'

const Button = forwardRef(function Button({
  variant = 'primary',
  size = 'md',
  disabled = false,
  loading = false,
  fullWidth = false,
  onClick,
  children,
  type = 'button',
  className = '',
  'data-testid': dataTestId,
  'aria-label': ariaLabel,
  ...props
}, ref) {
  const baseClass = 'apollo-button'
  const variantClass = `apollo-button--${variant}`
  const sizeClass = `apollo-button--${size}`
  const stateClasses = [
    disabled && 'apollo-button--disabled',
    loading && 'apollo-button--loading',
    fullWidth && 'apollo-button--full-width'
  ].filter(Boolean)
  
  const allClasses = [baseClass, variantClass, sizeClass, ...stateClasses, className]
    .filter(Boolean)
    .join(' ')

  const handleClick = (e) => {
    if (disabled || loading) {
      e.preventDefault()
      return
    }
    onClick?.(e)
  }

  return (
    <button
      ref={ref}
      type={type}
      className={allClasses}
      disabled={disabled || loading}
      onClick={handleClick}
      data-testid={dataTestId}
      aria-label={ariaLabel || (typeof children === 'string' ? children : undefined)}
      aria-disabled={disabled || loading}
      {...props}
    >
      {loading && (
        <span className="apollo-button__loading-icon" aria-hidden="true">
          <LoadingIcon size={size === 'sm' ? 14 : size === 'lg' ? 18 : 16} />
        </span>
      )}
      <span className={`apollo-button__content ${loading ? 'apollo-button__content--loading' : ''}`}>
        {children}
      </span>
    </button>
  )
})

Button.displayName = 'Button'

export default Button