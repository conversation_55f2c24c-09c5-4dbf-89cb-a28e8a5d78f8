/* Avatar Component Styles */
.apollo-avatar {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  background: var(--apollo-bg-tertiary);
  border: 1px solid var(--apollo-border-light);
  font-family: var(--apollo-font-ui);
  font-weight: var(--apollo-font-medium);
  color: var(--apollo-text-primary);
  transition: all var(--apollo-transition-normal);
  flex-shrink: 0;
}

/* Size Variants */
.apollo-avatar--xs {
  width: 20px;
  height: 20px;
  font-size: var(--apollo-text-xs);
}

.apollo-avatar--sm {
  width: 32px;
  height: 32px;
  font-size: var(--apollo-text-sm);
}

.apollo-avatar--md {
  width: 40px;
  height: 40px;
  font-size: var(--apollo-text-base);
}

.apollo-avatar--lg {
  width: 56px;
  height: 56px;
  font-size: var(--apollo-text-lg);
}

.apollo-avatar--xl {
  width: 80px;
  height: 80px;
  font-size: var(--apollo-text-xl);
}

/* Shape Variants */
.apollo-avatar--circle {
  border-radius: 50%;
}

.apollo-avatar--square {
  border-radius: var(--apollo-radius-xs);
}

.apollo-avatar--rounded {
  border-radius: var(--apollo-radius-md);
}

/* Image Styles */
.apollo-avatar__image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  opacity: 1;
  transition: opacity var(--apollo-transition-normal);
}

.apollo-avatar__image--loading {
  opacity: 0;
}

/* Text Fallback */
.apollo-avatar__text {
  font-size: inherit;
  font-weight: inherit;
  line-height: 1;
  user-select: none;
  color: inherit;
}

/* Loading State */
.apollo-avatar--loading {
  background: var(--apollo-bg-secondary);
}

.apollo-avatar__loading-indicator {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--apollo-bg-secondary);
}

.apollo-avatar__spinner {
  width: 40%;
  height: 40%;
  border: 2px solid var(--apollo-border-light);
  border-top: 2px solid var(--apollo-primary);
  border-radius: 50%;
  animation: apollo-avatar-spin 1s linear infinite;
}

@keyframes apollo-avatar-spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Hover State for Interactive Avatars */
.apollo-avatar:hover {
  border-color: var(--apollo-border-medium);
  background: var(--apollo-bg-card-hover);
}

/* Focus State for Accessibility */
.apollo-avatar:focus-visible {
  outline: 2px solid var(--apollo-primary);
  outline-offset: 2px;
}

/* Status Indicator (can be added as a modifier) */
.apollo-avatar--online::after {
  content: '';
  position: absolute;
  bottom: 0;
  right: 0;
  width: 25%;
  height: 25%;
  background: var(--apollo-success);
  border: 2px solid var(--apollo-bg-primary);
  border-radius: 50%;
}

.apollo-avatar--offline::after {
  content: '';
  position: absolute;
  bottom: 0;
  right: 0;
  width: 25%;
  height: 25%;
  background: var(--apollo-neutral);
  border: 2px solid var(--apollo-bg-primary);
  border-radius: 50%;
}

/* Clickable State */
.apollo-avatar--clickable {
  cursor: pointer;
}

.apollo-avatar--clickable:hover {
  transform: scale(1.05);
  box-shadow: var(--apollo-shadow-sm);
}

.apollo-avatar--clickable:active {
  transform: scale(0.98);
}

/* Group Avatar Styles (for when multiple avatars are displayed together) */
.apollo-avatar-group {
  display: flex;
  align-items: center;
}

.apollo-avatar-group .apollo-avatar {
  margin-left: calc(var(--apollo-space-sm) * -1);
  border: 2px solid var(--apollo-bg-primary);
  position: relative;
  z-index: 1;
}

.apollo-avatar-group .apollo-avatar:first-child {
  margin-left: 0;
}

.apollo-avatar-group .apollo-avatar:hover {
  z-index: 2;
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
  .apollo-avatar {
    border-width: 2px;
    border-color: var(--apollo-text-primary);
  }
  
  .apollo-avatar__text {
    font-weight: var(--apollo-font-semibold);
  }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  .apollo-avatar,
  .apollo-avatar__image,
  .apollo-avatar--clickable {
    transition: none;
  }
  
  .apollo-avatar__spinner {
    animation: none;
  }
  
  .apollo-avatar--clickable:hover {
    transform: none;
  }
}

/* Dark Mode Adjustments (if needed) */
@media (prefers-color-scheme: dark) {
  .apollo-avatar {
    /* Additional dark mode specific styles if needed */
  }
}