// Global configuration for the Apollo Prompt Library
const config = {
  // Data Backend Configuration
  USE_DATABASE_INSTEAD_OF_JSON_FILE: true, // Set to false to use in-memory JSON store
  // Note: If switching backends or updating data, clear localStorage in browser dev tools
  
  // AI Configuration
  USE_OPENAI: true, // Set to false to use simulated responses
  OPENAI_MODEL: 'gpt-4o',
  OPENAI_TEMPERATURE: 0.7,
  OPENAI_MAX_TOKENS: 1000,
  
  // Debug mode
  DEBUG: import.meta.env.MODE === 'development',
  
  // API Configuration
  API_BASE_URL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000',
}

export default config