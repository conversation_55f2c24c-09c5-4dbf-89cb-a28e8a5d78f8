import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { screen, fireEvent, waitFor, cleanup, act } from '@testing-library/react'
import { renderWithProviders, mockPromptsArray, createMockEvent, waitForLoadingToFinish } from '../test/utils'
import App from '../App'

// Mock the API client
vi.mock('../api/client', () => {
  const mockPrompts = [
    {
      id: 1,
      title: 'Investment Thesis Development',
      description: 'Create comprehensive investment thesis for target company',
      category: 'Investment Analysis',
      tags: ['Thesis', 'Market Analysis', 'Competitive Analysis'],
      status: 'Approved',
      votes: 8,
      author: 'PE Team',
      timeAgo: '2 hours ago',
      statusCount: 45,
      difficulty: 'Advanced',
      comments: 15,
      userVote: null,
      content: 'Develop a comprehensive investment thesis...'
    },
    {
      id: 2,
      title: 'Management Team Assessment',
      description: 'Evaluate management team capabilities and track record',
      category: 'Due Diligence',
      tags: ['Management', 'Leadership', 'Assessment'],
      status: 'Under Review',
      votes: 5,
      author: 'DD Team',
      timeAgo: '4 hours ago',
      statusCount: 23,
      difficulty: 'Intermediate',
      comments: 8,
      userVote: null,
      content: 'Evaluate management team capabilities...'
    },
    {
      id: 3,
      title: 'Financial Model Review',
      description: 'Review and validate financial projections',
      category: 'Financial Analysis',
      tags: ['Financial Model', 'Projections', 'Validation'],
      status: 'In Development',
      votes: 12,
      author: 'Finance Team',
      timeAgo: '1 day ago',
      statusCount: 67,
      difficulty: 'Advanced',
      comments: 22,
      userVote: 'up',
      content: 'Review and validate financial projections...'
    }
  ]

  const mockCategories = [
    { name: 'Investment Analysis', count: 15 },
    { name: 'Due Diligence', count: 12 },
    { name: 'Financial Analysis', count: 8 }
  ]

  return {
    authAPI: {
      login: vi.fn().mockResolvedValue({
        user: { id: 1, username: 'testuser', email: '<EMAIL>', fullName: 'Test User', role: 'user' },
        sessionToken: 'mock-token'
      }),
      logout: vi.fn().mockResolvedValue({ success: true }),
      getCurrentUser: vi.fn().mockResolvedValue(null)
    },
    promptsAPI: {
      getAll: vi.fn().mockResolvedValue(mockPrompts),
      getById: vi.fn().mockResolvedValue(mockPrompts[0]),
      create: vi.fn().mockResolvedValue({}),
      update: vi.fn().mockResolvedValue({}),
      delete: vi.fn().mockResolvedValue({ success: true }),
      vote: vi.fn().mockImplementation((promptId, userId, direction) => {
        const prompt = mockPrompts.find(p => p.id === parseInt(promptId))
        if (prompt) {
          if (direction === 'toggle') {
            if (prompt.userVote) {
              prompt.votes -= 1
              prompt.userVote = null
            } else {
              prompt.votes += 1
              prompt.userVote = 'up'
            }
          }
          return Promise.resolve(prompt)
        }
        return Promise.reject(new Error('Prompt not found'))
      })
    },
    categoriesAPI: {
      getAll: vi.fn().mockResolvedValue(mockCategories)
    },
    migrateJsonToDatabase: vi.fn().mockResolvedValue()
  }
})

// Mock the mockData module
vi.mock('../mockData', () => ({
  mockPrompts: [
    {
      id: 1,
      title: 'Investment Thesis Development',
      description: 'Create comprehensive investment thesis for target company',
      category: 'Investment Analysis',
      tags: ['Thesis', 'Market Analysis', 'Competitive Analysis'],
      status: 'Approved',
      votes: 8,
      author: 'PE Team',
      timeAgo: '2 hours ago',
      statusCount: 45,
      difficulty: 'Advanced',
      comments: 15,
      userVote: null,
      content: 'Develop a comprehensive investment thesis...'
    },
    {
      id: 2,
      title: 'Management Team Assessment',
      description: 'Evaluate management team capabilities and track record',
      category: 'Due Diligence',
      tags: ['Management', 'Leadership', 'Assessment'],
      status: 'Under Review',
      votes: 5,
      author: 'DD Team',
      timeAgo: '4 hours ago',
      statusCount: 23,
      difficulty: 'Intermediate',
      comments: 8,
      userVote: null,
      content: 'Evaluate management team capabilities...'
    },
    {
      id: 3,
      title: 'Financial Model Review',
      description: 'Review and validate financial projections',
      category: 'Financial Analysis',
      tags: ['Financial Model', 'Projections', 'Validation'],
      status: 'In Development',
      votes: 12,
      author: 'Finance Team',
      timeAgo: '1 day ago',
      statusCount: 67,
      difficulty: 'Advanced',
      comments: 22,
      userVote: 'up',
      content: 'Review and validate financial projections...'
    }
  ],
  categories: [
    { name: 'Investment Analysis', count: 15 },
    { name: 'Due Diligence', count: 12 },
    { name: 'Financial Analysis', count: 8 }
  ],
  statuses: ['Under Review', 'Approved', 'In Development', 'Published']
}))

// Mock child components to isolate App component testing
vi.mock('../components/TwoTierSidebar', () => ({
  default: ({ statuses, categories, selectedStatus, setSelectedStatus, selectedCategory, setSelectedCategory }) => (
    <div data-testid="two-tier-sidebar">
      <button 
        data-testid="status-filter-approved" 
        onClick={() => setSelectedStatus('Approved')}
      >
        Approved
      </button>
      <button 
        data-testid="status-filter-under-review" 
        onClick={() => setSelectedStatus('Under Review')}
      >
        Under Review
      </button>
      <button 
        data-testid="status-clear" 
        onClick={() => setSelectedStatus('')}
      >
        Clear Status
      </button>
      <button 
        data-testid="category-filter-investment" 
        onClick={() => setSelectedCategory('Investment Analysis')}
      >
        Investment Analysis
      </button>
      <button 
        data-testid="category-filter-due-diligence" 
        onClick={() => setSelectedCategory('Due Diligence')}
      >
        Due Diligence
      </button>
      <button 
        data-testid="category-clear" 
        onClick={() => setSelectedCategory('')}
      >
        Clear Category
      </button>
      <div data-testid="selected-status">{selectedStatus}</div>
      <div data-testid="selected-category">{selectedCategory}</div>
    </div>
  )
}))

vi.mock('../components/MainContent', () => ({
  default: ({ prompts, sortBy, setSortBy, searchTerm, setSearchTerm, onVote, onCardClick, messages, setMessages, query, setQuery, loading, setLoading, isExpanded, setIsExpanded, onCitationClick, onNewPromptClick, onMobileMenuToggle }) => (
    <div data-testid="main-content">
      <input 
        data-testid="search-input"
        value={searchTerm}
        onChange={(e) => setSearchTerm(e.target.value)}
        placeholder="Search prompts..."
      />
      <select 
        data-testid="sort-select"
        value={sortBy}
        onChange={(e) => setSortBy(e.target.value)}
      >
        <option value="trending">Trending</option>
        <option value="newest">Newest</option>
        <option value="votes">Most Voted</option>
      </select>
      <div data-testid="prompts-count">{prompts.length}</div>
      {prompts.map(prompt => (
        <div key={prompt.id} data-testid={`prompt-${prompt.id}`}>
          <span data-testid={`prompt-title-${prompt.id}`}>{prompt.title}</span>
          <span data-testid={`prompt-votes-${prompt.id}`}>{prompt.votes}</span>
          <span data-testid={`prompt-uservote-${prompt.id}`}>{prompt.userVote || 'null'}</span>
          <button 
            data-testid={`vote-button-${prompt.id}`}
            onClick={() => onVote(prompt.id, 'toggle')}
          >
            Vote
          </button>
          <button 
            data-testid={`card-button-${prompt.id}`}
            onClick={() => onCardClick(prompt.id)}
          >
            View Details
          </button>
        </div>
      ))}
    </div>
  )
}))

vi.mock('../components/PromptDetail', () => ({
  default: ({ onBack }) => (
    <div data-testid="prompt-detail">
      <button data-testid="back-button" onClick={onBack}>
        Back to Main
      </button>
    </div>
  )
}))

// Helper function to wait for app to finish loading
const waitForAppToLoad = async () => {
  await waitFor(() => {
    expect(screen.queryByText('Loading Apollo Prompt Library...')).not.toBeInTheDocument()
  }, { timeout: 3000 })
}

describe('App Component', () => {
  let consoleSpy

  beforeEach(() => {
    consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {})
  })

  afterEach(() => {
    cleanup()
    consoleSpy?.mockRestore?.() || consoleSpy?.restore?.()
    vi.clearAllMocks()
  })

  describe('Initial State and Rendering', () => {
    it('renders the main application with correct initial state', async () => {
      renderWithProviders(<App />)
      
      // Wait for app to finish loading
      await waitForAppToLoad()
      
      expect(screen.getByTestId('two-tier-sidebar')).toBeInTheDocument()
      expect(screen.getByTestId('main-content')).toBeInTheDocument()
      // The preview text only appears in detail view, not in main view
      
      // Check initial filter states
      expect(screen.getByTestId('selected-status')).toHaveTextContent('')
      expect(screen.getByTestId('selected-category')).toHaveTextContent('')
      
      // Check initial search and sort states
      expect(screen.getByTestId('search-input')).toHaveValue('')
      expect(screen.getByTestId('sort-select')).toHaveValue('trending')
      
      // Check all prompts are initially displayed
      expect(screen.getByTestId('prompts-count')).toHaveTextContent('3')
    })

    it('renders all prompts without any filters applied', async () => {
      renderWithProviders(<App />)
      
      // Wait for app to finish loading
      await waitForAppToLoad()
      
      expect(screen.getByTestId('prompt-1')).toBeInTheDocument()
      expect(screen.getByTestId('prompt-2')).toBeInTheDocument()
      expect(screen.getByTestId('prompt-3')).toBeInTheDocument()
      
      expect(screen.getByTestId('prompt-title-1')).toHaveTextContent('Investment Thesis Development')
      expect(screen.getByTestId('prompt-title-2')).toHaveTextContent('Management Team Assessment')
      expect(screen.getByTestId('prompt-title-3')).toHaveTextContent('Financial Model Review')
    })
  })

  describe('State Management', () => {
    it('updates filter state when status filter is applied', async () => {
      renderWithProviders(<App />)
      
      // Wait for app to finish loading
      await waitForAppToLoad()
      
      const approvedButton = screen.getByTestId('status-filter-approved')
      fireEvent.click(approvedButton)
      
      await waitFor(() => {
        expect(screen.getByTestId('selected-status')).toHaveTextContent('Approved')
      })
      
      // Should only show approved prompts (prompt-1)
      expect(screen.getByTestId('prompts-count')).toHaveTextContent('1')
      expect(screen.getByTestId('prompt-1')).toBeInTheDocument()
      expect(screen.queryByTestId('prompt-2')).not.toBeInTheDocument()
      expect(screen.queryByTestId('prompt-3')).not.toBeInTheDocument()
    })

    it('updates filter state when category filter is applied', async () => {
      renderWithProviders(<App />)
      
      // Wait for app to finish loading
      await waitForAppToLoad()
      
      const investmentButton = screen.getByTestId('category-filter-investment')
      fireEvent.click(investmentButton)
      
      await waitFor(() => {
        expect(screen.getByTestId('selected-category')).toHaveTextContent('Investment Analysis')
      })
      
      // Should only show Investment Analysis prompts (prompt-1)
      expect(screen.getByTestId('prompts-count')).toHaveTextContent('1')
      expect(screen.getByTestId('prompt-1')).toBeInTheDocument()
      expect(screen.queryByTestId('prompt-2')).not.toBeInTheDocument()
      expect(screen.queryByTestId('prompt-3')).not.toBeInTheDocument()
    })

    it('updates search state when search term is entered', async () => {
      renderWithProviders(<App />)
      
      // Wait for app to finish loading
      await waitForAppToLoad()
      
      const searchInput = screen.getByTestId('search-input')
      fireEvent.change(searchInput, { target: { value: 'management' } })
      
      await waitFor(() => {
        expect(searchInput).toHaveValue('management')
      })
      
      // Should only show prompts matching 'management' (prompt-2)
      expect(screen.getByTestId('prompts-count')).toHaveTextContent('1')
      expect(screen.queryByTestId('prompt-1')).not.toBeInTheDocument()
      expect(screen.getByTestId('prompt-2')).toBeInTheDocument()
      expect(screen.queryByTestId('prompt-3')).not.toBeInTheDocument()
    })

    it('updates sort state when sort option is changed', async () => {
      renderWithProviders(<App />)
      
      // Wait for app to finish loading
      await waitForAppToLoad()
      
      const sortSelect = screen.getByTestId('sort-select')
      fireEvent.change(sortSelect, { target: { value: 'newest' } })
      
      await waitFor(() => {
        expect(sortSelect).toHaveValue('newest')
      })
    })

    it('clears filters when clear buttons are clicked', async () => {
      renderWithProviders(<App />)
      
      // Wait for app to finish loading
      await waitForAppToLoad()
      
      // Apply filters first
      fireEvent.click(screen.getByTestId('status-filter-approved'))
      fireEvent.click(screen.getByTestId('category-filter-investment'))
      
      await waitFor(() => {
        expect(screen.getByTestId('selected-status')).toHaveTextContent('Approved')
        expect(screen.getByTestId('selected-category')).toHaveTextContent('Investment Analysis')
      })
      
      // Clear status filter
      fireEvent.click(screen.getByTestId('status-clear'))
      await waitFor(() => {
        expect(screen.getByTestId('selected-status')).toHaveTextContent('')
      })
      
      // Clear category filter
      fireEvent.click(screen.getByTestId('category-clear'))
      await waitFor(() => {
        expect(screen.getByTestId('selected-category')).toHaveTextContent('')
      })
      
      // Should show all prompts again
      expect(screen.getByTestId('prompts-count')).toHaveTextContent('3')
    })
  })

  describe('Voting Functionality', () => {
    it('handles vote toggle - adds vote when prompt has no user vote', async () => {
      renderWithProviders(<App />)
      
      // Wait for app to finish loading
      await waitForAppToLoad()
      
      // Initial state: prompt-1 has 8 votes and no user vote
      expect(screen.getByTestId('prompt-votes-1')).toHaveTextContent('8')
      expect(screen.getByTestId('prompt-uservote-1')).toHaveTextContent('null')
      
      // Click vote button
      fireEvent.click(screen.getByTestId('vote-button-1'))
      
      await waitFor(() => {
        expect(screen.getByTestId('prompt-votes-1')).toHaveTextContent('9')
        expect(screen.getByTestId('prompt-uservote-1')).toHaveTextContent('up')
      })
    })

    it('handles vote toggle - removes vote when prompt has user vote', async () => {
      renderWithProviders(<App />)
      
      // Wait for app to finish loading
      await waitForAppToLoad()
      
      // Initial state: prompt-3 has 12 votes and user vote 'up'
      expect(screen.getByTestId('prompt-votes-3')).toHaveTextContent('12')
      expect(screen.getByTestId('prompt-uservote-3')).toHaveTextContent('up')
      
      // Click vote button to remove vote
      fireEvent.click(screen.getByTestId('vote-button-3'))
      
      await waitFor(() => {
        expect(screen.getByTestId('prompt-votes-3')).toHaveTextContent('11')
        expect(screen.getByTestId('prompt-uservote-3')).toHaveTextContent('null')
      })
    })

    it('handles multiple vote toggles correctly', async () => {
      renderWithProviders(<App />)
      
      // Wait for app to finish loading
      await waitForAppToLoad()
      
      const voteButton = screen.getByTestId('vote-button-2')
      const votesDisplay = screen.getByTestId('prompt-votes-2')
      const userVoteDisplay = screen.getByTestId('prompt-uservote-2')
      
      // Initial state: 5 votes, no user vote
      expect(votesDisplay).toHaveTextContent('5')
      expect(userVoteDisplay).toHaveTextContent('null')
      
      // First click: add vote
      fireEvent.click(voteButton)
      await waitFor(() => {
        expect(votesDisplay).toHaveTextContent('6')
        expect(userVoteDisplay).toHaveTextContent('up')
      })
      
      // Second click: remove vote
      fireEvent.click(voteButton)
      await waitFor(() => {
        expect(votesDisplay).toHaveTextContent('5')
        expect(userVoteDisplay).toHaveTextContent('null')
      })
      
      // Third click: add vote again
      fireEvent.click(voteButton)
      await waitFor(() => {
        expect(votesDisplay).toHaveTextContent('6')
        expect(userVoteDisplay).toHaveTextContent('up')
      })
    })

    it('does not affect other prompts when voting on one prompt', async () => {
      renderWithProviders(<App />)
      
      // Wait for app to finish loading
      await waitForAppToLoad()
      
      // Get initial vote counts
      const votes1Initial = screen.getByTestId('prompt-votes-1').textContent
      const votes3Initial = screen.getByTestId('prompt-votes-3').textContent
      
      // Vote on prompt-2
      fireEvent.click(screen.getByTestId('vote-button-2'))
      
      await waitFor(() => {
        expect(screen.getByTestId('prompt-votes-2')).toHaveTextContent('6')
      })
      
      // Check other prompts are unchanged
      expect(screen.getByTestId('prompt-votes-1')).toHaveTextContent(votes1Initial)
      expect(screen.getByTestId('prompt-votes-3')).toHaveTextContent(votes3Initial)
    })
  })

  describe('Filtering Logic', () => {
    it('filters by status correctly', async () => {
      renderWithProviders(<App />)
      
      // Wait for app to finish loading
      await waitForAppToLoad()
      
      // Filter by "Under Review"
      fireEvent.click(screen.getByTestId('status-filter-under-review'))
      
      await waitFor(() => {
        expect(screen.getByTestId('prompts-count')).toHaveTextContent('1')
        expect(screen.getByTestId('prompt-2')).toBeInTheDocument()
        expect(screen.queryByTestId('prompt-1')).not.toBeInTheDocument()
        expect(screen.queryByTestId('prompt-3')).not.toBeInTheDocument()
      })
    })

    it('filters by category correctly', async () => {
      renderWithProviders(<App />)
      
      // Wait for app to finish loading
      await waitForAppToLoad()
      
      // Filter by "Due Diligence"
      fireEvent.click(screen.getByTestId('category-filter-due-diligence'))
      
      await waitFor(() => {
        expect(screen.getByTestId('prompts-count')).toHaveTextContent('1')
        expect(screen.getByTestId('prompt-2')).toBeInTheDocument()
        expect(screen.queryByTestId('prompt-1')).not.toBeInTheDocument()
        expect(screen.queryByTestId('prompt-3')).not.toBeInTheDocument()
      })
    })

    it('filters by search term in title correctly', async () => {
      renderWithProviders(<App />)
      
      // Wait for app to finish loading
      await waitForAppToLoad()
      
      const searchInput = screen.getByTestId('search-input')
      fireEvent.change(searchInput, { target: { value: 'financial' } })
      
      await waitFor(() => {
        expect(screen.getByTestId('prompts-count')).toHaveTextContent('1')
        expect(screen.getByTestId('prompt-3')).toBeInTheDocument()
        expect(screen.queryByTestId('prompt-1')).not.toBeInTheDocument()
        expect(screen.queryByTestId('prompt-2')).not.toBeInTheDocument()
      })
    })

    it('filters by search term in description correctly', async () => {
      renderWithProviders(<App />)
      
      // Wait for app to finish loading
      await waitForAppToLoad()
      
      const searchInput = screen.getByTestId('search-input')
      fireEvent.change(searchInput, { target: { value: 'comprehensive' } })
      
      await waitFor(() => {
        expect(screen.getByTestId('prompts-count')).toHaveTextContent('1')
        expect(screen.getByTestId('prompt-1')).toBeInTheDocument()
        expect(screen.queryByTestId('prompt-2')).not.toBeInTheDocument()
        expect(screen.queryByTestId('prompt-3')).not.toBeInTheDocument()
      })
    })

    it('filters by search term in tags correctly', async () => {
      renderWithProviders(<App />)
      
      // Wait for app to finish loading
      await waitForAppToLoad()
      
      const searchInput = screen.getByTestId('search-input')
      fireEvent.change(searchInput, { target: { value: 'leadership' } })
      
      await waitFor(() => {
        expect(screen.getByTestId('prompts-count')).toHaveTextContent('1')
        expect(screen.getByTestId('prompt-2')).toBeInTheDocument()
        expect(screen.queryByTestId('prompt-1')).not.toBeInTheDocument()
        expect(screen.queryByTestId('prompt-3')).not.toBeInTheDocument()
      })
    })

    it('combines multiple filters correctly', async () => {
      renderWithProviders(<App />)
      
      // Wait for app to finish loading
      await waitForAppToLoad()
      
      // Apply status filter first (should show prompt-1 and prompt-3)
      fireEvent.click(screen.getByTestId('status-filter-approved'))
      
      await waitFor(() => {
        expect(screen.getByTestId('prompts-count')).toHaveTextContent('1')
      })
      
      // Then apply search term that matches prompt-1
      const searchInput = screen.getByTestId('search-input')
      fireEvent.change(searchInput, { target: { value: 'investment' } })
      
      await waitFor(() => {
        expect(screen.getByTestId('prompts-count')).toHaveTextContent('1')
        expect(screen.getByTestId('prompt-1')).toBeInTheDocument()
        expect(screen.queryByTestId('prompt-2')).not.toBeInTheDocument()
        expect(screen.queryByTestId('prompt-3')).not.toBeInTheDocument()
      })
    })

    it('shows no results when filters match nothing', async () => {
      renderWithProviders(<App />)
      
      // Wait for app to finish loading
      await waitForAppToLoad()
      
      // Apply filters that match no prompts
      fireEvent.click(screen.getByTestId('status-filter-approved'))
      const searchInput = screen.getByTestId('search-input')
      fireEvent.change(searchInput, { target: { value: 'nonexistent' } })
      
      await waitFor(() => {
        expect(screen.getByTestId('prompts-count')).toHaveTextContent('0')
        expect(screen.queryByTestId('prompt-1')).not.toBeInTheDocument()
        expect(screen.queryByTestId('prompt-2')).not.toBeInTheDocument()
        expect(screen.queryByTestId('prompt-3')).not.toBeInTheDocument()
      })
    })

    it('handles case-insensitive search correctly', async () => {
      renderWithProviders(<App />)
      
      // Wait for app to finish loading
      await waitForAppToLoad()
      
      const searchInput = screen.getByTestId('search-input')
      fireEvent.change(searchInput, { target: { value: 'MANAGEMENT' } })
      
      await waitFor(() => {
        expect(screen.getByTestId('prompts-count')).toHaveTextContent('1')
        expect(screen.getByTestId('prompt-2')).toBeInTheDocument()
      })
    })
  })

  describe('Keyboard Event Handling', () => {
    it('closes detail preview when Escape key is pressed', async () => {
      renderWithProviders(<App />)
      
      // Wait for app to finish loading
      await waitForAppToLoad()
      
      // Click on a card to open detail view
      fireEvent.click(screen.getByTestId('card-button-1'))
      
      await waitFor(() => {
        expect(screen.getByTestId('prompt-detail')).toBeInTheDocument()
        expect(screen.queryByTestId('main-content')).not.toBeInTheDocument()
      })
      
      // Press 'Escape' key to close detail view
      fireEvent.keyDown(window, { key: 'Escape' })
      
      await waitFor(() => {
        expect(screen.getByTestId('main-content')).toBeInTheDocument()
        expect(screen.queryByTestId('prompt-detail')).not.toBeInTheDocument()
      })
    })

    it('does not close detail view on other key presses', async () => {
      renderWithProviders(<App />)
      
      // Wait for app to finish loading
      await waitForAppToLoad()
      
      // Click on a card to open detail view
      fireEvent.click(screen.getByTestId('card-button-1'))
      
      await waitFor(() => {
        expect(screen.getByTestId('prompt-detail')).toBeInTheDocument()
      })
      
      // Press other keys
      fireEvent.keyDown(window, { key: 'Enter' })
      fireEvent.keyDown(window, { key: 'Space' })
      fireEvent.keyDown(window, { key: 'a' })
      fireEvent.keyDown(window, { key: 'P' })
      fireEvent.keyDown(window, { key: 'p' })
      
      await waitForLoadingToFinish()
      
      // Should still show detail view
      expect(screen.getByTestId('prompt-detail')).toBeInTheDocument()
      expect(screen.queryByTestId('main-content')).not.toBeInTheDocument()
    })
  })

  describe('Component Rendering and Props Passing', () => {
    it('renders correct conditional view based on showDetailPreview state', async () => {
      renderWithProviders(<App />)
      
      // Wait for app to finish loading
      await waitForAppToLoad()
      
      // Main view should be rendered initially
      expect(screen.getByTestId('two-tier-sidebar')).toBeInTheDocument()
      expect(screen.getByTestId('main-content')).toBeInTheDocument()
      expect(screen.queryByTestId('prompt-detail')).not.toBeInTheDocument()
      
      // Switch to detail view by clicking on a card
      fireEvent.click(screen.getByTestId('card-button-1'))
      
      await waitFor(() => {
        expect(screen.queryByTestId('two-tier-sidebar')).not.toBeInTheDocument()
        expect(screen.queryByTestId('main-content')).not.toBeInTheDocument()
        expect(screen.getByTestId('prompt-detail')).toBeInTheDocument()
      })
    })

    it('passes correct props to TwoTierSidebar component', async () => {
      renderWithProviders(<App />)
      
      // Wait for app to finish loading
      await waitForAppToLoad()
      
      const sidebar = screen.getByTestId('two-tier-sidebar')
      expect(sidebar).toBeInTheDocument()
      
      // Test that status and category filter functions work (implies correct props)
      fireEvent.click(screen.getByTestId('status-filter-approved'))
      expect(screen.getByTestId('selected-status')).toHaveTextContent('Approved')
      
      fireEvent.click(screen.getByTestId('category-filter-investment'))
      expect(screen.getByTestId('selected-category')).toHaveTextContent('Investment Analysis')
    })

    it('passes correct props to MainContent component', async () => {
      renderWithProviders(<App />)
      
      // Wait for app to finish loading
      await waitForAppToLoad()
      
      const mainContent = screen.getByTestId('main-content')
      expect(mainContent).toBeInTheDocument()
      
      // Test that all required elements are present (implies correct props)
      expect(screen.getByTestId('search-input')).toBeInTheDocument()
      expect(screen.getByTestId('sort-select')).toBeInTheDocument()
      expect(screen.getByTestId('prompts-count')).toHaveTextContent('3')
      
      // Test that filtered prompts are passed correctly
      fireEvent.click(screen.getByTestId('status-filter-approved'))
      await waitFor(() => {
        expect(screen.getByTestId('prompts-count')).toHaveTextContent('1')
      })
    })

    it('passes correct vote handler to MainContent', async () => {
      renderWithProviders(<App />)
      
      // Wait for app to finish loading
      await waitForAppToLoad()
      
      // Test that vote functionality works (implies correct onVote prop)
      fireEvent.click(screen.getByTestId('vote-button-1'))
      
      await waitFor(() => {
        expect(screen.getByTestId('prompt-votes-1')).toHaveTextContent('9')
        expect(screen.getByTestId('prompt-uservote-1')).toHaveTextContent('up')
      })
    })

    it('passes correct card click handler to MainContent', async () => {
      renderWithProviders(<App />)
      
      // Wait for app to finish loading
      await waitForAppToLoad()
      
      // Test that card click handler is called
      fireEvent.click(screen.getByTestId('card-button-1'))
      
      expect(consoleSpy).toHaveBeenCalledWith('Navigate to prompt:', 1)
    })

    it('handles PromptDetail back navigation correctly', async () => {
      renderWithProviders(<App />)
      
      // Wait for app to finish loading
      await waitForAppToLoad()
      
      // Switch to detail view by clicking a card
      fireEvent.click(screen.getByTestId('card-button-1'))
      await waitFor(() => {
        expect(screen.getByTestId('prompt-detail')).toBeInTheDocument()
      })
      
      // Click back button
      fireEvent.click(screen.getByTestId('back-button'))
      
      await waitFor(() => {
        expect(screen.getByTestId('main-content')).toBeInTheDocument()
        expect(screen.queryByTestId('prompt-detail')).not.toBeInTheDocument()
      })
    })
  })

  describe('Edge Cases', () => {
    it('handles empty search results gracefully', async () => {
      renderWithProviders(<App />)
      
      // Wait for app to finish loading
      await waitForAppToLoad()
      
      const searchInput = screen.getByTestId('search-input')
      fireEvent.change(searchInput, { target: { value: 'xyz123nonexistent' } })
      
      await waitFor(() => {
        expect(screen.getByTestId('prompts-count')).toHaveTextContent('0')
        expect(screen.queryByTestId('prompt-1')).not.toBeInTheDocument()
        expect(screen.queryByTestId('prompt-2')).not.toBeInTheDocument()
        expect(screen.queryByTestId('prompt-3')).not.toBeInTheDocument()
      })
    })

    it('handles rapid filter changes correctly', async () => {
      renderWithProviders(<App />)
      
      // Wait for app to finish loading
      await waitForAppToLoad()
      
      // Rapidly change filters
      fireEvent.click(screen.getByTestId('status-filter-approved'))
      fireEvent.click(screen.getByTestId('category-filter-investment'))
      fireEvent.click(screen.getByTestId('status-clear'))
      fireEvent.click(screen.getByTestId('category-filter-due-diligence'))
      
      await waitFor(() => {
        expect(screen.getByTestId('prompts-count')).toHaveTextContent('1')
        expect(screen.getByTestId('prompt-2')).toBeInTheDocument()
      })
    })

    it('maintains vote state after filtering', async () => {
      renderWithProviders(<App />)
      
      // Wait for app to finish loading
      await waitForAppToLoad()
      
      // Vote on prompt-1
      fireEvent.click(screen.getByTestId('vote-button-1'))
      await waitFor(() => {
        expect(screen.getByTestId('prompt-votes-1')).toHaveTextContent('9')
      })
      
      // Apply filter that hides prompt-1
      fireEvent.click(screen.getByTestId('status-filter-under-review'))
      await waitFor(() => {
        expect(screen.queryByTestId('prompt-1')).not.toBeInTheDocument()
      })
      
      // Clear filter to show prompt-1 again
      fireEvent.click(screen.getByTestId('status-clear'))
      await waitFor(() => {
        expect(screen.getByTestId('prompt-1')).toBeInTheDocument()
        expect(screen.getByTestId('prompt-votes-1')).toHaveTextContent('9')
        expect(screen.getByTestId('prompt-uservote-1')).toHaveTextContent('up')
      })
    })

    it('handles whitespace-only search terms correctly', async () => {
      renderWithProviders(<App />)
      
      // Wait for app to finish loading
      await waitForAppToLoad()
      
      const searchInput = screen.getByTestId('search-input')
      fireEvent.change(searchInput, { target: { value: '   ' } })
      
      await waitFor(() => {
        // Whitespace-only search terms are treated as actual search terms
        // Since no content matches spaces, should show 0 results
        expect(screen.getByTestId('prompts-count')).toHaveTextContent('0')
      })
    })

    it('handles special characters in search correctly', async () => {
      renderWithProviders(<App />)
      
      // Wait for app to finish loading
      await waitForAppToLoad()
      
      const searchInput = screen.getByTestId('search-input')
      fireEvent.change(searchInput, { target: { value: '!@#$%^&*()' } })
      
      await waitFor(() => {
        expect(screen.getByTestId('prompts-count')).toHaveTextContent('0')
      })
    })

    it('preserves search term when toggling between main and detail views', async () => {
      renderWithProviders(<App />)
      
      // Wait for app to finish loading
      await waitForAppToLoad()
      
      const searchInput = screen.getByTestId('search-input')
      fireEvent.change(searchInput, { target: { value: 'management' } })
      
      await waitFor(() => {
        expect(searchInput).toHaveValue('management')
      })
      
      // Switch to detail view by clicking a card
      fireEvent.click(screen.getByTestId('card-button-2')) // management card
      await waitFor(() => {
        expect(screen.getByTestId('prompt-detail')).toBeInTheDocument()
      })
      
      // Go back to main view
      fireEvent.click(screen.getByTestId('back-button'))
      await waitFor(() => {
        expect(screen.getByTestId('main-content')).toBeInTheDocument()
        expect(screen.getByTestId('search-input')).toHaveValue('management')
        expect(screen.getByTestId('prompts-count')).toHaveTextContent('1')
      })
    })
  })
})