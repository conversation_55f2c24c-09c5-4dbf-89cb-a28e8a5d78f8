import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { screen, fireEvent, waitFor, cleanup } from '@testing-library/react'
import { renderWithProviders, waitForAppToLoad } from '../test/utils'
import App from '../App'
import config from '../config'
import { browserDbHelpers } from '../api/browserDatabase'

describe('Database Features Regression Tests', () => {
  beforeEach(async () => {
    // Clear localStorage before each test
    if (typeof localStorage !== 'undefined') {
      localStorage.clear()
    }
    
    // Mock console to avoid noise
    vi.spyOn(console, 'log').mockImplementation(() => {})
    vi.spyOn(console, 'warn').mockImplementation(() => {})
    vi.spyOn(console, 'error').mockImplementation(() => {})

    // Initialize browser database for tests
    const { initializeBrowserDatabase } = await import('../api/browserDatabase')
    initializeBrowserDatabase()
  })

  afterEach(() => {
    cleanup()
    vi.restoreAllMocks()
  })

  describe('Database Toggle Functionality', () => {
    it('if database toggle is set to false then JSON backend should be used', async () => {
      const originalValue = config.USE_DATABASE_INSTEAD_OF_JSON_FILE
      config.USE_DATABASE_INSTEAD_OF_JSON_FILE = false
      
      try {
        renderWithProviders(<App />)
        await waitForAppToLoad()
        
        // Verify app loads correctly with header
        expect(screen.getByText('Apollo Prompt Library')).toBeInTheDocument()
        
        // Verify search functionality is available (indicating app loaded)
        expect(screen.getByPlaceholderText('Search prompts...')).toBeInTheDocument()
        
      } finally {
        config.USE_DATABASE_INSTEAD_OF_JSON_FILE = originalValue
      }
    })

    it('if database toggle is set to true then browser database should be used', async () => {
      const originalValue = config.USE_DATABASE_INSTEAD_OF_JSON_FILE
      config.USE_DATABASE_INSTEAD_OF_JSON_FILE = true
      
      try {
        renderWithProviders(<App />)
        await waitForAppToLoad()
        
        // Verify app loads correctly with header
        expect(screen.getByText('Apollo Prompt Library')).toBeInTheDocument()
        
        // Verify search functionality is available (indicating app loaded)
        expect(screen.getByPlaceholderText('Search prompts...')).toBeInTheDocument()
        
      } finally {
        config.USE_DATABASE_INSTEAD_OF_JSON_FILE = originalValue
      }
    })

    it('if config.USE_DATABASE_INSTEAD_OF_JSON_FILE works correctly then data should load properly in both modes', async () => {
      const originalValue = config.USE_DATABASE_INSTEAD_OF_JSON_FILE
      
      // Test JSON mode
      config.USE_DATABASE_INSTEAD_OF_JSON_FILE = false
      let jsonModeWorks = false
      
      try {
        renderWithProviders(<App />)
        await waitForAppToLoad()
        
        // Verify app functions correctly
        expect(screen.getByText('Apollo Prompt Library')).toBeInTheDocument()
        expect(screen.getByPlaceholderText('Search prompts...')).toBeInTheDocument()
        jsonModeWorks = true
        
        cleanup()
      } finally {
        config.USE_DATABASE_INSTEAD_OF_JSON_FILE = originalValue
      }
      
      // Test Database mode
      localStorage.clear()
      config.USE_DATABASE_INSTEAD_OF_JSON_FILE = true
      let dbModeWorks = false
      
      try {
        renderWithProviders(<App />)
        await waitForAppToLoad()
        
        // Verify app functions correctly
        expect(screen.getByText('Apollo Prompt Library')).toBeInTheDocument()
        expect(screen.getByPlaceholderText('Search prompts...')).toBeInTheDocument()
        dbModeWorks = true
        
        // Both modes should work
        expect(jsonModeWorks).toBe(true)
        expect(dbModeWorks).toBe(true)
        
      } finally {
        config.USE_DATABASE_INSTEAD_OF_JSON_FILE = originalValue
      }
    })

    it('if backend switching works then localStorage should be populated only in database mode', async () => {
      const originalValue = config.USE_DATABASE_INSTEAD_OF_JSON_FILE
      
      // Test Database mode - should populate localStorage  
      localStorage.clear()
      config.USE_DATABASE_INSTEAD_OF_JSON_FILE = true
      
      try {
        // Reinitialize browser database to ensure localStorage is populated
        const { initializeBrowserDatabase } = await import('../api/browserDatabase')
        initializeBrowserDatabase()
        
        renderWithProviders(<App />)
        await waitForAppToLoad()
        
        // localStorage should be populated in database mode
        expect(localStorage.getItem('apollo_prompts')).toBeTruthy()
        expect(localStorage.getItem('apollo_categories')).toBeTruthy()
        expect(localStorage.getItem('apollo_users')).toBeTruthy()
        expect(localStorage.getItem('apollo_votes')).toBeTruthy()
        expect(localStorage.getItem('apollo_sessions')).toBeTruthy()
        
      } finally {
        config.USE_DATABASE_INSTEAD_OF_JSON_FILE = originalValue
      }
    })
  })

  describe('Data Persistence and Migration', () => {
    it('if localStorage persistence works then data should survive page reloads', async () => {
      const originalValue = config.USE_DATABASE_INSTEAD_OF_JSON_FILE
      config.USE_DATABASE_INSTEAD_OF_JSON_FILE = true
      
      try {
        // Initialize browser database first
        const { initializeBrowserDatabase } = await import('../api/browserDatabase')
        initializeBrowserDatabase()
        
        // First render - initialize data
        renderWithProviders(<App />)
        await waitForAppToLoad()
        
        // Get initial data
        const initialPrompts = JSON.parse(localStorage.getItem('apollo_prompts'))
        expect(initialPrompts).toBeTruthy()
        expect(initialPrompts.length).toBeGreaterThan(0)
        
        cleanup()
        
        // Second render - data should persist
        renderWithProviders(<App />)
        await waitForAppToLoad()
        
        const persistedPrompts = JSON.parse(localStorage.getItem('apollo_prompts'))
        expect(persistedPrompts).toEqual(initialPrompts)
        
      } finally {
        config.USE_DATABASE_INSTEAD_OF_JSON_FILE = originalValue
      }
    })

    it('if data migration works then switching from JSON to database should preserve functionality', async () => {
      const originalValue = config.USE_DATABASE_INSTEAD_OF_JSON_FILE
      
      // Start with JSON mode
      config.USE_DATABASE_INSTEAD_OF_JSON_FILE = false
      
      let jsonModeWorks = false
      
      try {
        renderWithProviders(<App />)
        await waitForAppToLoad()
        
        // Verify JSON mode loads correctly
        expect(screen.getByText('Apollo Prompt Library')).toBeInTheDocument()
        expect(screen.getByPlaceholderText('Search prompts...')).toBeInTheDocument()
        jsonModeWorks = true
        cleanup()
        
        // Switch to database mode
        config.USE_DATABASE_INSTEAD_OF_JSON_FILE = true
        localStorage.clear()
        
        // Initialize browser database for database mode
        const { initializeBrowserDatabase } = await import('../api/browserDatabase')
        initializeBrowserDatabase()
        
        renderWithProviders(<App />)
        await waitForAppToLoad()
        
        // Verify database mode loads correctly
        expect(screen.getByText('Apollo Prompt Library')).toBeInTheDocument()
        expect(screen.getByPlaceholderText('Search prompts...')).toBeInTheDocument()
        
        // Both modes should work
        expect(jsonModeWorks).toBe(true)
        
      } finally {
        config.USE_DATABASE_INSTEAD_OF_JSON_FILE = originalValue
      }
    })

    it('if browser database initialization works then test data should be loaded correctly in test environment', async () => {
      const originalValue = config.USE_DATABASE_INSTEAD_OF_JSON_FILE
      config.USE_DATABASE_INSTEAD_OF_JSON_FILE = true
      
      try {
        // Initialize browser database first
        const { initializeBrowserDatabase } = await import('../api/browserDatabase')
        initializeBrowserDatabase()
        
        renderWithProviders(<App />)
        await waitForAppToLoad()
        
        // Verify test-specific prompts are loaded
        expect(screen.getByText('Investment Thesis Development')).toBeInTheDocument()
        expect(screen.getByText('Management Team Assessment')).toBeInTheDocument()
        expect(screen.getByText('Financial Model Review')).toBeInTheDocument()
        
        // Verify test data structure
        const prompts = JSON.parse(localStorage.getItem('apollo_prompts'))
        expect(prompts).toHaveLength(3) // Test environment has 3 prompts
        expect(prompts[0]).toMatchObject({
          id: 1,
          title: 'Investment Thesis Development',
          category: 'Investment Analysis',
          status: 'Approved',
          votes: 8
        })
        
      } finally {
        config.USE_DATABASE_INSTEAD_OF_JSON_FILE = originalValue
      }
    })

    it('if localStorage structure is correct then all required keys should be present', async () => {
      const originalValue = config.USE_DATABASE_INSTEAD_OF_JSON_FILE
      config.USE_DATABASE_INSTEAD_OF_JSON_FILE = true
      
      try {
        // Initialize browser database first
        const { initializeBrowserDatabase } = await import('../api/browserDatabase')
        initializeBrowserDatabase()
        
        renderWithProviders(<App />)
        await waitForAppToLoad()
        
        // Verify all expected localStorage keys exist
        const expectedKeys = ['apollo_prompts', 'apollo_categories', 'apollo_users', 'apollo_votes', 'apollo_sessions']
        
        for (const key of expectedKeys) {
          expect(localStorage.getItem(key)).toBeTruthy()
          
          // Verify data is valid JSON
          const data = JSON.parse(localStorage.getItem(key))
          expect(Array.isArray(data)).toBe(true)
        }
        
      } finally {
        config.USE_DATABASE_INSTEAD_OF_JSON_FILE = originalValue
      }
    })
  })

  describe('Browser Database Helper Functions', () => {
    beforeEach(() => {
      localStorage.clear()
      // Initialize browser database
      const { initializeBrowserDatabase } = require('../api/browserDatabase')
      initializeBrowserDatabase()
    })

    it('if browserDbHelpers.getPromptsWithFilters works then it should return enriched prompt data', () => {
      const prompts = browserDbHelpers.getPromptsWithFilters()
      
      expect(Array.isArray(prompts)).toBe(true)
      expect(prompts.length).toBeGreaterThan(0)
      
      // Check that prompts have required fields
      const firstPrompt = prompts[0]
      expect(firstPrompt).toHaveProperty('id')
      expect(firstPrompt).toHaveProperty('title')
      expect(firstPrompt).toHaveProperty('category_name')
      expect(firstPrompt).toHaveProperty('author_name')
      expect(firstPrompt).toHaveProperty('votes')
      expect(firstPrompt).toHaveProperty('comments')
    })

    it('if browserDbHelpers.getPromptById works then it should return correct prompt', () => {
      const prompt = browserDbHelpers.getPromptById(1)
      
      expect(prompt).toBeTruthy()
      expect(prompt.id).toBe(1)
      expect(prompt.title).toBe('Investment Thesis Development')
    })

    it('if browserDbHelpers.getCategories works then it should return categories with counts', () => {
      const categories = browserDbHelpers.getCategories()
      
      expect(Array.isArray(categories)).toBe(true)
      expect(categories.length).toBeGreaterThan(0)
      
      const firstCategory = categories[0]
      expect(firstCategory).toHaveProperty('name')
      expect(firstCategory).toHaveProperty('count')
      expect(firstCategory).toHaveProperty('description')
      expect(typeof firstCategory.count).toBe('number')
    })

    it('if browserDbHelpers.getUserByUsername works then it should return correct user', () => {
      const user = browserDbHelpers.getUserByUsername('alexf')
      
      expect(user).toBeTruthy()
      expect(user.username).toBe('alexf')
      expect(user.email).toBe('<EMAIL>')
      expect(user.role).toBe('admin')
    })
  })
})