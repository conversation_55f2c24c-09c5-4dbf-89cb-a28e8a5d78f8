import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { screen, fireEvent, waitFor, cleanup } from '@testing-library/react'
import { renderWithProviders, waitForAppToLoad } from '../test/utils'
import { render } from '@testing-library/react'
import PromptDetail from '../components/PromptDetail'
import config from '../config'
import App from '../App'

describe('Input/Output Examples Integration Regression Tests', () => {
  beforeEach(() => {
    // Clear localStorage before each test
    if (typeof localStorage !== 'undefined') {
      localStorage.clear()
    }
    
    // Mock console to avoid noise
    vi.spyOn(console, 'log').mockImplementation(() => {})
    vi.spyOn(console, 'warn').mockImplementation(() => {})
    vi.spyOn(console, 'error').mockImplementation(() => {})
  })

  afterEach(() => {
    cleanup()
    vi.restoreAllMocks()
  })

  describe('PromptDetail Examples Display', () => {
    it('if examples display correctly then PromptDetail should show input/output examples section', () => {
      const mockOnBack = vi.fn()
      const mockOnVote = vi.fn()
      
      render(<PromptDetail promptId={1} onBack={mockOnBack} onVote={mockOnVote} />)
      
      // Verify examples section is present
      expect(screen.getByText('Input Output Examples')).toBeInTheDocument()
      
      // Verify examples container exists
      expect(screen.getByClassName('examples-container')).toBeInTheDocument()
    })

    it('if two-column layout works then examples should display in input/output columns', () => {
      const mockOnBack = vi.fn()
      const mockOnVote = vi.fn()
      
      render(<PromptDetail promptId={1} onBack={mockOnBack} onVote={mockOnVote} />)
      
      // Look for column headers
      expect(screen.getAllByText('Input')).toHaveLength(3) // Default has 3 examples
      expect(screen.getAllByText('Output')).toHaveLength(3)
      
      // Verify two-column structure exists
      expect(screen.getAllByClassName('example-content-two-column')).toHaveLength(3)
      expect(screen.getAllByClassName('example-input-column')).toHaveLength(3)
      expect(screen.getAllByClassName('example-output-column')).toHaveLength(3)
    })

    it('if fallback examples work then hardcoded examples should display when database examples are empty', () => {
      const mockOnBack = vi.fn()
      const mockOnVote = vi.fn()
      
      render(<PromptDetail promptId={1} onBack={mockOnBack} onVote={mockOnVote} />)
      
      // Verify fallback examples are displayed (these are the hardcoded ones)
      expect(screen.getByText(/Upload the Q3 portfolio performance data/)).toBeInTheDocument()
      expect(screen.getByText(/Review the due diligence checklist/)).toBeInTheDocument()
      expect(screen.getByText(/Generate an investment thesis/)).toBeInTheDocument()
      
      // Verify outputs are displayed
      expect(screen.getByText(/Key Performance Metrics/)).toBeInTheDocument()
      expect(screen.getByText(/Due Diligence Review - TechCorp/)).toBeInTheDocument()
      expect(screen.getByText(/Renewable Energy Investment Thesis/)).toBeInTheDocument()
    })

    it('if real examples integration works then database examples should override fallback when available', async () => {
      const originalValue = config.USE_DATABASE_INSTEAD_OF_JSON_FILE
      config.USE_DATABASE_INSTEAD_OF_JSON_FILE = true
      
      try {
        // First add some real examples to localStorage
        const testPromptWithExamples = {
          id: 1,
          title: 'Test Prompt with Examples',
          description: 'A test prompt',
          category: 'Test',
          tags: [],
          status: 'Approved',
          votes: 10,
          author: 'Test Author',
          timeAgo: '1 hour ago',
          statusCount: 5,
          difficulty: 'Intermediate',
          comments: 2,
          userVote: null,
          content: 'Test content',
          inputOutputExamples: [
            {
              input: 'Test input from database',
              output: 'Test output from database'
            }
          ]
        }
        
        localStorage.setItem('apollo_prompts', JSON.stringify([testPromptWithExamples]))
        
        const mockOnBack = vi.fn()
        const mockOnVote = vi.fn()
        
        render(<PromptDetail promptId={1} onBack={mockOnBack} onVote={mockOnVote} />)
        
        // Should display database examples instead of fallback
        expect(screen.getByText('Test input from database')).toBeInTheDocument()
        expect(screen.getByText('Test output from database')).toBeInTheDocument()
        
        // Should not display fallback examples
        expect(screen.queryByText(/Upload the Q3 portfolio performance data/)).not.toBeInTheDocument()
        
      } finally {
        config.USE_DATABASE_INSTEAD_OF_JSON_FILE = originalValue
      }
    })

    it('if examples formatting works then pre tags should preserve output formatting', () => {
      const mockOnBack = vi.fn()
      const mockOnVote = vi.fn()
      
      render(<PromptDetail promptId={1} onBack={mockOnBack} onVote={mockOnVote} />)
      
      // Verify pre tags are used for outputs to preserve formatting
      const preElements = screen.getAllByTagName('pre')
      expect(preElements.length).toBeGreaterThan(0)
      
      // Check that structured output formatting is preserved
      expect(screen.getByText(/\*\*Key Performance Metrics:\*\*/)).toBeInTheDocument()
      expect(screen.getByText(/- Total Portfolio Value:/)).toBeInTheDocument()
    })

    it('if examples structure is correct then each example should have proper dividers', () => {
      const mockOnBack = vi.fn()
      const mockOnVote = vi.fn()
      
      render(<PromptDetail promptId={1} onBack={mockOnBack} onVote={mockOnVote} />)
      
      // Check for example dividers between input and output columns
      expect(screen.getAllByClassName('example-divider')).toHaveLength(3)
      
      // Check for proper example item structure
      expect(screen.getAllByClassName('example-item')).toHaveLength(3)
    })
  })

  describe('Examples Integration with Database', () => {
    it('if database examples integration works then prompts should load with inputOutputExamples field', async () => {
      const originalValue = config.USE_DATABASE_INSTEAD_OF_JSON_FILE
      config.USE_DATABASE_INSTEAD_OF_JSON_FILE = true
      
      try {
        renderWithProviders(<App />)
        await waitForAppToLoad()
        
        // Verify examples are in localStorage structure
        const prompts = JSON.parse(localStorage.getItem('apollo_prompts'))
        expect(prompts).toBeTruthy()
        
        // Each prompt should have inputOutputExamples field (even if empty array)
        prompts.forEach(prompt => {
          expect(prompt).toHaveProperty('inputOutputExamples')
          expect(Array.isArray(prompt.inputOutputExamples)).toBe(true)
        })
        
      } finally {
        config.USE_DATABASE_INSTEAD_OF_JSON_FILE = originalValue
      }
    })

    it('if examples creation works then new prompts should support inputOutputExamples parameter', async () => {
      const originalValue = config.USE_DATABASE_INSTEAD_OF_JSON_FILE
      config.USE_DATABASE_INSTEAD_OF_JSON_FILE = true
      
      try {
        // Initialize database
        const { initializeBrowserDatabase, browserDbHelpers } = await import('../api/browserDatabase')
        initializeBrowserDatabase()
        
        const testExamples = JSON.stringify([
          {
            input: 'Test input for new prompt',
            output: 'Test output for new prompt'
          }
        ])
        
        // Create new prompt with examples
        const result = browserDbHelpers.createPrompt(
          'Test Prompt with Examples',
          'Test description',
          'Test content',
          'Test Category',
          1,
          'Approved',
          'Intermediate',
          testExamples
        )
        
        expect(result.lastInsertRowid).toBeTruthy()
        
        // Verify the prompt was created with examples
        const createdPrompt = browserDbHelpers.getPromptById(result.lastInsertRowid)
        expect(createdPrompt.inputOutputExamples).toEqual([
          {
            input: 'Test input for new prompt',
            output: 'Test output for new prompt'
          }
        ])
        
      } finally {
        config.USE_DATABASE_INSTEAD_OF_JSON_FILE = originalValue
      }
    })

    it('if examples validation works then invalid JSON should not break the application', async () => {
      const originalValue = config.USE_DATABASE_INSTEAD_OF_JSON_FILE
      config.USE_DATABASE_INSTEAD_OF_JSON_FILE = true
      
      try {
        // Create prompt with invalid examples JSON
        const testPromptWithInvalidExamples = {
          id: 1,
          title: 'Test Prompt',
          description: 'Test description',
          category: 'Test',
          tags: [],
          status: 'Approved',
          votes: 10,
          author: 'Test Author',
          timeAgo: '1 hour ago',
          statusCount: 5,
          difficulty: 'Intermediate',
          comments: 2,
          userVote: null,
          content: 'Test content',
          inputOutputExamples: 'invalid-json-string' // Invalid format
        }
        
        localStorage.setItem('apollo_prompts', JSON.stringify([testPromptWithInvalidExamples]))
        
        const mockOnBack = vi.fn()
        const mockOnVote = vi.fn()
        
        // Should not throw error and should fall back to default examples
        expect(() => {
          render(<PromptDetail promptId={1} onBack={mockOnBack} onVote={mockOnVote} />)
        }).not.toThrow()
        
        // Should display fallback examples
        expect(screen.getByText(/Upload the Q3 portfolio performance data/)).toBeInTheDocument()
        
      } finally {
        config.USE_DATABASE_INSTEAD_OF_JSON_FILE = originalValue
      }
    })
  })

  describe('Examples Display Edge Cases', () => {
    it('if empty examples array works then fallback examples should be displayed', () => {
      const testPromptWithEmptyExamples = {
        id: 1,
        title: 'Test Prompt',
        description: 'Test description',
        category: 'Test',
        inputOutputExamples: [] // Empty array
      }
      
      localStorage.setItem('apollo_prompts', JSON.stringify([testPromptWithEmptyExamples]))
      
      const mockOnBack = vi.fn()
      const mockOnVote = vi.fn()
      
      render(<PromptDetail promptId={1} onBack={mockOnBack} onVote={mockOnVote} />)
      
      // Should display fallback examples when array is empty
      expect(screen.getByText(/Upload the Q3 portfolio performance data/)).toBeInTheDocument()
    })

    it('if missing inputOutputExamples field works then fallback examples should be displayed', () => {
      const testPromptWithoutExamples = {
        id: 1,
        title: 'Test Prompt',
        description: 'Test description',
        category: 'Test'
        // inputOutputExamples field is missing
      }
      
      localStorage.setItem('apollo_prompts', JSON.stringify([testPromptWithoutExamples]))
      
      const mockOnBack = vi.fn()
      const mockOnVote = vi.fn()
      
      render(<PromptDetail promptId={1} onBack={mockOnBack} onVote={mockOnVote} />)
      
      // Should display fallback examples when field is missing
      expect(screen.getByText(/Upload the Q3 portfolio performance data/)).toBeInTheDocument()
    })

    it('if long examples work then content should be properly displayed without breaking layout', () => {
      const testPromptWithLongExamples = {
        id: 1,
        title: 'Test Prompt',
        description: 'Test description',
        category: 'Test',
        inputOutputExamples: [
          {
            input: 'This is a very long input example that contains multiple sentences and should test how the layout handles extended content. It includes various types of text and formatting that might be present in real-world examples.',
            output: 'This is a correspondingly long output example that demonstrates how the system would handle extensive responses. It includes multiple paragraphs:\n\nParagraph 1: First section of the response\nParagraph 2: Second section with more details\nParagraph 3: Final section with conclusions\n\nFormatted elements:\n- Bullet point 1\n- Bullet point 2\n- Bullet point 3\n\nAnd numbered lists:\n1. First item\n2. Second item\n3. Third item'
          }
        ]
      }
      
      localStorage.setItem('apollo_prompts', JSON.stringify([testPromptWithLongExamples]))
      
      const mockOnBack = vi.fn()
      const mockOnVote = vi.fn()
      
      render(<PromptDetail promptId={1} onBack={mockOnBack} onVote={mockOnVote} />)
      
      // Should display the long content without breaking
      expect(screen.getByText(/This is a very long input example/)).toBeInTheDocument()
      expect(screen.getByText(/This is a correspondingly long output example/)).toBeInTheDocument()
      
      // Verify structure is maintained
      expect(screen.getByClassName('example-content-two-column')).toBeInTheDocument()
    })

    it('if examples with special characters work then content should be properly escaped', () => {
      const testPromptWithSpecialChars = {
        id: 1,
        title: 'Test Prompt',
        description: 'Test description',
        category: 'Test',
        inputOutputExamples: [
          {
            input: 'Test input with <script>alert("xss")</script> and & special chars',
            output: 'Test output with "quotes" and \'apostrophes\' and <tags>'
          }
        ]
      }
      
      localStorage.setItem('apollo_prompts', JSON.stringify([testPromptWithSpecialChars]))
      
      const mockOnBack = vi.fn()
      const mockOnVote = vi.fn()
      
      render(<PromptDetail promptId={1} onBack={mockOnBack} onVote={mockOnVote} />)
      
      // Content should be displayed but not executed as code
      expect(screen.getByText(/Test input with.*script.*alert.*xss.*script.*and.*special chars/)).toBeInTheDocument()
      expect(screen.getByText(/Test output with.*quotes.*and.*apostrophes.*and.*tags/)).toBeInTheDocument()
    })
  })
})