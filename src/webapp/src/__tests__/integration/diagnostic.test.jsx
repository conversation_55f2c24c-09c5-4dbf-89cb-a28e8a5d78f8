import React from 'react'
import { screen, vi, describe, it } from 'vitest'
import { renderWithProviders } from '../../test/utils'
import App from '../../App'

describe('Diagnostic Test', () => {
  it('should show what text is actually rendered', () => {
    const { container } = renderWithProviders(<App />)
    
    // Get all text content
    const allText = container.textContent
    console.log('All text content length:', allText.length)
    console.log('All text content (first 500 chars):', allText.substring(0, 500))
    
    // Check for specific strings
    const hasManagement = allText.includes('Management Team Assessment')
    const hasInvestment = allText.includes('Investment Thesis Development')
    const hasFinancial = allText.includes('Financial Model Validation')
    
    console.log('Contains "Management Team Assessment":', hasManagement)
    console.log('Contains "Investment Thesis Development":', hasInvestment)
    console.log('Contains "Financial Model Validation":', hasFinancial)
    
    // Look for prompt cards
    const promptCards = container.querySelectorAll('.prompt-card')
    console.log('Found prompt cards:', promptCards.length)
    
    // Look for main content
    const mainContent = container.querySelector('.main-content')
    console.log('Main content found:', !!mainContent)
    if (mainContent) {
      console.log('Main content text (first 300 chars):', mainContent.textContent.substring(0, 300))
    }
    
    // Look for Apollo Prompt Library title
    const hasTitle = allText.includes('Apollo Prompt Library')
    console.log('Contains "Apollo Prompt Library":', hasTitle)
  })
})