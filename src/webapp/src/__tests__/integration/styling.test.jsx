import React from 'react'
import { render } from '@testing-library/react'
import PromptCard from '../../components/PromptCard'
import { AuthProvider } from '../../auth/AuthContext'

describe('PromptCard Styling', () => {
  const mockPrompt = {
    id: 1,
    title: "Test Prompt",
    description: "Test description for prompt card styling",
    category: "Investment Analysis", 
    tags: ["test", "styling"],
    status: "Approved",
    votes: 5,
    author: "Test Author",
    timeAgo: "1 hour ago",
    difficulty: "Intermediate",
    comments: 3,
    userVote: null
  }

  const TestWrapper = ({ children }) => (
    <AuthProvider>
      {children}
    </AuthProvider>
  )

  it('should render prompt card with proper structure', () => {
    const { container } = render(
      <TestWrapper>
        <PromptCard 
          prompt={mockPrompt} 
          onVote={() => {}} 
          onCardClick={() => {}} 
        />
      </TestWrapper>
    )
    
    const promptCard = container.querySelector('.prompt-card')
    expect(promptCard).toBeInTheDocument()
    
    // Verify the card has the correct CSS class for dark theme styling
    expect(promptCard).toHaveClass('prompt-card')
  })

  it('should have proper CSS classes applied', () => {
    const { container } = render(
      <TestWrapper>
        <PromptCard 
          prompt={mockPrompt} 
          onVote={() => {}} 
          onCardClick={() => {}} 
        />
      </TestWrapper>
    )
    
    // Verify essential CSS classes are present
    expect(container.querySelector('.prompt-card')).toBeInTheDocument()
    expect(container.querySelector('.vote-section')).toBeInTheDocument()
    expect(container.querySelector('.card-content')).toBeInTheDocument()
    expect(container.querySelector('.card-title')).toBeInTheDocument()
    expect(container.querySelector('.card-description')).toBeInTheDocument()
    expect(container.querySelector('.status-badge')).toBeInTheDocument()
  })
})