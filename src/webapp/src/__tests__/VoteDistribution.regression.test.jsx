import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { screen, fireEvent, waitFor, cleanup } from '@testing-library/react'
import { renderWithProviders, waitForAppToLoad } from '../test/utils'
import App from '../App'
import config from '../config'
import { browserDbHelpers } from '../api/browserDatabase'

describe('Vote Distribution System Regression Tests', () => {
  beforeEach(() => {
    // Clear localStorage before each test
    if (typeof localStorage !== 'undefined') {
      localStorage.clear()
    }
    
    // Mock console to avoid noise
    vi.spyOn(console, 'log').mockImplementation(() => {})
    vi.spyOn(console, 'warn').mockImplementation(() => {})
    vi.spyOn(console, 'error').mockImplementation(() => {})
  })

  afterEach(() => {
    cleanup()
    vi.restoreAllMocks()
  })

  describe('Vote Numbers and Range Testing', () => {
    it('if vote numbers are in 5-291 range then test data should reflect realistic vote counts', async () => {
      const originalValue = config.USE_DATABASE_INSTEAD_OF_JSON_FILE
      config.USE_DATABASE_INSTEAD_OF_JSON_FILE = true
      
      try {
        renderWithProviders(<App />)
        await waitForAppToLoad()
        
        // Check that vote counts are in realistic range (5-291)
        const prompts = JSON.parse(localStorage.getItem('apollo_prompts'))
        expect(prompts).toBeTruthy()
        expect(Array.isArray(prompts)).toBe(true)
        
        prompts.forEach(prompt => {
          expect(prompt.votes).toBeGreaterThanOrEqual(5)
          expect(prompt.votes).toBeLessThanOrEqual(291)
        })
        
        // Verify vote elements exist by finding them through query
        const promptCards = screen.getAllByTestId(/prompt-card-/)
        expect(promptCards.length).toBeGreaterThan(0)
        
        // Check vote counts in displayed prompts
        let voteElementsFound = 0
        prompts.slice(0, 10).forEach(prompt => { // Check first 10 displayed prompts
          try {
            const voteElement = screen.getByTestId(`prompt-votes-${prompt.id}`)
            const voteCount = parseInt(voteElement.textContent)
            expect(voteCount).toBeGreaterThanOrEqual(5)
            expect(voteCount).toBeLessThanOrEqual(291)
            voteElementsFound++
          } catch (e) {
            // Some prompts might not be rendered in view, which is okay
          }
        })
        
        expect(voteElementsFound).toBeGreaterThan(0)
        
      } finally {
        config.USE_DATABASE_INSTEAD_OF_JSON_FILE = originalValue
      }
    })

    it('if voting functionality works then vote counts should increase correctly', async () => {
      const originalValue = config.USE_DATABASE_INSTEAD_OF_JSON_FILE
      config.USE_DATABASE_INSTEAD_OF_JSON_FILE = true
      
      try {
        renderWithProviders(<App />)
        await waitForAppToLoad()
        
        // Get prompts data and find first displayed one
        const prompts = JSON.parse(localStorage.getItem('apollo_prompts'))
        let testPromptId = null
        let initialVotes = null
        
        // Find first prompt that's actually displayed
        for (const prompt of prompts.slice(0, 10)) {
          try {
            const voteElement = screen.getByTestId(`prompt-votes-${prompt.id}`)
            testPromptId = prompt.id
            initialVotes = parseInt(voteElement.textContent)
            break
          } catch (e) {
            // Try next prompt
          }
        }
        
        expect(testPromptId).toBeTruthy()
        expect(initialVotes).toBeGreaterThan(0)
        
        // Click vote button for the found prompt
        const voteButton = screen.getByTestId(`vote-button-${testPromptId}`)
        fireEvent.click(voteButton)
        
        // Wait for vote to be processed and check new count
        await waitFor(() => {
          const newVoteElement = screen.getByTestId(`prompt-votes-${testPromptId}`)
          const newVotes = parseInt(newVoteElement.textContent)
          expect(newVotes).toBe(initialVotes + 1)
        })
        
      } finally {
        config.USE_DATABASE_INSTEAD_OF_JSON_FILE = originalValue
      }
    })

    it('if vote persistence works then votes should be stored in localStorage votes table', async () => {
      const originalValue = config.USE_DATABASE_INSTEAD_OF_JSON_FILE
      config.USE_DATABASE_INSTEAD_OF_JSON_FILE = true
      
      try {
        renderWithProviders(<App />)
        await waitForAppToLoad()
        
        // Get second available vote element (or first if only one)
        const voteElements = screen.getAllByTestId(/prompt-votes-\d+/)
        const testVoteElement = voteElements.length > 1 ? voteElements[1] : voteElements[0]
        const promptId = parseInt(testVoteElement.getAttribute('data-testid').match(/prompt-votes-(\d+)/)[1])
        
        // Click vote button
        const voteButton = screen.getByTestId(`vote-button-${promptId}`)
        fireEvent.click(voteButton)
        
        await waitFor(() => {
          // Check that vote is stored in localStorage
          const votes = JSON.parse(localStorage.getItem('apollo_votes'))
          const userVote = votes.find(v => v.promptId === promptId && v.userId === 1 && v.voteType === 'up')
          expect(userVote).toBeTruthy()
          expect(userVote).toHaveProperty('createdAt')
        })
        
      } finally {
        config.USE_DATABASE_INSTEAD_OF_JSON_FILE = originalValue
      }
    })

    it('if vote calculations work then browser database should correctly aggregate votes', async () => {
      const originalValue = config.USE_DATABASE_INSTEAD_OF_JSON_FILE
      config.USE_DATABASE_INSTEAD_OF_JSON_FILE = true
      
      try {
        // Initialize database
        const { initializeBrowserDatabase } = await import('../api/browserDatabase')
        initializeBrowserDatabase()
        
        // Add additional votes directly to localStorage
        const existingVotes = JSON.parse(localStorage.getItem('apollo_votes'))
        const additionalVotes = [
          { userId: 2, promptId: 1, voteType: 'up', createdAt: new Date().toISOString() },
          { userId: 3, promptId: 1, voteType: 'up', createdAt: new Date().toISOString() },
          { userId: 4, promptId: 1, voteType: 'up', createdAt: new Date().toISOString() }
        ]
        
        localStorage.setItem('apollo_votes', JSON.stringify([...existingVotes, ...additionalVotes]))
        
        // Get prompt with calculated votes
        const promptsWithVotes = browserDbHelpers.getPromptsWithFilters()
        const prompt1 = promptsWithVotes.find(p => p.id === 1)
        
        // Should have base votes (8) + test vote (1) + additional votes (3) = 12 total
        expect(prompt1.votes).toBe(12)
        
      } finally {
        config.USE_DATABASE_INSTEAD_OF_JSON_FILE = originalValue
      }
    })
  })

  describe('Status Alignment with Vote Counts', () => {
    it('if status alignment works then high vote counts should align with Approved status', async () => {
      const originalValue = config.USE_DATABASE_INSTEAD_OF_JSON_FILE
      config.USE_DATABASE_INSTEAD_OF_JSON_FILE = true
      
      try {
        // Create test data with high vote count and Approved status
        const testPrompts = [
          {
            id: 1,
            title: 'High Vote Approved Prompt',
            description: 'Test prompt',
            category: 'Test Category',
            tags: [],
            status: 'Approved',
            votes: 45, // High vote count
            author: 'Test Author',
            timeAgo: '1 hour ago',
            statusCount: 45,
            difficulty: 'Advanced',
            comments: 12,
            userVote: null,
            content: 'Test content',
            inputOutputExamples: []
          },
          {
            id: 2,
            title: 'Medium Vote Under Review Prompt',
            description: 'Test prompt',
            category: 'Test Category',
            tags: [],
            status: 'Under Review',
            votes: 23, // Medium vote count
            author: 'Test Author',
            timeAgo: '2 hours ago',
            statusCount: 23,
            difficulty: 'Intermediate',
            comments: 8,
            userVote: null,
            content: 'Test content',
            inputOutputExamples: []
          },
          {
            id: 3,
            title: 'Low Vote In Development Prompt',
            description: 'Test prompt',
            category: 'Test Category',
            tags: [],
            status: 'In Development',
            votes: 6, // Low vote count
            author: 'Test Author',
            timeAgo: '3 hours ago',
            statusCount: 6,
            difficulty: 'Beginner',
            comments: 2,
            userVote: null,
            content: 'Test content',
            inputOutputExamples: []
          }
        ]
        
        localStorage.setItem('apollo_prompts', JSON.stringify(testPrompts))
        
        renderWithProviders(<App />)
        await waitForAppToLoad()
        
        // Verify status badges are displayed correctly
        expect(screen.getByText(/Approved/)).toBeInTheDocument()
        expect(screen.getByText(/Under Review/)).toBeInTheDocument()
        expect(screen.getByText(/In Development/)).toBeInTheDocument()
        
        // Verify vote counts match status expectations
        expect(screen.getByTestId('prompt-votes-1')).toHaveTextContent('45')
        expect(screen.getByTestId('prompt-votes-2')).toHaveTextContent('23')
        expect(screen.getByTestId('prompt-votes-3')).toHaveTextContent('6')
        
      } finally {
        config.USE_DATABASE_INSTEAD_OF_JSON_FILE = originalValue
      }
    })

    it('if statusCount alignment works then statusCount should reflect vote numbers logically', async () => {
      const originalValue = config.USE_DATABASE_INSTEAD_OF_JSON_FILE
      config.USE_DATABASE_INSTEAD_OF_JSON_FILE = true
      
      try {
        renderWithProviders(<App />)
        await waitForAppToLoad()
        
        // Get prompts from localStorage to verify statusCount alignment
        const prompts = JSON.parse(localStorage.getItem('apollo_prompts'))
        
        prompts.forEach(prompt => {
          // StatusCount should be reasonable relative to votes
          // (not necessarily equal, but in reasonable proportion)
          expect(prompt.statusCount).toBeGreaterThan(0)
          expect(prompt.statusCount).toBeLessThanOrEqual(300) // Reasonable upper bound
          
          // For test data, statusCount should be aligned with expectations
          if (prompt.id === 1) expect(prompt.statusCount).toBe(45)
          if (prompt.id === 2) expect(prompt.statusCount).toBe(23)
          if (prompt.id === 3) expect(prompt.statusCount).toBe(67)
        })
        
      } finally {
        config.USE_DATABASE_INSTEAD_OF_JSON_FILE = originalValue
      }
    })
  })

  describe('Vote Persistence in Browser Database Mode', () => {
    it('if vote persistence works then votes should survive app restarts', async () => {
      const originalValue = config.USE_DATABASE_INSTEAD_OF_JSON_FILE
      config.USE_DATABASE_INSTEAD_OF_JSON_FILE = true
      
      try {
        // First session - cast a vote
        renderWithProviders(<App />)
        await waitForAppToLoad()
        
        const voteButton = screen.getByTestId('vote-button-1')
        fireEvent.click(voteButton)
        
        await waitFor(() => {
          expect(screen.getByTestId('prompt-votes-1')).toHaveTextContent('9') // 8 + 1
        })
        
        cleanup()
        
        // Second session - verify vote persists
        renderWithProviders(<App />)
        await waitForAppToLoad()
        
        expect(screen.getByTestId('prompt-votes-1')).toHaveTextContent('9')
        
        // Verify vote is still in localStorage
        const votes = JSON.parse(localStorage.getItem('apollo_votes'))
        const userVote = votes.find(v => v.promptId === 1 && v.userId === 1)
        expect(userVote).toBeTruthy()
        
      } finally {
        config.USE_DATABASE_INSTEAD_OF_JSON_FILE = originalValue
      }
    })

    it('if vote toggling works then users should be able to remove votes', async () => {
      const originalValue = config.USE_DATABASE_INSTEAD_OF_JSON_FILE
      config.USE_DATABASE_INSTEAD_OF_JSON_FILE = true
      
      try {
        renderWithProviders(<App />)
        await waitForAppToLoad()
        
        const voteButton = screen.getByTestId('vote-button-2')
        
        // First click - add vote
        fireEvent.click(voteButton)
        await waitFor(() => {
          expect(screen.getByTestId('prompt-votes-2')).toHaveTextContent('6') // 5 + 1
        })
        
        // Second click - remove vote
        fireEvent.click(voteButton)
        await waitFor(() => {
          expect(screen.getByTestId('prompt-votes-2')).toHaveTextContent('5') // Back to original
        })
        
        // Verify vote is removed from localStorage
        const votes = JSON.parse(localStorage.getItem('apollo_votes'))
        const userVote = votes.find(v => v.promptId === 2 && v.userId === 1)
        expect(userVote).toBeFalsy()
        
      } finally {
        config.USE_DATABASE_INSTEAD_OF_JSON_FILE = originalValue
      }
    })

    it('if multiple user votes work then different users should have independent votes', async () => {
      const originalValue = config.USE_DATABASE_INSTEAD_OF_JSON_FILE
      config.USE_DATABASE_INSTEAD_OF_JSON_FILE = true
      
      try {
        // Initialize database
        const { initializeBrowserDatabase } = await import('../api/browserDatabase')
        initializeBrowserDatabase()
        
        // Simulate votes from multiple users
        const multiUserVotes = [
          { userId: 1, promptId: 1, voteType: 'up', createdAt: new Date().toISOString() },
          { userId: 2, promptId: 1, voteType: 'up', createdAt: new Date().toISOString() },
          { userId: 3, promptId: 1, voteType: 'up', createdAt: new Date().toISOString() },
          { userId: 1, promptId: 2, voteType: 'up', createdAt: new Date().toISOString() },
          { userId: 2, promptId: 2, voteType: 'up', createdAt: new Date().toISOString() }
        ]
        
        // Add existing test vote
        const existingVotes = JSON.parse(localStorage.getItem('apollo_votes'))
        localStorage.setItem('apollo_votes', JSON.stringify([...existingVotes, ...multiUserVotes]))
        
        // Verify vote aggregation
        const prompts = browserDbHelpers.getPromptsWithFilters()
        
        const prompt1 = prompts.find(p => p.id === 1)
        const prompt2 = prompts.find(p => p.id === 2)
        
        // Prompt 1: 8 base + 1 existing test + 3 new = 12 total
        expect(prompt1.votes).toBe(12)
        
        // Prompt 2: 5 base + 2 new = 7 total
        expect(prompt2.votes).toBe(7)
        
      } finally {
        config.USE_DATABASE_INSTEAD_OF_JSON_FILE = originalValue
      }
    })
  })

  describe('Vote System Edge Cases', () => {
    it('if vote boundary testing works then votes should handle edge cases correctly', async () => {
      const originalValue = config.USE_DATABASE_INSTEAD_OF_JSON_FILE
      config.USE_DATABASE_INSTEAD_OF_JSON_FILE = true
      
      try {
        // Test with extreme vote counts
        const extremeVotePrompts = [
          {
            id: 1,
            title: 'Minimum Vote Prompt',
            description: 'Test prompt',
            category: 'Test',
            tags: [],
            status: 'In Development',
            votes: 5, // Minimum expected
            author: 'Test Author',
            timeAgo: '1 hour ago',
            statusCount: 5,
            difficulty: 'Beginner',
            comments: 1,
            userVote: null,
            content: 'Test content',
            inputOutputExamples: []
          },
          {
            id: 2,
            title: 'Maximum Vote Prompt',
            description: 'Test prompt',
            category: 'Test',
            tags: [],
            status: 'Approved',
            votes: 291, // Maximum expected
            author: 'Test Author',
            timeAgo: '1 day ago',
            statusCount: 291,
            difficulty: 'Advanced',
            comments: 75,
            userVote: null,
            content: 'Test content',
            inputOutputExamples: []
          }
        ]
        
        localStorage.setItem('apollo_prompts', JSON.stringify(extremeVotePrompts))
        
        renderWithProviders(<App />)
        await waitForAppToLoad()
        
        // Verify extreme values are handled correctly
        expect(screen.getByTestId('prompt-votes-1')).toHaveTextContent('5')
        expect(screen.getByTestId('prompt-votes-2')).toHaveTextContent('291')
        
        // UI should not break with extreme values
        expect(screen.getByText('Minimum Vote Prompt')).toBeInTheDocument()
        expect(screen.getByText('Maximum Vote Prompt')).toBeInTheDocument()
        
      } finally {
        config.USE_DATABASE_INSTEAD_OF_JSON_FILE = originalValue
      }
    })

    it('if vote data corruption handling works then invalid vote data should not break the app', async () => {
      const originalValue = config.USE_DATABASE_INSTEAD_OF_JSON_FILE
      config.USE_DATABASE_INSTEAD_OF_JSON_FILE = true
      
      try {
        // Initialize with corrupted vote data
        const corruptedVotes = [
          { userId: 1, promptId: 1, voteType: 'up', createdAt: 'invalid-date' },
          { userId: 'invalid', promptId: 2, voteType: 'up', createdAt: new Date().toISOString() },
          { userId: 3, promptId: 'invalid', voteType: 'down', createdAt: new Date().toISOString() },
          { userId: 4, promptId: 1, voteType: 'invalid', createdAt: new Date().toISOString() }
        ]
        
        localStorage.setItem('apollo_votes', JSON.stringify(corruptedVotes))
        
        const { initializeBrowserDatabase } = await import('../api/browserDatabase')
        initializeBrowserDatabase()
        
        // App should not crash
        expect(() => {
          renderWithProviders(<App />)
        }).not.toThrow()
        
        await waitForAppToLoad()
        
        // Should display default prompts despite vote corruption
        expect(screen.getByText('Investment Thesis Development')).toBeInTheDocument()
        
      } finally {
        config.USE_DATABASE_INSTEAD_OF_JSON_FILE = originalValue
      }
    })

    it('if vote helper functions work then browserDbHelpers should handle votes correctly', async () => {
      const originalValue = config.USE_DATABASE_INSTEAD_OF_JSON_FILE
      config.USE_DATABASE_INSTEAD_OF_JSON_FILE = true
      
      try {
        // Initialize database
        const { initializeBrowserDatabase } = await import('../api/browserDatabase')
        initializeBrowserDatabase()
        
        // Test vote creation
        browserDbHelpers.createVote(1, 1, 'up')
        
        // Test vote retrieval
        const userVote = browserDbHelpers.getUserVote(1, 1)
        expect(userVote).toBeTruthy()
        expect(userVote.voteType).toBe('up')
        
        // Test vote update
        browserDbHelpers.updateVote('down', 1, 1)
        const updatedVote = browserDbHelpers.getUserVote(1, 1)
        expect(updatedVote.voteType).toBe('down')
        
        // Test vote deletion
        browserDbHelpers.deleteVote(1, 1)
        const deletedVote = browserDbHelpers.getUserVote(1, 1)
        expect(deletedVote).toBeFalsy()
        
      } finally {
        config.USE_DATABASE_INSTEAD_OF_JSON_FILE = originalValue
      }
    })
  })
})