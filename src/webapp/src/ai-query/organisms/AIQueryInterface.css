/* Minimized state */
.ai-query-minimized {
  background: #282A2C;
  border: 1px solid rgba(255,255,255,0.1);
  border-radius: 24px;
  padding: 12px 20px;
  cursor: pointer;
  transition: all 0.2s ease;
  user-select: none;
}

.ai-query-minimized:hover {
  background: #333538;
  border-color: #007B63;
}

.minimized-content {
  display: flex;
  align-items: center;
  gap: 10px;
  color: #b0b0b0;
  font-size: 14px;
}

.ai-icon {
  color: #007B63;
  flex-shrink: 0;
}

/* Expanded state */
.ai-query-interface {
  display: flex;
  flex-direction: column;
  background: #1F2025;
  border: 1px solid rgba(255,255,255,0.1);
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s ease;
}

.ai-query-interface.expanded {
  height: 400px;
}

.chat-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  border-bottom: 1px solid rgba(255,255,255,0.1);
  background: #282A2C;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #e0e0e0;
  font-size: 14px;
  font-weight: 500;
}

.minimize-button {
  background: none;
  border: none;
  color: #808080;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.minimize-button:hover {
  background: rgba(255,255,255,0.1);
  color: #e0e0e0;
}

.chat-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.welcome-message {
  padding: 24px;
  text-align: center;
  color: #808080;
}

.welcome-message h3 {
  margin: 0 0 8px 0;
  color: #e0e0e0;
  font-size: 18px;
  font-weight: 600;
}

.welcome-message p {
  margin: 0;
  font-size: 14px;
  line-height: 1.5;
}

.messages-container {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
  scroll-behavior: smooth;
}

.chat-message {
  margin-bottom: 16px;
  display: flex;
  align-items: flex-start;
}

.chat-message.user {
  justify-content: flex-end;
}

.chat-message.ai {
  justify-content: flex-start;
}

.message-content {
  max-width: 70%;
  padding: 12px 16px;
  border-radius: 12px;
  word-wrap: break-word;
  font-size: 14px;
  line-height: 1.5;
}

/* Full width for narrow chat windows */
@media (max-width: 500px) {
  .message-content {
    max-width: 100%;
  }
}

/* Streaming animation */
.chat-message.streaming .message-content {
  position: relative;
}

.cursor {
  color: #007B63;
  font-weight: bold;
  animation: blink 1s infinite;
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0; }
}

.chat-message.user .message-content {
  background: #007B63;
  color: white;
  border-bottom-right-radius: 4px;
}

.chat-message.ai .message-content {
  background: #282A2C;
  color: #e0e0e0;
  border-bottom-left-radius: 4px;
}

.citation-link {
  background: none;
  border: none;
  color: #007B63;
  text-decoration: underline;
  cursor: pointer;
  font-size: inherit;
  font-family: inherit;
  padding: 0;
  margin: 0 2px;
  transition: color 0.2s ease;
}

.citation-link:hover {
  color: #00a085;
  text-decoration: none;
}

.loading-message {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 16px;
  color: #808080;
  font-size: 14px;
}

.input-container {
  border-top: 1px solid rgba(255,255,255,0.1);
  padding: 16px;
  background: #1F2025;
}

.input-row {
  display: flex;
  gap: 8px;
  align-items: center;
  margin-bottom: 8px;
}

.query-input {
  flex: 1;
  padding: 12px 16px;
  border: 1px solid rgba(255,255,255,0.1);
  border-radius: 8px;
  font-size: 14px;
  color: #e0e0e0;
  background: #282A2C;
  outline: none;
  transition: border-color 0.2s ease;
}

.query-input:focus {
  border-color: #007B63;
}

.query-input:disabled {
  background: #1a1a1a;
  color: #666;
  cursor: not-allowed;
}

.query-input::placeholder {
  color: #606060;
}

.send-button {
  padding: 12px;
  background: #007B63;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s ease;
  min-width: 44px;
}

.send-button:hover:not(:disabled) {
  background: #006854;
}

.send-button:disabled {
  background: #333;
  cursor: not-allowed;
}

.spinner {
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.footer-text {
  font-size: 12px;
  color: #606060;
  text-align: center;
  line-height: 1.4;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Scrollbar styling */
.messages-container::-webkit-scrollbar {
  width: 6px;
}

.messages-container::-webkit-scrollbar-track {
  background: #1F2025;
}

.messages-container::-webkit-scrollbar-thumb {
  background: #4a4a4a;
  border-radius: 3px;
}

.messages-container::-webkit-scrollbar-thumb:hover {
  background: #5a5a5a;
}