import { useState, useRef, useEffect } from 'react'
import './AIQueryInterface.css'

function AIQueryInterface({ prompts, categories, onCitationClick }) {
  const [query, setQuery] = useState('')
  const [messages, setMessages] = useState([])
  const [loading, setLoading] = useState(false)
  const [isExpanded, setIsExpanded] = useState(false)
  const containerRef = useRef(null)

  const handleSubmit = async () => {
    if (!query.trim() || loading) return

    const userMessage = { type: 'user', message: query }
    setMessages(prev => [...prev, userMessage])
    setQuery('')
    setLoading(true)

    // Simulate AI response for now
    setTimeout(() => {
      const aiMessage = { 
        type: 'ai', 
        message: `I found several prompts related to "${query}". Here are some relevant ones: [1] Investment Analysis Framework, [5] Due Diligence Checklist, and [12] Market Research Template. Click on any citation to view the full prompt.` 
      }
      setMessages(prev => [...prev, aiMessage])
      setLoading(false)
    }, 1000)
  }

  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSubmit()
    }
  }

  const handleCitationClick = (promptId) => {
    onCitationClick?.(promptId)
  }

  const handleExpand = () => {
    setIsExpanded(true)
  }

  const handleMinimize = () => {
    setIsExpanded(false)
  }

  // Close when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (containerRef.current && !containerRef.current.contains(event.target)) {
        setIsExpanded(false)
      }
    }

    if (isExpanded) {
      document.addEventListener('mousedown', handleClickOutside)
      return () => document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [isExpanded])

  const renderMessage = (msg, index) => {
    if (msg.type === 'user') {
      return (
        <div key={index} className="chat-message user">
          <div className="message-content">{msg.message}</div>
        </div>
      )
    }

    // Parse citations in AI messages
    const parts = msg.message.split(/(\[\d+\])/)
    
    return (
      <div key={index} className="chat-message ai">
        <div className="message-content">
          {parts.map((part, i) => {
            const citationMatch = part.match(/\[(\d+)\]/)
            if (citationMatch) {
              const promptId = parseInt(citationMatch[1])
              const prompt = prompts.find(p => p.id === promptId)
              return (
                <button 
                  key={i}
                  className="citation-link"
                  onClick={() => handleCitationClick(promptId)}
                >
                  [{prompt ? prompt.title : `Prompt ${promptId}`}]
                </button>
              )
            }
            return <span key={i}>{part}</span>
          })}
        </div>
      </div>
    )
  }

  if (!isExpanded) {
    return (
      <div className="ai-query-minimized" onClick={handleExpand}>
        <div className="minimized-content">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" className="ai-icon">
            <path d="M9 12l2 2 4-4" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            <path d="M21 12c0 4.97-4.03 9-9 9s-9-4.03-9-9 4.03-9 9-9 9 4.03 9 9z" stroke="currentColor" strokeWidth="2"/>
          </svg>
          <span>Ask me anything about the Apollo prompts library</span>
        </div>
      </div>
    )
  }

  return (
    <div className="ai-query-interface expanded" ref={containerRef}>
      <div className="chat-header">
        <div className="header-left">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" className="ai-icon">
            <path d="M9 12l2 2 4-4" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            <path d="M21 12c0 4.97-4.03 9-9 9s-9-4.03-9-9 4.03-9 9-9 9 4.03 9 9z" stroke="currentColor" strokeWidth="2"/>
          </svg>
          <span>Apollo AI Assistant</span>
        </div>
        <button className="minimize-button" onClick={handleMinimize}>
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
            <path d="M19 12H5" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
          </svg>
        </button>
      </div>

      <div className="chat-container">
        {messages.length === 0 && (
          <div className="welcome-message">
            <h3>Ask anything about Apollo prompts</h3>
            <p>I can help you find prompts, compare options, and answer questions about our investment analysis library.</p>
          </div>
        )}
        
        <div className="messages-container">
          {messages.map(renderMessage)}
          {loading && (
            <div className="loading-message">
              <div className="spinner"></div>
              <span>Thinking...</span>
            </div>
          )}
        </div>
      </div>

      <div className="input-container">
        <div className="input-row">
          <input
            type="text"
            className="query-input"
            value={query}
            onChange={(e) => setQuery(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder="E.g., Is there a prompt for making charts from excel files?"
            disabled={loading}
            autoFocus
          />
          <button 
            className="send-button"
            onClick={handleSubmit}
            disabled={!query.trim() || loading}
          >
            {loading ? (
              <div className="spinner" />
            ) : (
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                <path 
                  d="M22 2L11 13M22 2l-7 20-4-9-9-4 20-7z" 
                  stroke="currentColor" 
                  strokeWidth="2" 
                  strokeLinecap="round" 
                  strokeLinejoin="round"
                />
              </svg>
            )}
          </button>
        </div>
        <div className="footer-text">
          In accordance with Apollo's AI guidelines, assess and confirm the applicability, veracity, and accuracy of outputs.
        </div>
      </div>
    </div>
  )
}

export default AIQueryInterface