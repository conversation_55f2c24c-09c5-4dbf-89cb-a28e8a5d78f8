import { useState, useRef, useEffect } from 'react'
import './AIQueryInterface.css'
import config from '../../config.js'
import StreamingAIQueryService from '../services/StreamingAIQueryService.js'
import PromptContextBuilder from '../utils/PromptContextBuilder.js'

function StreamingAIQueryInterface({ 
  prompts, 
  categories, 
  onCitationClick,
  messages,
  setMessages,
  query,
  setQuery,
  loading,
  setLoading,
  isExpanded,
  setIsExpanded
}) {
  const containerRef = useRef(null)
  const [streamingMessageId, setStreamingMessageId] = useState(null)

  const handleSubmit = async () => {
    if (!query.trim() || loading) return

    const userMessage = { type: 'user', message: query, id: Date.now() }
    setMessages(prev => [...prev, userMessage])
    const currentQuery = query
    setQuery('')
    setLoading(true)

    // Create placeholder for streaming response
    const streamingId = Date.now() + 1
    setStreamingMessageId(streamingId)
    const streamingMessage = { 
      type: 'ai', 
      message: '', 
      id: streamingId,
      isStreaming: true 
    }
    setMessages(prev => [...prev, streamingMessage])

    try {
      if (config.USE_OPENAI) {
        // Use real OpenAI streaming
        if (config.DEBUG) {
          console.log('🌊 Using OpenAI streaming for query:', currentQuery);
        }
        
        const context = PromptContextBuilder.buildContext(prompts, categories)
        const conversationHistory = messages.map(msg => ({
          role: msg.type === 'user' ? 'user' : 'assistant',
          content: msg.message
        }))

        const result = await StreamingAIQueryService.queryPromptsStreaming(
          currentQuery, 
          context, 
          conversationHistory,
          (chunk) => {
            // Update the streaming message with new content
            setMessages(prev => prev.map(msg => 
              msg.id === streamingId 
                ? { ...msg, message: chunk.fullResponse, isStreaming: !chunk.isComplete }
                : msg
            ))
          }
        )

        if (!result.success) {
          throw new Error(result.error)
        }
      } else {
        // Simulate streaming response
        if (config.DEBUG) {
          console.log('🎭 Using simulated streaming for query:', currentQuery);
        }
        await simulatedStreamingResponse(currentQuery, streamingId)
      }
    } catch (error) {
      console.error('Streaming AI Query Error:', error)
      
      // Replace streaming message with error
      setMessages(prev => prev.map(msg => 
        msg.id === streamingId 
          ? { 
              ...msg, 
              message: `I apologize, but I encountered an error: ${error.message}. Please try again.`,
              isStreaming: false
            }
          : msg
      ))
    } finally {
      setLoading(false)
      setStreamingMessageId(null)
    }
  }

  const simulatedStreamingResponse = async (currentQuery, streamingId) => {
    // Generate simulated response
    const relevantPrompts = prompts.filter(p => 
      p.title.toLowerCase().includes(currentQuery.toLowerCase()) ||
      p.description.toLowerCase().includes(currentQuery.toLowerCase()) ||
      (p.tags && p.tags.some(tag => tag.toLowerCase().includes(currentQuery.toLowerCase())))
    ).slice(0, 3)

    let responseMessage = `I found ${relevantPrompts.length > 0 ? relevantPrompts.length : 'several'} prompts related to "${currentQuery}". Here are the most relevant ones:\n\n`
    
    if (relevantPrompts.length > 0) {
      relevantPrompts.forEach(prompt => {
        const queryLower = currentQuery.toLowerCase()
        const titleMatch = prompt.title.toLowerCase().includes(queryLower)
        const descMatch = prompt.description.toLowerCase().includes(queryLower)
        const tagMatch = prompt.tags && prompt.tags.some(tag => tag.toLowerCase().includes(queryLower))
        
        let relevanceReason = ""
        if (titleMatch) {
          relevanceReason = `title matches "${currentQuery}"`
        } else if (tagMatch) {
          const matchingTags = prompt.tags.filter(tag => tag.toLowerCase().includes(queryLower))
          relevanceReason = `tagged with ${matchingTags.join(', ')}`
        } else if (descMatch) {
          relevanceReason = `description mentions "${currentQuery}"`
        } else {
          relevanceReason = `related to your query`
        }
        
        responseMessage += `[${prompt.id}] **${prompt.title}** (${prompt.difficulty}) - ${prompt.description.substring(0, 100)}...\n\n*Relevant because: ${relevanceReason}*\n\n`
      })
    }
    
    responseMessage += `Click on any citation to view the full prompt details.`

    // Use the streaming service to simulate word-by-word delivery
    await StreamingAIQueryService.simulateStreaming(responseMessage, (chunk) => {
      setMessages(prev => prev.map(msg => 
        msg.id === streamingId 
          ? { ...msg, message: chunk.fullResponse, isStreaming: !chunk.isComplete }
          : msg
      ))
    })
  }

  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSubmit()
    }
  }

  const handleCitationClick = (promptId) => {
    onCitationClick?.(promptId)
  }

  const handleExpand = () => {
    setIsExpanded(true)
  }

  const handleMinimize = () => {
    setIsExpanded(false)
  }

  // Close when clicking outside or pressing Esc
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (containerRef.current && !containerRef.current.contains(event.target)) {
        setIsExpanded(false)
      }
    }

    const handleKeyPress = (event) => {
      if (event.key === 'Escape' && isExpanded) {
        event.preventDefault()
        setIsExpanded(false)
      }
    }

    if (isExpanded) {
      document.addEventListener('mousedown', handleClickOutside)
      document.addEventListener('keydown', handleKeyPress)
      return () => {
        document.removeEventListener('mousedown', handleClickOutside)
        document.removeEventListener('keydown', handleKeyPress)
      }
    }
  }, [isExpanded, setIsExpanded])

  const renderMessage = (msg, index) => {
    if (msg.type === 'user') {
      return (
        <div key={msg.id || index} className="chat-message user">
          <div className="message-content">{msg.message}</div>
        </div>
      )
    }

    // Parse citations and markdown-style formatting in AI messages
    const formatMessage = (text) => {
      const parts = text.split(/(\[\d+\])/)
      
      return parts.map((part, i) => {
        const citationMatch = part.match(/\[(\d+)\]/)
        if (citationMatch) {
          const promptId = parseInt(citationMatch[1])
          const prompt = prompts.find(p => p.id === promptId)
          return (
            <button 
              key={i}
              className="citation-link"
              onClick={() => handleCitationClick(promptId)}
            >
[{promptId}]
            </button>
          )
        }
        
        // Handle **bold** and *italic* text
        const boldParts = part.split(/(\*\*[^*]+\*\*)/)
        return boldParts.map((boldPart, j) => {
          if (boldPart.startsWith('**') && boldPart.endsWith('**')) {
            return <strong key={`${i}-${j}`}>{boldPart.slice(2, -2)}</strong>
          }
          
          const italicParts = boldPart.split(/(\*[^*]+\*)/)
          return italicParts.map((italicPart, k) => {
            if (italicPart.startsWith('*') && italicPart.endsWith('*') && !italicPart.startsWith('**')) {
              return <em key={`${i}-${j}-${k}`}>{italicPart.slice(1, -1)}</em>
            }
            
            return italicPart.split('\n').map((line, l) => (
              <span key={`${i}-${j}-${k}-${l}`}>
                {line}
                {l < italicPart.split('\n').length - 1 && <br />}
              </span>
            ))
          })
        })
      })
    }
    
    return (
      <div key={msg.id || index} className={`chat-message ai ${msg.isStreaming ? 'streaming' : ''}`}>
        <div className="message-content">
          {formatMessage(msg.message)}
          {msg.isStreaming && <span className="cursor">▋</span>}
        </div>
      </div>
    )
  }

  if (!isExpanded) {
    return (
      <div className="ai-query-minimized" onClick={handleExpand}>
        <div className="minimized-content">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" className="ai-icon">
            <path d="M9 12l2 2 4-4" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            <path d="M21 12c0 4.97-4.03 9-9 9s-9-4.03-9-9 4.03-9 9-9 9 4.03 9 9z" stroke="currentColor" strokeWidth="2"/>
          </svg>
          <span>Ask me anything about the Apollo prompts library</span>
        </div>
      </div>
    )
  }

  return (
    <div className="ai-query-interface expanded" ref={containerRef}>
      <div className="chat-header">
        <div className="header-left">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" className="ai-icon">
            <path d="M9 12l2 2 4-4" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            <path d="M21 12c0 4.97-4.03 9-9 9s-9-4.03-9-9 4.03-9 9-9 9 4.03 9 9z" stroke="currentColor" strokeWidth="2"/>
          </svg>
          <span>Apollo AI Assistant</span>
        </div>
        <button className="minimize-button" onClick={handleMinimize}>
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
            <path d="M19 12H5" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
          </svg>
        </button>
      </div>

      <div className="chat-container">
        {messages.length === 0 && (
          <div className="welcome-message">
            <h3>Ask anything about Apollo prompts</h3>
            <p>I can help you find prompts, compare options, and answer questions about our investment analysis library.</p>
          </div>
        )}
        
        <div className="messages-container">
          {messages.map(renderMessage)}
          {loading && !streamingMessageId && (
            <div className="loading-message">
              <div className="spinner"></div>
              <span>Thinking...</span>
            </div>
          )}
        </div>
      </div>

      <div className="input-container">
        <div className="input-row">
          <input
            type="text"
            className="query-input"
            value={query}
            onChange={(e) => setQuery(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder="E.g., Is there a prompt for making charts from excel files?"
            disabled={loading}
            autoFocus
          />
          <button 
            className="send-button"
            onClick={handleSubmit}
            disabled={!query.trim() || loading}
          >
            {loading ? (
              <div className="spinner" />
            ) : (
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                <path 
                  d="M22 2L11 13M22 2l-7 20-4-9-9-4 20-7z" 
                  stroke="currentColor" 
                  strokeWidth="2" 
                  strokeLinecap="round" 
                  strokeLinejoin="round"
                />
              </svg>
            )}
          </button>
        </div>
        <div className="footer-text">
          In accordance with Apollo's AI guidelines, assess and confirm the applicability, veracity, and accuracy of outputs.
        </div>
      </div>
    </div>
  )
}

export default StreamingAIQueryInterface