import { useState, useRef, useEffect } from 'react'
import './AIQueryInterface.css'
import config from '../../config.js'
import AIQueryService from '../services/AIQueryService.js'
import PromptContextBuilder from '../utils/PromptContextBuilder.js'

function SharedAIQueryInterface({ 
  prompts, 
  categories, 
  onCitationClick,
  messages,
  setMessages,
  query,
  setQuery,
  loading,
  setLoading,
  isExpanded,
  setIsExpanded
}) {
  const containerRef = useRef(null)

  const handleSubmit = async () => {
    if (!query.trim() || loading) return

    const userMessage = { type: 'user', message: query }
    setMessages(prev => [...prev, userMessage])
    const currentQuery = query
    setQuery('')
    setLoading(true)

    try {
      if (config.USE_OPENAI) {
        // Use real OpenAI API
        if (config.DEBUG) {
          console.log('🤖 Using OpenAI API for query:', currentQuery);
          console.log('🔑 API Key available:', !!import.meta.env.VITE_OPENAI_API_KEY);
        }
        
        const context = PromptContextBuilder.buildContext(prompts, categories)
        const conversationHistory = messages.map(msg => ({
          role: msg.type === 'user' ? 'user' : 'assistant',
          content: msg.message
        }))

        const result = await AIQueryService.queryPrompts(currentQuery, context, conversationHistory)

        if (result.success) {
          const aiMessage = { type: 'ai', message: result.response }
          setMessages(prev => [...prev, aiMessage])
          
          if (config.DEBUG) {
            console.log('✓ OpenAI response received:', result.usage);
          }
        } else {
          // Fallback to simulation if OpenAI fails
          console.warn('OpenAI failed, falling back to simulation:', result.error);
          await simulatedResponse(currentQuery)
        }
      } else {
        // Fallback to simulated response
        if (config.DEBUG) {
          console.log('🎭 Using simulated response for query:', currentQuery);
        }
        await simulatedResponse(currentQuery)
      }
    } catch (error) {
      console.error('AI Query Error:', error)
      const errorMessage = { 
        type: 'ai', 
        message: `I apologize, but I encountered an error processing your request: ${error.message}. Please try again or check your connection.` 
      }
      setMessages(prev => [...prev, errorMessage])
    } finally {
      setLoading(false)
    }
  }

  const simulatedResponse = async (currentQuery) => {
    return new Promise((resolve) => {
      setTimeout(() => {
        const relevantPrompts = prompts.filter(p => 
          p.title.toLowerCase().includes(currentQuery.toLowerCase()) ||
          p.description.toLowerCase().includes(currentQuery.toLowerCase()) ||
          (p.tags && p.tags.some(tag => tag.toLowerCase().includes(currentQuery.toLowerCase())))
        ).slice(0, 3)

        let responseMessage = `I found ${relevantPrompts.length > 0 ? relevantPrompts.length : 'several'} prompts related to "${currentQuery}". Here are the most relevant ones:\n\n`
        
        if (relevantPrompts.length > 0) {
          relevantPrompts.forEach(prompt => {
            // Determine why it's relevant
            let relevanceReason = ""
            const queryLower = currentQuery.toLowerCase()
            const titleMatch = prompt.title.toLowerCase().includes(queryLower)
            const descMatch = prompt.description.toLowerCase().includes(queryLower)
            const tagMatch = prompt.tags && prompt.tags.some(tag => tag.toLowerCase().includes(queryLower))
            
            if (titleMatch) {
              relevanceReason = `title matches "${currentQuery}"`
            } else if (tagMatch) {
              const matchingTags = prompt.tags.filter(tag => tag.toLowerCase().includes(queryLower))
              relevanceReason = `tagged with ${matchingTags.join(', ')}`
            } else if (descMatch) {
              relevanceReason = `description mentions "${currentQuery}"`
            } else {
              relevanceReason = `related to your query`
            }
            
            responseMessage += `[${prompt.id}] **${prompt.title}** (${prompt.difficulty}) - ${prompt.description.substring(0, 100)}...\n\n*Relevant because: ${relevanceReason}*\n\n`
          })
        } else {
          // Fallback with actual prompt data if available
          const fallbackPrompts = prompts.slice(0, 3)
          if (fallbackPrompts.length > 0) {
            fallbackPrompts.forEach(prompt => {
              responseMessage += `[${prompt.id}] **${prompt.title}** (${prompt.difficulty}) - ${prompt.description.substring(0, 100)}...\n\n*Relevant because: general investment analysis prompt*\n\n`
            })
          } else {
            responseMessage += `[1] **Investment Analysis Framework** (Advanced) - Comprehensive framework for evaluating investment opportunities...\n\n*Relevant because: foundational investment analysis*\n\n`
          }
        }
        
        responseMessage += `Click on any citation to view the full prompt details. Each prompt includes detailed instructions, example outputs, and difficulty ratings.`

        const aiMessage = { 
          type: 'ai', 
          message: responseMessage
        }
        setMessages(prev => [...prev, aiMessage])
        resolve()
      }, 1000)
    })
  }

  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSubmit()
    }
  }

  const handleCitationClick = (promptId) => {
    onCitationClick?.(promptId)
  }

  const handleExpand = () => {
    setIsExpanded(true)
  }

  const handleMinimize = () => {
    setIsExpanded(false)
  }

  // Close when clicking outside or pressing Esc
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (containerRef.current && !containerRef.current.contains(event.target)) {
        setIsExpanded(false)
      }
    }

    const handleKeyPress = (event) => {
      if (event.key === 'Escape' && isExpanded) {
        event.preventDefault()
        setIsExpanded(false)
      }
    }

    if (isExpanded) {
      document.addEventListener('mousedown', handleClickOutside)
      document.addEventListener('keydown', handleKeyPress)
      return () => {
        document.removeEventListener('mousedown', handleClickOutside)
        document.removeEventListener('keydown', handleKeyPress)
      }
    }
  }, [isExpanded, setIsExpanded])

  const renderMessage = (msg, index) => {
    if (msg.type === 'user') {
      return (
        <div key={index} className="chat-message user">
          <div className="message-content">{msg.message}</div>
        </div>
      )
    }

    // Parse citations and markdown-style formatting in AI messages
    const formatMessage = (text) => {
      // Split by citations first
      const parts = text.split(/(\[\d+\])/)
      
      return parts.map((part, i) => {
        const citationMatch = part.match(/\[(\d+)\]/)
        if (citationMatch) {
          const promptId = parseInt(citationMatch[1])
          const prompt = prompts.find(p => p.id === promptId)
          return (
            <button 
              key={i}
              className="citation-link"
              onClick={() => handleCitationClick(promptId)}
            >
              [{prompt ? prompt.title : `Prompt ${promptId}`}]
            </button>
          )
        }
        
        // Handle **bold** and *italic* text
        const boldParts = part.split(/(\*\*[^*]+\*\*)/)
        return boldParts.map((boldPart, j) => {
          if (boldPart.startsWith('**') && boldPart.endsWith('**')) {
            return <strong key={`${i}-${j}`}>{boldPart.slice(2, -2)}</strong>
          }
          
          // Handle *italic* text
          const italicParts = boldPart.split(/(\*[^*]+\*)/)
          return italicParts.map((italicPart, k) => {
            if (italicPart.startsWith('*') && italicPart.endsWith('*') && !italicPart.startsWith('**')) {
              return <em key={`${i}-${j}-${k}`}>{italicPart.slice(1, -1)}</em>
            }
            
            // Handle line breaks
            return italicPart.split('\n').map((line, l) => (
              <span key={`${i}-${j}-${k}-${l}`}>
                {line}
                {l < italicPart.split('\n').length - 1 && <br />}
              </span>
            ))
          })
        })
      })
    }
    
    return (
      <div key={index} className="chat-message ai">
        <div className="message-content">
          {formatMessage(msg.message)}
        </div>
      </div>
    )
  }

  if (!isExpanded) {
    return (
      <div className="ai-query-minimized" onClick={handleExpand}>
        <div className="minimized-content">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" className="ai-icon">
            <path d="M9 12l2 2 4-4" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            <path d="M21 12c0 4.97-4.03 9-9 9s-9-4.03-9-9 4.03-9 9-9 9 4.03 9 9z" stroke="currentColor" strokeWidth="2"/>
          </svg>
          <span>Ask me anything about the Apollo prompts library</span>
        </div>
      </div>
    )
  }

  return (
    <div className="ai-query-interface expanded" ref={containerRef}>
      <div className="chat-header">
        <div className="header-left">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" className="ai-icon">
            <path d="M9 12l2 2 4-4" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            <path d="M21 12c0 4.97-4.03 9-9 9s-9-4.03-9-9 4.03-9 9-9 9 4.03 9 9z" stroke="currentColor" strokeWidth="2"/>
          </svg>
          <span>Apollo AI Assistant</span>
        </div>
        <button className="minimize-button" onClick={handleMinimize}>
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
            <path d="M19 12H5" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
          </svg>
        </button>
      </div>

      <div className="chat-container">
        {messages.length === 0 && (
          <div className="welcome-message">
            <h3>Ask anything about Apollo prompts</h3>
            <p>I can help you find prompts, compare options, and answer questions about our investment analysis library.</p>
          </div>
        )}
        
        <div className="messages-container">
          {messages.map(renderMessage)}
          {loading && (
            <div className="loading-message">
              <div className="spinner"></div>
              <span>Thinking...</span>
            </div>
          )}
        </div>
      </div>

      <div className="input-container">
        <div className="input-row">
          <input
            type="text"
            className="query-input"
            value={query}
            onChange={(e) => setQuery(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder="E.g., Is there a prompt for making charts from excel files?"
            disabled={loading}
            autoFocus
          />
          <button 
            className="send-button"
            onClick={handleSubmit}
            disabled={!query.trim() || loading}
          >
            {loading ? (
              <div className="spinner" />
            ) : (
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                <path 
                  d="M22 2L11 13M22 2l-7 20-4-9-9-4 20-7z" 
                  stroke="currentColor" 
                  strokeWidth="2" 
                  strokeLinecap="round" 
                  strokeLinejoin="round"
                />
              </svg>
            )}
          </button>
        </div>
        <div className="footer-text">
          In accordance with Apollo's AI guidelines, assess and confirm the applicability, veracity, and accuracy of outputs.
        </div>
      </div>
    </div>
  )
}

export default SharedAIQueryInterface