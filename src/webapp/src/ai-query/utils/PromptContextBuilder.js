class PromptContextBuilder {
  static buildContext(prompts, categories = []) {
    const contextData = {
      total_prompts: prompts.length,
      categories: this.getCategoryStats(prompts, categories),
      prompts: prompts.map(prompt => ({
        id: prompt.id,
        title: prompt.title,
        description: prompt.description,
        content: prompt.content ? prompt.content.substring(0, 500) + (prompt.content.length > 500 ? '...' : '') : '',
        category: prompt.category,
        tags: prompt.tags || [],
        status: prompt.status,
        difficulty: prompt.difficulty,
        votes: prompt.votes || 0,
        author: prompt.author,
        created_at: prompt.created_at || prompt.timeAgo
      }))
    };

    return contextData;
  }

  static getCategoryStats(prompts, categories) {
    const stats = {};
    
    categories.forEach(cat => {
      const categoryPrompts = prompts.filter(p => p.category === cat.name);
      stats[cat.name] = {
        count: categoryPrompts.length,
        avg_votes: categoryPrompts.reduce((sum, p) => sum + (p.votes || 0), 0) / categoryPrompts.length || 0,
        difficulties: this.getDifficultyBreakdown(categoryPrompts)
      };
    });

    return stats;
  }

  static getDifficultyBreakdown(prompts) {
    return prompts.reduce((acc, prompt) => {
      const diff = prompt.difficulty || 'Intermediate';
      acc[diff] = (acc[diff] || 0) + 1;
      return acc;
    }, {});
  }

  static buildSearchContext(prompts, searchTerm = '', selectedCategory = '', selectedStatus = '') {
    let filteredPrompts = prompts;

    if (searchTerm) {
      filteredPrompts = filteredPrompts.filter(p => 
        p.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        p.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (p.tags && p.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase())))
      );
    }

    if (selectedCategory) {
      filteredPrompts = filteredPrompts.filter(p => p.category === selectedCategory);
    }

    if (selectedStatus) {
      filteredPrompts = filteredPrompts.filter(p => p.status === selectedStatus);
    }

    return {
      filtered_results: filteredPrompts.length,
      current_filters: {
        search: searchTerm,
        category: selectedCategory,
        status: selectedStatus
      },
      prompts: filteredPrompts.map(prompt => ({
        id: prompt.id,
        title: prompt.title,
        description: prompt.description,
        category: prompt.category,
        status: prompt.status,
        votes: prompt.votes || 0
      }))
    };
  }
}

export default PromptContextBuilder;