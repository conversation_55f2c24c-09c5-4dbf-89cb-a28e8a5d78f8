import OpenAI from 'openai';
import config from '../../config.js';

class AIQueryService {
  constructor() {
    const apiKey = import.meta.env.VITE_OPENAI_API_KEY;
    
    // Debug environment variables
    if (config.DEBUG) {
      console.log('🔍 Environment check:', {
        'import.meta.env.MODE': import.meta.env.MODE,
        'import.meta.env.DEV': import.meta.env.DEV,
        'API key present': !!apiKey,
        'API key length': apiKey ? apiKey.length : 0,
        'All VITE vars': Object.keys(import.meta.env).filter(key => key.startsWith('VITE_'))
      });
    }
    
    if (!apiKey) {
      console.warn('⚠️ VITE_OPENAI_API_KEY not found. AI queries will use simulation mode.');
      console.log('Available env vars:', Object.keys(import.meta.env));
      this.openai = null;
    } else {
      this.openai = new OpenAI({
        apiKey: api<PERSON><PERSON>,
        dangerouslyAllowBrowser: true
      });
      
      if (config.DEBUG) {
        console.log('✓ OpenAI service initialized with API key');
      }
    }
    
    this.systemPrompt = `You are an AI copilot assistant for Apollo Global Management's prompt library. 
You have access to the complete database of investment analysis prompts below. You must not converse with user about anything other than the prompt library although you may ask clarifying questions or be creative helping them craft their own prompts / using prompts for the database as a starting point.

CRITICAL: When referencing prompts, you MUST use this exact citation format: [number] where number is the prompt's id field. 
This
Examples: [1], [5], [12] - these will become clickable links to the actual prompts.

When answering questions about prompts:
- Provide accurate information from the provided prompt data only
- Include specific citations using [id] format (NOT [prompt_id: x]) for any prompts you reference
- Give relevant recommendations based on user needs
- Use a professional tone appropriate for investment professionals
- If asked about prompts that don't exist, say so clearly
- When suggesting multiple prompts, explain why each is relevant
- Format responses clearly with **bold titles**, difficulty levels, and descriptions
- Always explain why each prompt is relevant to the user's query
- Use the exact id from the prompts database for citations

Available prompts database:
{PROMPT_CONTEXT}

# EXAMPLE OUTPUT

User: "Is there a prompt for making charts from excel files?"

Response: "I found several prompts that can help with Excel data visualization and chart creation:

[5] **Financial Dashboard Creation** (Intermediate) - Create comprehensive financial dashboards with charts and visualizations from raw data.

*Relevant because: involves creating charts and visual representations from financial data, typically sourced from Excel files*

[12] **Data Visualization Framework** (Beginner) - Build clear, professional charts and graphs to communicate investment insights effectively.

*Relevant because: specifically focuses on chart creation and data visualization techniques*

[8] **Portfolio Performance Reporting** (Advanced) - Generate detailed performance reports with charts, tables, and visual analytics.

*Relevant because: includes creating visual reports and charts from portfolio data typically managed in Excel*

Click on any citation to view the full prompt details."`;`
  }

  async queryPrompts(userQuestion, promptContext, conversationHistory = []) {
    try {
      if (!this.openai) {
        throw new Error('OpenAI client not initialized - API key missing');
      }

      if (config.DEBUG) {
        console.log('OpenAI Query:', { userQuestion, promptContext: Object.keys(promptContext) });
      }

      const systemPromptWithContext = this.systemPrompt.replace('{PROMPT_CONTEXT}', 
        JSON.stringify(promptContext, null, 2));

      const messages = [
        { role: 'system', content: systemPromptWithContext },
        ...conversationHistory,
        { role: 'user', content: userQuestion }
      ];

      const response = await this.openai.chat.completions.create({
        model: config.OPENAI_MODEL,
        messages: messages,
        temperature: config.OPENAI_TEMPERATURE,
        max_tokens: config.OPENAI_MAX_TOKENS
      });

      const result = {
        success: true,
        response: response.choices[0].message.content,
        usage: response.usage
      };

      if (config.DEBUG) {
        console.log('OpenAI Response:', result);
      }

      return result;
    } catch (error) {
      console.error('AI Query Error:', error);
      return {
        success: false,
        error: error.message || 'Failed to process query'
      };
    }
  }

  extractCitations(responseText) {
    const citationRegex = /\[(\d+)\]/g;
    const citations = [];
    let match;

    while ((match = citationRegex.exec(responseText)) !== null) {
      citations.push(parseInt(match[1]));
    }

    return [...new Set(citations)]; // Remove duplicates
  }
}

export default new AIQueryService();