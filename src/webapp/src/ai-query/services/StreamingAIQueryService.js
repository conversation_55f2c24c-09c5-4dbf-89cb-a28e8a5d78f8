import OpenAI from 'openai';
import config from '../../config.js';

class StreamingAIQueryService {
  constructor() {
    const apiKey = import.meta.env.VITE_OPENAI_API_KEY;
    
    if (!apiKey) {
      console.warn('⚠️ VITE_OPENAI_API_KEY not found. Streaming AI queries will use simulation mode.');
      this.openai = null;
    } else {
      this.openai = new OpenAI({
        apiKey: apiKey,
        dangerouslyAllowBrowser: true
      });
      
      if (config.DEBUG) {
        console.log('✓ Streaming OpenAI service initialized');
      }
    }
    
    this.systemPrompt = `You are an AI assistant for Apollo Global Management's prompt library. 
You have access to the complete database of investment analysis prompts below.

When answering questions about prompts:
- Provide accurate information from the provided prompt data
- Include specific citations using [prompt_id] format for any prompts you reference
- Give relevant recommendations based on user needs
- Use a professional tone appropriate for investment professionals
- If asked about prompts that don't exist, say so clearly
- When suggesting multiple prompts, explain why each is relevant
- Format responses clearly with titles, difficulty levels, and descriptions
- Always explain why each prompt is relevant to the user's query

Available prompts database:
{PROMPT_CONTEXT}`;
  }

  async queryPromptsStreaming(userQuestion, promptContext, conversationHistory = [], onChunk) {
    try {
      if (!this.openai) {
        throw new Error('OpenAI client not initialized - API key missing');
      }

      if (config.DEBUG) {
        console.log('🌊 Starting streaming OpenAI query:', userQuestion);
      }

      const systemPromptWithContext = this.systemPrompt.replace('{PROMPT_CONTEXT}', 
        JSON.stringify(promptContext, null, 2));

      const messages = [
        { role: 'system', content: systemPromptWithContext },
        ...conversationHistory,
        { role: 'user', content: userQuestion }
      ];

      const stream = await this.openai.chat.completions.create({
        model: config.OPENAI_MODEL,
        messages: messages,
        temperature: config.OPENAI_TEMPERATURE,
        max_tokens: config.OPENAI_MAX_TOKENS,
        stream: true
      });

      let fullResponse = '';
      let tokenCount = 0;

      for await (const chunk of stream) {
        const content = chunk.choices[0]?.delta?.content || '';
        if (content) {
          fullResponse += content;
          tokenCount++;
          
          // Call the chunk handler with incremental content
          onChunk({
            content: content,
            fullResponse: fullResponse,
            isComplete: false,
            tokenCount: tokenCount
          });
        }
      }

      // Final chunk to indicate completion
      onChunk({
        content: '',
        fullResponse: fullResponse,
        isComplete: true,
        tokenCount: tokenCount
      });

      if (config.DEBUG) {
        console.log('✓ Streaming response completed. Total tokens:', tokenCount);
      }

      return {
        success: true,
        response: fullResponse,
        tokenCount: tokenCount
      };

    } catch (error) {
      console.error('Streaming AI Query Error:', error);
      return {
        success: false,
        error: error.message || 'Failed to process streaming query'
      };
    }
  }

  async simulateStreaming(response, onChunk) {
    // Simulate streaming by sending words gradually
    const words = response.split(' ');
    let accumulated = '';
    
    for (let i = 0; i < words.length; i++) {
      accumulated += (i > 0 ? ' ' : '') + words[i];
      
      onChunk({
        content: (i > 0 ? ' ' : '') + words[i],
        fullResponse: accumulated,
        isComplete: i === words.length - 1,
        tokenCount: i + 1
      });
      
      // Simulate realistic typing speed - faster than before for better UX
      await new Promise(resolve => setTimeout(resolve, 30 + Math.random() * 40));
    }
  }
}

export default new StreamingAIQueryService();