import React, { useState } from 'react';
import { useAuth } from './AuthContext';
import { Loading } from '../components/atoms';
import './LoginModal.css';

const LoginModal = ({ isOpen, onClose }) => {
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  const { login } = useAuth();

  const mockUsers = [
    { username: 'alexf', name: '<PERSON>', role: 'Admin' },
    { username: 'peteam', name: 'PE Team', role: 'User' },
    { username: 'quantteam', name: 'Quantitative Team', role: 'User' },
    { username: 'research', name: 'Research Team', role: 'User' }
  ];

  const handleQuickLogin = async (username) => {
    try {
      setLoading(true);
      setError('');
      await login(username);
      onClose();
    } catch (error) {
      setError('<PERSON><PERSON> failed. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="login-overlay">
      <div className="login-modal">
        <div className="login-header">
          <div className="apollo-logo">
            <svg viewBox="0 0 100 100" className="logo-svg">
              <text x="50" y="75" textAnchor="middle" fontSize="80" fontFamily="Adobe Garamond Pro, Georgia, Times, serif" fontWeight="400">A</text>
            </svg>
          </div>
          <h2>Apollo Prompt Library</h2>
        </div>

        <div className="login-content">
          {error && <div className="error-message">{error}</div>}
          
          {loading && (
            <div className="login-loading-overlay">
              <Loading 
                variant="spinner" 
                size="md" 
                color="primary" 
                text="Signing you in..." 
              />
            </div>
          )}
          
          <div className={`user-list ${loading ? 'loading' : ''}`}>
            {mockUsers.map(user => (
              <button
                key={user.username}
                className="user-card"
                onClick={() => handleQuickLogin(user.username)}
                disabled={loading}
              >
                <div className="user-info">
                  <div className="user-name">{user.name}</div>
                  <div className="user-role">{user.role}</div>
                </div>
              </button>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default LoginModal;