.login-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.login-modal {
  background: #000000;
  border: 1px solid #333;
  border-radius: 8px;
  padding: 32px;
  width: 100%;
  max-width: 320px;
  text-align: center;
}

.login-header {
  margin-bottom: 32px;
}

.apollo-logo {
  width: 60px;
  height: 60px;
  margin: 0 auto 16px;
}

.logo-svg {
  width: 100%;
  height: 100%;
  fill: #007B63;
}

.login-modal h2 {
  color: #e0e0e0;
  font-size: 18px;
  font-weight: 500;
  margin: 0;
  font-family: 'Segoe UI', -apple-system, sans-serif;
}

.login-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
  position: relative;
}

.login-loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
  border-radius: 6px;
}

.error-message {
  background: #dc2626;
  color: white;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 14px;
  margin-bottom: 8px;
}

.user-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
  transition: opacity var(--apollo-transition-normal);
}

.user-list.loading {
  opacity: 0.3;
  pointer-events: none;
}

.user-card {
  background: #1F2025;
  border: 1px solid #333;
  border-radius: 6px;
  padding: 12px 16px;
  cursor: pointer;
  transition: all 0.2s;
  text-align: left;
  width: 100%;
}

.user-card:hover {
  background: #2a2d33;
  border-color: #007B63;
}

.user-card:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.user-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.user-name {
  color: #e0e0e0;
  font-size: 14px;
  font-weight: 500;
}

.user-role {
  color: #888;
  font-size: 12px;
}