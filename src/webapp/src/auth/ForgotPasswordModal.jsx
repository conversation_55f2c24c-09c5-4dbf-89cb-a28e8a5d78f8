import React, { useState } from 'react';
import './ForgotPasswordModal.css';

const ForgotPasswordModal = ({ isOpen, onClose }) => {
  const [email, setEmail] = useState('');
  const [loading, setLoading] = useState(false);
  const [sent, setSent] = useState(false);
  const [error, setError] = useState('');

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    // Simulate API call
    setTimeout(() => {
      if (email.includes('@')) {
        setSent(true);
      } else {
        setError('Please enter a valid email address');
      }
      setLoading(false);
    }, 1000);
  };

  const handleClose = () => {
    setEmail('');
    setError('');
    setSent(false);
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="forgot-modal-overlay" onClick={handleClose}>
      <div className="forgot-modal" onClick={e => e.stopPropagation()}>
        <div className="forgot-header">
          <h2>{sent ? 'Email Sent' : 'Reset Password'}</h2>
          <button className="close-button" onClick={handleClose}>
            <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
              <path d="M8 6.586L13.657 1 15 2.343 9.414 8 15 13.657 13.657 15 8 9.414 2.343 15 1 13.657 6.586 8 1 2.343 2.343 1 8 6.586z"/>
            </svg>
          </button>
        </div>

        <div className="forgot-content">
          {sent ? (
            <div className="success-content">
              <div className="success-icon">
                <svg width="48" height="48" viewBox="0 0 48 48" fill="currentColor">
                  <path d="M24 4C12.95 4 4 12.95 4 24s8.95 20 20 20 20-8.95 20-20S35.05 4 24 4zm-4 30L10 24l2.83-2.83L20 28.34l15.17-15.17L38 16 20 34z"/>
                </svg>
              </div>
              <h3>Password reset email sent!</h3>
              <p>
                We've sent password reset instructions to <strong>{email}</strong>. 
                Please check your email and follow the instructions to reset your password.
              </p>
              <div className="help-text">
                <p>Didn't receive the email? Check your spam folder or try again.</p>
              </div>
              <div className="success-actions">
                <button className="primary-button" onClick={handleClose}>
                  Back to Login
                </button>
                <button className="secondary-button" onClick={() => setSent(false)}>
                  Try Different Email
                </button>
              </div>
            </div>
          ) : (
            <div className="form-content">
              <div className="forgot-description">
                <p>Enter your email address and we'll send you instructions to reset your password.</p>
              </div>

              <form onSubmit={handleSubmit} className="forgot-form">
                <div className="form-group">
                  <label htmlFor="email">Email Address</label>
                  <input
                    type="email"
                    id="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    placeholder="Enter your email address"
                    disabled={loading}
                    required
                    autoComplete="email"
                  />
                </div>

                {error && <div className="error-message">{error}</div>}

                <button type="submit" className="reset-button" disabled={loading || !email}>
                  {loading ? (
                    <span className="loading-text">
                      <svg className="loading-spinner" width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                        <path d="M8 0v4c2.2 0 4 1.8 4 4s-1.8 4-4 4-4-1.8-4-4H0c0 4.4 3.6 8 8 8s8-3.6 8-8-3.6-8-8-8z"/>
                      </svg>
                      Sending...
                    </span>
                  ) : (
                    'Send Reset Instructions'
                  )}
                </button>
              </form>

              <div className="back-to-login">
                <button className="back-link" onClick={handleClose}>
                  ← Back to Sign In
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ForgotPasswordModal;