import React, { createContext, useContext, useState, useEffect } from 'react';
import { authAPI } from '../api/client.js';

const AuthContext = createContext(null);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [sessionToken, setSessionToken] = useState(null);
  const [sessionExpiry, setSessionExpiry] = useState(null);

  // Session timeout (30 minutes)
  const SESSION_TIMEOUT = 30 * 60 * 1000; // 30 minutes in milliseconds

  // Initialize auth state from localStorage
  useEffect(() => {
    const initAuth = async () => {
      const storedToken = localStorage.getItem('apollo_session_token');
      const storedExpiry = localStorage.getItem('apollo_session_expiry');
      
      if (storedToken && storedExpiry) {
        const expiryTime = parseInt(storedExpiry);
        const now = Date.now();
        
        // Check if session has expired
        if (now > expiryTime) {
          console.log('Session expired');
          localStorage.removeItem('apollo_session_token');
          localStorage.removeItem('apollo_session_expiry');
          setLoading(false);
          return;
        }
        
        try {
          const currentUser = await authAPI.getCurrentUser(storedToken);
          if (currentUser) {
            setUser(currentUser);
            setSessionToken(storedToken);
            setSessionExpiry(expiryTime);
            
            // Refresh session if less than 5 minutes remaining
            const timeRemaining = expiryTime - now;
            if (timeRemaining < 5 * 60 * 1000) {
              refreshSession(storedToken);
            }
          } else {
            localStorage.removeItem('apollo_session_token');
            localStorage.removeItem('apollo_session_expiry');
          }
        } catch (error) {
          console.error('Failed to restore session:', error);
          localStorage.removeItem('apollo_session_token');
          localStorage.removeItem('apollo_session_expiry');
        }
      }
      setLoading(false);
    };

    initAuth();
  }, []);

  // Auto refresh session
  const refreshSession = async (token) => {
    try {
      const newExpiry = Date.now() + SESSION_TIMEOUT;
      setSessionExpiry(newExpiry);
      localStorage.setItem('apollo_session_expiry', newExpiry.toString());
      console.log('Session refreshed');
    } catch (error) {
      console.error('Failed to refresh session:', error);
    }
  };

  // Setup session monitoring
  useEffect(() => {
    if (!sessionExpiry || !sessionToken) return;

    const checkSession = () => {
      const now = Date.now();
      const timeRemaining = sessionExpiry - now;
      
      if (timeRemaining <= 0) {
        // Session expired
        logout();
        alert('Your session has expired. Please sign in again.');
      } else if (timeRemaining < 5 * 60 * 1000) { // 5 minutes warning
        // Auto-refresh session
        refreshSession(sessionToken);
      }
    };

    // Check every minute
    const interval = setInterval(checkSession, 60 * 1000);
    
    return () => clearInterval(interval);
  }, [sessionExpiry, sessionToken]);

  const login = async (username, password = 'mock-password') => {
    try {
      const result = await authAPI.login(username, password);
      const expiry = Date.now() + SESSION_TIMEOUT;
      
      setUser(result.user);
      setSessionToken(result.sessionToken);
      setSessionExpiry(expiry);
      
      localStorage.setItem('apollo_session_token', result.sessionToken);
      localStorage.setItem('apollo_session_expiry', expiry.toString());
      
      return result.user;
    } catch (error) {
      throw error;
    }
  };

  const logout = async () => {
    try {
      if (sessionToken) {
        await authAPI.logout(sessionToken);
      }
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      setUser(null);
      setSessionToken(null);
      setSessionExpiry(null);
      localStorage.removeItem('apollo_session_token');
      localStorage.removeItem('apollo_session_expiry');
    }
  };

  const getSessionTimeRemaining = () => {
    if (!sessionExpiry) return null;
    return Math.max(0, sessionExpiry - Date.now());
  };

  const value = {
    user,
    loading,
    sessionToken,
    sessionExpiry,
    login,
    logout,
    refreshSession,
    getSessionTimeRemaining,
    isAuthenticated: !!user
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

export default AuthContext;