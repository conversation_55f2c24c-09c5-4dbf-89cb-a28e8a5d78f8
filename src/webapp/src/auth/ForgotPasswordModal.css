/* Forgot Password Modal Styles */
.forgot-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  backdrop-filter: blur(8px);
}

.forgot-modal {
  background: white;
  border-radius: 16px;
  width: 100%;
  max-width: 480px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
  animation: modalSlideIn 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Header */
.forgot-header {
  padding: 32px 32px 24px;
  border-bottom: 1px solid #f1f5f9;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
  border-radius: 16px 16px 0 0;
}

.forgot-header h2 {
  font-family: 'AGaramondPro-Bold', Georgia, serif;
  font-size: 24px;
  font-weight: 700;
  color: #1a1a1a;
  margin: 0;
}

.close-button {
  background: rgba(107, 114, 128, 0.1);
  border: none;
  color: #64748b;
  cursor: pointer;
  padding: 12px;
  border-radius: 8px;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-button:hover {
  background: #f1f5f9;
  color: #1a1a1a;
}

/* Content */
.forgot-content {
  padding: 32px;
}

.forgot-description {
  text-align: center;
  margin-bottom: 32px;
}

.forgot-description p {
  color: #64748b;
  font-size: 16px;
  margin: 0;
  line-height: 1.5;
}

/* Form */
.forgot-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-group label {
  font-weight: 500;
  color: #374151;
  font-size: 14px;
}

.form-group input {
  padding: 14px 16px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 14px;
  transition: all 0.2s;
  font-family: 'Segoe UI', -apple-system, sans-serif;
  background: white;
}

.form-group input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-group input:disabled {
  background: #f9fafb;
  color: #6b7280;
}

.error-message {
  padding: 12px 16px;
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 8px;
  color: #dc2626;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.error-message::before {
  content: '⚠️';
  font-size: 16px;
}

.reset-button {
  padding: 14px 24px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
  font-family: 'Segoe UI', -apple-system, sans-serif;
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  min-height: 48px;
}

.reset-button:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.reset-button:disabled {
  background: #d1d5db;
  cursor: not-allowed;
  transform: none;
}

.loading-text {
  display: flex;
  align-items: center;
  gap: 8px;
}

.loading-spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.back-to-login {
  text-align: center;
  margin-top: 24px;
  padding-top: 20px;
  border-top: 1px solid #f1f5f9;
}

.back-link {
  background: none;
  border: none;
  color: #667eea;
  cursor: pointer;
  font-size: 14px;
  text-decoration: none;
  transition: color 0.2s;
  font-family: 'Segoe UI', -apple-system, sans-serif;
}

.back-link:hover {
  color: #5a67d8;
  text-decoration: underline;
}

/* Success Content */
.success-content {
  text-align: center;
}

.success-icon {
  color: #10b981;
  margin-bottom: 20px;
  display: flex;
  justify-content: center;
}

.success-content h3 {
  font-size: 20px;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0 0 16px 0;
}

.success-content p {
  color: #64748b;
  font-size: 16px;
  margin: 0 0 24px 0;
  line-height: 1.5;
}

.help-text {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 24px;
}

.help-text p {
  color: #6b7280;
  font-size: 14px;
  margin: 0;
}

.success-actions {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.primary-button {
  padding: 12px 24px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
  font-family: 'Segoe UI', -apple-system, sans-serif;
  font-size: 14px;
}

.primary-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.secondary-button {
  padding: 12px 24px;
  background: none;
  color: #667eea;
  border: 1px solid #667eea;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  font-family: 'Segoe UI', -apple-system, sans-serif;
  font-size: 14px;
}

.secondary-button:hover {
  background: #667eea;
  color: white;
}

/* Responsive Design */
@media (max-width: 640px) {
  .forgot-modal {
    max-width: 95%;
    margin: 20px;
  }
  
  .forgot-header {
    padding: 24px 24px 20px;
  }
  
  .forgot-content {
    padding: 24px;
  }
  
  .forgot-header h2 {
    font-size: 20px;
  }
}