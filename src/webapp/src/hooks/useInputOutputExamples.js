import { useState, useCallback, useEffect } from 'react'

/**
 * useInputOutputExamples - Hook for managing input/output examples CRUD operations
 * 
 * @param {Array} initialExamples - Initial examples array
 * @param {Object} options
 * @param {Function} options.onUpdate - Callback when examples are updated
 * @param {Function} options.onAdd - Callback when example is added
 * @param {Function} options.onEdit - Callback when example is edited
 * @param {Function} options.onDelete - Callback when example is deleted
 * @param {Function} options.validateExample - Function to validate example before operations
 * @param {boolean} options.autoSave - Whether to auto-save changes
 * @param {number} options.maxExamples - Maximum number of examples allowed
 * 
 * @returns {Object} Examples state and CRUD operations
 */
function useInputOutputExamples(initialExamples = [], options = {}) {
  const {
    onUpdate,
    onAdd,
    onEdit,
    onDelete,
    validateExample,
    autoSave = false,
    maxExamples = 10
  } = options

  const [examples, setExamples] = useState(initialExamples)
  const [hasChanges, setHasChanges] = useState(false)
  const [errors, setErrors] = useState({})
  const [isLoading, setIsLoading] = useState(false)

  // Update examples when initialExamples changes
  useEffect(() => {
    setExamples(initialExamples)
    setHasChanges(false)
    setErrors({})
  }, [initialExamples])

  // Auto-save changes
  useEffect(() => {
    if (autoSave && hasChanges && onUpdate) {
      const timeoutId = setTimeout(() => {
        save()
      }, 1000) // Debounce auto-save

      return () => clearTimeout(timeoutId)
    }
  }, [examples, hasChanges, autoSave, onUpdate])

  const validateExampleData = useCallback((example, index) => {
    const errors = []

    if (!example.input || example.input.trim() === '') {
      errors.push('Input is required')
    }

    if (!example.output || example.output.trim() === '') {
      errors.push('Output is required')
    }

    if (example.input && example.input.length > 1000) {
      errors.push('Input is too long (max 1000 characters)')
    }

    if (example.output && example.output.length > 2000) {
      errors.push('Output is too long (max 2000 characters)')
    }

    // Custom validation
    if (validateExample) {
      const customErrors = validateExample(example, index, examples)
      if (customErrors && customErrors.length > 0) {
        errors.push(...customErrors)
      }
    }

    return errors
  }, [validateExample, examples])

  const addExample = useCallback((newExample = {}) => {
    if (examples.length >= maxExamples) {
      setErrors(prev => ({
        ...prev,
        general: `Maximum ${maxExamples} examples allowed`
      }))
      return false
    }

    const example = {
      id: Date.now().toString(),
      input: '',
      output: '',
      ...newExample
    }

    const validationErrors = validateExampleData(example, examples.length)
    if (validationErrors.length > 0) {
      setErrors(prev => ({
        ...prev,
        [example.id]: validationErrors
      }))
      return false
    }

    setExamples(prev => {
      const updated = [...prev, example]
      if (onAdd) {
        onAdd(example, updated)
      }
      return updated
    })
    
    setHasChanges(true)
    setErrors(prev => {
      const next = { ...prev }
      delete next.general
      delete next[example.id]
      return next
    })

    return true
  }, [examples, maxExamples, validateExampleData, onAdd])

  const updateExample = useCallback((index, updates) => {
    if (index < 0 || index >= examples.length) {
      return false
    }

    const updatedExample = { ...examples[index], ...updates }
    const validationErrors = validateExampleData(updatedExample, index)
    
    if (validationErrors.length > 0) {
      setErrors(prev => ({
        ...prev,
        [updatedExample.id || index]: validationErrors
      }))
      return false
    }

    setExamples(prev => {
      const updated = [...prev]
      updated[index] = updatedExample
      if (onEdit) {
        onEdit(updatedExample, index, updated)
      }
      return updated
    })

    setHasChanges(true)
    setErrors(prev => {
      const next = { ...prev }
      delete next[updatedExample.id || index]
      return next
    })

    return true
  }, [examples, validateExampleData, onEdit])

  const deleteExample = useCallback((index) => {
    if (index < 0 || index >= examples.length) {
      return false
    }

    const exampleToDelete = examples[index]
    
    setExamples(prev => {
      const updated = prev.filter((_, i) => i !== index)
      if (onDelete) {
        onDelete(exampleToDelete, index, updated)
      }
      return updated
    })

    setHasChanges(true)
    setErrors(prev => {
      const next = { ...prev }
      delete next[exampleToDelete.id || index]
      delete next.general
      return next
    })

    return true
  }, [examples, onDelete])

  const moveExample = useCallback((fromIndex, toIndex) => {
    if (fromIndex < 0 || fromIndex >= examples.length || 
        toIndex < 0 || toIndex >= examples.length) {
      return false
    }

    setExamples(prev => {
      const updated = [...prev]
      const [movedExample] = updated.splice(fromIndex, 1)
      updated.splice(toIndex, 0, movedExample)
      return updated
    })

    setHasChanges(true)
    return true
  }, [examples])

  const duplicateExample = useCallback((index) => {
    if (index < 0 || index >= examples.length) {
      return false
    }

    if (examples.length >= maxExamples) {
      setErrors(prev => ({
        ...prev,
        general: `Maximum ${maxExamples} examples allowed`
      }))
      return false
    }

    const originalExample = examples[index]
    const duplicatedExample = {
      ...originalExample,
      id: Date.now().toString(),
      input: `${originalExample.input} (copy)`,
      output: originalExample.output
    }

    setExamples(prev => {
      const updated = [...prev]
      updated.splice(index + 1, 0, duplicatedExample)
      return updated
    })

    setHasChanges(true)
    return true
  }, [examples, maxExamples])

  const clearAllExamples = useCallback(() => {
    setExamples([])
    setHasChanges(true)
    setErrors({})
  }, [])

  const resetToInitial = useCallback(() => {
    setExamples(initialExamples)
    setHasChanges(false)
    setErrors({})
  }, [initialExamples])

  const save = useCallback(async () => {
    if (!onUpdate || !hasChanges) {
      return true
    }

    setIsLoading(true)
    
    try {
      await onUpdate(examples)
      setHasChanges(false)
      setErrors({})
      return true
    } catch (error) {
      setErrors(prev => ({
        ...prev,
        general: error.message || 'Failed to save examples'
      }))
      return false
    } finally {
      setIsLoading(false)
    }
  }, [examples, hasChanges, onUpdate])

  const getExampleErrors = useCallback((index) => {
    const example = examples[index]
    if (!example) return []
    
    return errors[example.id || index] || []
  }, [examples, errors])

  const hasErrors = Object.keys(errors).length > 0
  const canAddMore = examples.length < maxExamples
  const isEmpty = examples.length === 0

  return {
    // State
    examples,
    hasChanges,
    errors,
    isLoading,
    hasErrors,
    canAddMore,
    isEmpty,

    // CRUD operations
    addExample,
    updateExample,
    deleteExample,
    moveExample,
    duplicateExample,
    clearAllExamples,

    // Utility functions
    save,
    resetToInitial,
    getExampleErrors,

    // Validation
    validateExample: validateExampleData
  }
}

/**
 * useExampleEditor - Hook for editing a single example with validation
 */
export function useExampleEditor(initialExample = { input: '', output: '' }, options = {}) {
  const [example, setExample] = useState(initialExample)
  const [errors, setErrors] = useState([])
  const [isDirty, setIsDirty] = useState(false)

  const updateInput = useCallback((input) => {
    setExample(prev => ({ ...prev, input }))
    setIsDirty(true)
    
    // Clear input-related errors
    setErrors(prev => prev.filter(error => !error.includes('Input')))
  }, [])

  const updateOutput = useCallback((output) => {
    setExample(prev => ({ ...prev, output }))
    setIsDirty(true)
    
    // Clear output-related errors
    setErrors(prev => prev.filter(error => !error.includes('Output')))
  }, [])

  const validate = useCallback(() => {
    const validationErrors = []

    if (!example.input || example.input.trim() === '') {
      validationErrors.push('Input is required')
    }

    if (!example.output || example.output.trim() === '') {
      validationErrors.push('Output is required')
    }

    if (example.input && example.input.length > 1000) {
      validationErrors.push('Input is too long (max 1000 characters)')
    }

    if (example.output && example.output.length > 2000) {
      validationErrors.push('Output is too long (max 2000 characters)')
    }

    if (options.customValidation) {
      const customErrors = options.customValidation(example)
      if (customErrors && customErrors.length > 0) {
        validationErrors.push(...customErrors)
      }
    }

    setErrors(validationErrors)
    return validationErrors.length === 0
  }, [example, options.customValidation])

  const reset = useCallback(() => {
    setExample(initialExample)
    setErrors([])
    setIsDirty(false)
  }, [initialExample])

  const save = useCallback(() => {
    if (validate()) {
      setIsDirty(false)
      if (options.onSave) {
        options.onSave(example)
      }
      return true
    }
    return false
  }, [example, validate, options.onSave])

  return {
    example,
    errors,
    isDirty,
    isValid: errors.length === 0,
    updateInput,
    updateOutput,
    validate,
    reset,
    save
  }
}

export default useInputOutputExamples