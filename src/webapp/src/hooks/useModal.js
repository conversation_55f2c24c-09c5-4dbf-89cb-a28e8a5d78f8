import { useState, useCallback, useEffect } from 'react'
import { useEscapeKey } from './useKeyboard'

/**
 * useModal - Hook for managing modal state and behavior
 * 
 * @param {Object} options
 * @param {boolean} options.initialOpen - Initial open state
 * @param {Function} options.onOpen - Callback when modal opens
 * @param {Function} options.onClose - Callback when modal closes
 * @param {boolean} options.closeOnEscape - Whether to close on Escape key
 * @param {boolean} options.closeOnOutsideClick - Whether to close on outside click
 * @param {boolean} options.preventBodyScroll - Whether to prevent body scroll when open
 * @param {Array} options.dependencies - Dependencies for callbacks
 * 
 * @returns {Object} Modal state and controls
 */
function useModal({
  initialOpen = false,
  onOpen,
  onClose,
  closeOnEscape = true,
  closeOnOutsideClick = true,
  preventBodyScroll = true,
  dependencies = []
} = {}) {
  const [isOpen, setIsOpen] = useState(initialOpen)
  const [isClosing, setIsClosing] = useState(false)

  const open = useCallback((data) => {
    setIsOpen(true)
    setIsClosing(false)
    onOpen?.(data)
  }, [onOpen, ...dependencies])

  const close = useCallback((data) => {
    setIsClosing(true)
    // Small delay to allow close animation
    setTimeout(() => {
      setIsOpen(false)
      setIsClosing(false)
      onClose?.(data)
    }, 150)
  }, [onClose, ...dependencies])

  const toggle = useCallback((data) => {
    if (isOpen) {
      close(data)
    } else {
      open(data)
    }
  }, [isOpen, open, close])

  const handleOutsideClick = useCallback((event) => {
    if (closeOnOutsideClick && event.target === event.currentTarget) {
      close()
    }
  }, [close, closeOnOutsideClick])

  // Handle escape key
  useEscapeKey(
    closeOnEscape ? close : () => {},
    isOpen && closeOnEscape,
    dependencies
  )

  // Prevent body scroll when modal is open
  useEffect(() => {
    if (!preventBodyScroll) return

    if (isOpen) {
      const originalStyle = window.getComputedStyle(document.body).overflow
      document.body.style.overflow = 'hidden'
      
      return () => {
        document.body.style.overflow = originalStyle
      }
    }
  }, [isOpen, preventBodyScroll])

  return {
    isOpen,
    isClosing,
    open,
    close,
    toggle,
    handleOutsideClick
  }
}

/**
 * useMultiModal - Hook for managing multiple modals
 * 
 * @param {Array} modalIds - Array of modal identifiers
 * @param {Object} options - Options for all modals
 */
export function useMultiModal(modalIds = [], options = {}) {
  const [openModals, setOpenModals] = useState(new Set())
  const [closingModals, setClosingModals] = useState(new Set())

  const openModal = useCallback((modalId, data) => {
    setOpenModals(prev => new Set([...prev, modalId]))
    setClosingModals(prev => {
      const next = new Set(prev)
      next.delete(modalId)
      return next
    })
    options.onOpen?.(modalId, data)
  }, [options.onOpen])

  const closeModal = useCallback((modalId, data) => {
    setClosingModals(prev => new Set([...prev, modalId]))
    setTimeout(() => {
      setOpenModals(prev => {
        const next = new Set(prev)
        next.delete(modalId)
        return next
      })
      setClosingModals(prev => {
        const next = new Set(prev)
        next.delete(modalId)
        return next
      })
      options.onClose?.(modalId, data)
    }, 150)
  }, [options.onClose])

  const toggleModal = useCallback((modalId, data) => {
    if (openModals.has(modalId)) {
      closeModal(modalId, data)
    } else {
      openModal(modalId, data)
    }
  }, [openModals, openModal, closeModal])

  const closeAllModals = useCallback(() => {
    openModals.forEach(modalId => closeModal(modalId))
  }, [openModals, closeModal])

  const isModalOpen = useCallback((modalId) => {
    return openModals.has(modalId)
  }, [openModals])

  const isModalClosing = useCallback((modalId) => {
    return closingModals.has(modalId)
  }, [closingModals])

  // Close all modals on escape
  useEscapeKey(
    options.closeOnEscape !== false ? closeAllModals : () => {},
    openModals.size > 0 && options.closeOnEscape !== false
  )

  // Prevent body scroll when any modal is open
  useEffect(() => {
    if (options.preventBodyScroll !== false && openModals.size > 0) {
      const originalStyle = window.getComputedStyle(document.body).overflow
      document.body.style.overflow = 'hidden'
      
      return () => {
        document.body.style.overflow = originalStyle
      }
    }
  }, [openModals.size, options.preventBodyScroll])

  const modalControls = modalIds.reduce((acc, modalId) => {
    acc[modalId] = {
      isOpen: isModalOpen(modalId),
      isClosing: isModalClosing(modalId),
      open: (data) => openModal(modalId, data),
      close: (data) => closeModal(modalId, data),
      toggle: (data) => toggleModal(modalId, data),
      handleOutsideClick: (event) => {
        if (options.closeOnOutsideClick !== false && event.target === event.currentTarget) {
          closeModal(modalId)
        }
      }
    }
    return acc
  }, {})

  return {
    openModals: Array.from(openModals),
    modalControls,
    openModal,
    closeModal,
    toggleModal,
    closeAllModals,
    isModalOpen,
    isModalClosing,
    hasOpenModals: openModals.size > 0
  }
}

/**
 * useConfirmModal - Hook for confirmation modals
 */
export function useConfirmModal() {
  const [config, setConfig] = useState(null)
  
  const modal = useModal({
    onClose: () => {
      if (config && config.onCancel) {
        config.onCancel()
      }
      setConfig(null)
    }
  })

  const confirm = useCallback((options = {}) => {
    setConfig({
      title: options.title || 'Confirm Action',
      message: options.message || 'Are you sure you want to continue?',
      confirmText: options.confirmText || 'Confirm',
      cancelText: options.cancelText || 'Cancel',
      onConfirm: options.onConfirm,
      onCancel: options.onCancel,
      variant: options.variant || 'default'
    })
    modal.open()
  }, [modal])

  const handleConfirm = useCallback(() => {
    if (config && config.onConfirm) {
      config.onConfirm()
    }
    modal.close()
  }, [config, modal])

  const handleCancel = useCallback(() => {
    if (config && config.onCancel) {
      config.onCancel()
    }
    modal.close()
  }, [config, modal])

  return {
    ...modal,
    config,
    confirm,
    handleConfirm,
    handleCancel
  }
}

/**
 * useDrawer - Hook for drawer/sidebar modals
 */
export function useDrawer(options = {}) {
  const modal = useModal({
    ...options,
    preventBodyScroll: options.preventBodyScroll ?? false
  })

  return {
    ...modal,
    isOpen: modal.isOpen,
    isClosing: modal.isClosing,
    openDrawer: modal.open,
    closeDrawer: modal.close,
    toggleDrawer: modal.toggle
  }
}

export default useModal