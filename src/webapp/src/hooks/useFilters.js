import { useState, useMemo, useCallback, useEffect } from 'react'

/**
 * Advanced filtering hook for prompt library
 * Provides comprehensive filter state management with debounced search,
 * memoized results, and URL sync capabilities
 * 
 * @param {Array} prompts - Array of prompt objects to filter
 * @param {Array} categories - Array of available categories
 * @param {Object} initialFilters - Optional initial filter state
 * @returns {Object} Filter state and methods
 */
export const useFilters = (prompts = [], categories = [], initialFilters = {}) => {
  // Core filter state
  const [filters, setFilters] = useState({
    searchQuery: initialFilters.searchQuery || '',
    category: initialFilters.category || '',
    status: initialFilters.status || [],
    difficulty: initialFilters.difficulty || [],
    author: initialFilters.author || '',
    sortBy: initialFilters.sortBy || 'trending',
    ...initialFilters
  })

  // Debounced search state
  const [debouncedSearchQuery, setDebouncedSearchQuery] = useState(filters.searchQuery)
  
  // Debounce search query updates
  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedSearchQuery(filters.searchQuery)
    }, 300)

    return () => {
      clearTimeout(handler)
    }
  }, [filters.searchQuery])

  // Filter update function
  const setFilter = useCallback((filterKey, value) => {
    setFilters(prev => ({
      ...prev,
      [filterKey]: value
    }))
  }, [])

  // Reset all filters
  const resetFilters = useCallback(() => {
    setFilters({
      searchQuery: '',
      category: '',
      status: [],
      difficulty: [],
      author: '',
      sortBy: 'trending'
    })
  }, [])

  // Advanced search function
  const searchPrompts = useCallback((prompts, query) => {
    if (!query || !query.trim()) return prompts

    const searchTerms = query.toLowerCase().split(' ').filter(term => term.trim().length > 0)
    
    // If no valid search terms after filtering whitespace, return all prompts
    if (searchTerms.length === 0) return prompts
    
    return prompts.filter(prompt => {
      const searchableText = [
        prompt.title,
        prompt.description,
        prompt.content || '',
        prompt.author || '',
        ...(prompt.tags || [])
      ].join(' ').toLowerCase()

      // All search terms must be found (AND operation)
      return searchTerms.every(term => searchableText.includes(term))
    })
  }, [])

  // Category filter function
  const filterByCategory = useCallback((prompts, category) => {
    if (!category) return prompts
    return prompts.filter(prompt => 
      prompt.category.toLowerCase().includes(category.toLowerCase())
    )
  }, [])

  // Status filter function (supports multiple selection)
  const filterByStatus = useCallback((prompts, statusArray) => {
    if (!statusArray || statusArray.length === 0) return prompts
    return prompts.filter(prompt => 
      statusArray.some(status => 
        prompt.status.toLowerCase().includes(status.toLowerCase())
      )
    )
  }, [])

  // Difficulty filter function (supports multiple selection)
  const filterByDifficulty = useCallback((prompts, difficultyArray) => {
    if (!difficultyArray || difficultyArray.length === 0) return prompts
    return prompts.filter(prompt => 
      difficultyArray.some(difficulty => 
        prompt.difficulty && prompt.difficulty.toLowerCase().includes(difficulty.toLowerCase())
      )
    )
  }, [])

  // Author filter function
  const filterByAuthor = useCallback((prompts, author) => {
    if (!author) return prompts
    return prompts.filter(prompt => 
      prompt.author && prompt.author.toLowerCase().includes(author.toLowerCase())
    )
  }, [])

  // Sort function
  const sortPrompts = useCallback((prompts, sortBy) => {
    const sorted = [...prompts]
    
    switch (sortBy) {
      case 'trending':
      case 'popular':
        return sorted.sort((a, b) => (b.votes || 0) - (a.votes || 0))
      
      case 'recent':
        return sorted.sort((a, b) => {
          // Sort by ID descending as a proxy for recent (higher ID = newer)
          return (b.id || 0) - (a.id || 0)
        })
      
      case 'title':
        return sorted.sort((a, b) => a.title.localeCompare(b.title))
      
      case 'difficulty':
        const difficultyOrder = { 'Beginner': 1, 'Intermediate': 2, 'Advanced': 3 }
        return sorted.sort((a, b) => {
          const aDiff = difficultyOrder[a.difficulty] || 0
          const bDiff = difficultyOrder[b.difficulty] || 0
          return aDiff - bDiff
        })
      
      case 'author':
        return sorted.sort((a, b) => (a.author || '').localeCompare(b.author || ''))
      
      default:
        return sorted
    }
  }, [])

  // Main filtered prompts with memoization
  const filteredPrompts = useMemo(() => {
    let result = [...prompts]

    // Apply search filter (using debounced query)
    result = searchPrompts(result, debouncedSearchQuery)

    // Apply category filter
    result = filterByCategory(result, filters.category)

    // Apply status filter
    result = filterByStatus(result, filters.status)

    // Apply difficulty filter
    result = filterByDifficulty(result, filters.difficulty)

    // Apply author filter
    result = filterByAuthor(result, filters.author)

    // Apply sorting
    result = sortPrompts(result, filters.sortBy)

    return result
  }, [
    prompts,
    debouncedSearchQuery,
    filters.category,
    filters.status,
    filters.difficulty,
    filters.author,
    filters.sortBy,
    searchPrompts,
    filterByCategory,
    filterByStatus,
    filterByDifficulty,
    filterByAuthor,
    sortPrompts
  ])

  // Calculate filter counts for badges
  const filterCounts = useMemo(() => {
    const counts = {
      categories: {},
      statuses: {},
      difficulties: {},
      authors: {}
    }

    // Count items for each category
    categories.forEach(category => {
      const categoryName = typeof category === 'object' ? category.name : category
      counts.categories[categoryName] = prompts.filter(prompt => 
        prompt.category.toLowerCase().includes(categoryName.toLowerCase())
      ).length
    })

    // Count items for each status
    const allStatuses = [...new Set(prompts.map(p => p.status).filter(Boolean))]
    allStatuses.forEach(status => {
      counts.statuses[status] = prompts.filter(prompt => 
        prompt.status.toLowerCase().includes(status.toLowerCase())
      ).length
    })

    // Count items for each difficulty
    const allDifficulties = [...new Set(prompts.map(p => p.difficulty).filter(Boolean))]
    allDifficulties.forEach(difficulty => {
      counts.difficulties[difficulty] = prompts.filter(prompt => 
        prompt.difficulty && prompt.difficulty.toLowerCase().includes(difficulty.toLowerCase())
      ).length
    })

    // Count items for each author
    const allAuthors = [...new Set(prompts.map(p => p.author).filter(Boolean))]
    allAuthors.forEach(author => {
      counts.authors[author] = prompts.filter(prompt => 
        prompt.author && prompt.author.toLowerCase().includes(author.toLowerCase())
      ).length
    })

    return counts
  }, [prompts, categories])

  // Count active filters
  const activeFilterCount = useMemo(() => {
    let count = 0
    
    if (filters.searchQuery) count++
    if (filters.category) count++
    if (filters.status && filters.status.length > 0) count++
    if (filters.difficulty && filters.difficulty.length > 0) count++
    if (filters.author) count++
    
    return count
  }, [filters])

  // Search highlighting function
  const highlightSearchTerms = useCallback((text, query) => {
    if (!query || !text) return text
    
    const terms = query.toLowerCase().split(' ').filter(term => term.length > 0)
    let highlightedText = text
    
    terms.forEach(term => {
      const regex = new RegExp(`(${term})`, 'gi')
      highlightedText = highlightedText.replace(regex, '<mark>$1</mark>')
    })
    
    return highlightedText
  }, [])

  // URL sync utilities (prepared for future integration)
  const getFilterParams = useCallback(() => {
    const params = new URLSearchParams()
    
    if (filters.searchQuery) params.set('q', filters.searchQuery)
    if (filters.category) params.set('category', filters.category)
    if (filters.status && filters.status.length > 0) params.set('status', filters.status.join(','))
    if (filters.difficulty && filters.difficulty.length > 0) params.set('difficulty', filters.difficulty.join(','))
    if (filters.author) params.set('author', filters.author)
    if (filters.sortBy !== 'trending') params.set('sort', filters.sortBy)
    
    return params.toString()
  }, [filters])

  const setFiltersFromParams = useCallback((searchParams) => {
    const params = new URLSearchParams(searchParams)
    
    setFilters({
      searchQuery: params.get('q') || '',
      category: params.get('category') || '',
      status: params.get('status') ? params.get('status').split(',') : [],
      difficulty: params.get('difficulty') ? params.get('difficulty').split(',') : [],
      author: params.get('author') || '',
      sortBy: params.get('sort') || 'trending'
    })
  }, [])

  // Multi-filter toggle utility
  const toggleMultiFilter = useCallback((filterKey, value) => {
    setFilters(prev => {
      const currentArray = prev[filterKey] || []
      const newArray = currentArray.includes(value)
        ? currentArray.filter(item => item !== value)
        : [...currentArray, value]
      
      return {
        ...prev,
        [filterKey]: newArray
      }
    })
  }, [])

  // Filter combination utilities
  const hasActiveFilters = useMemo(() => activeFilterCount > 0, [activeFilterCount])
  
  const getFilterSummary = useMemo(() => {
    const summary = []
    
    if (filters.searchQuery) summary.push(`Search: "${filters.searchQuery}"`)
    if (filters.category) summary.push(`Category: ${filters.category}`)
    if (filters.status && filters.status.length > 0) summary.push(`Status: ${filters.status.join(', ')}`)
    if (filters.difficulty && filters.difficulty.length > 0) summary.push(`Difficulty: ${filters.difficulty.join(', ')}`)
    if (filters.author) summary.push(`Author: ${filters.author}`)
    
    return summary
  }, [filters])

  return {
    // Filtered results
    filteredPrompts,
    
    // Filter state
    filters,
    debouncedSearchQuery,
    
    // Filter methods
    setFilter,
    resetFilters,
    toggleMultiFilter,
    
    // Filter counts and metrics
    filterCounts,
    activeFilterCount,
    hasActiveFilters,
    getFilterSummary,
    
    // Utility functions
    highlightSearchTerms,
    
    // URL sync utilities (for future use)
    getFilterParams,
    setFiltersFromParams,
    
    // Individual filter functions (exposed for advanced use cases)
    searchPrompts,
    filterByCategory,
    filterByStatus,
    filterByDifficulty,
    filterByAuthor,
    sortPrompts
  }
}

export default useFilters