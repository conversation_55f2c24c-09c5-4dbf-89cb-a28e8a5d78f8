import { renderHook, act } from '@testing-library/react'
import { vi, describe, it, expect, beforeEach } from 'vitest'
import { useVote } from '../useVote'
import { useAuth } from '../../auth/AuthContext'

// Mock the auth context
vi.mock('../../auth/AuthContext')

// Mock the dynamic imports
vi.mock('../../api/browserEndpoints.js', () => ({
  promptsAPI: {
    vote: vi.fn()
  }
}))

vi.mock('../../api/client.js', () => ({
  promptsAPI: {
    vote: vi.fn()
  }
}))

// Mock config
vi.mock('../../config.js', () => ({
  USE_DATABASE_INSTEAD_OF_JSON_FILE: false
}))

describe('useVote', () => {
  const mockUser = {
    id: 1,
    username: 'testuser',
    email: '<EMAIL>'
  }

  const mockPrompt = {
    id: 1,
    votes: 5,
    userVote: null
  }

  beforeEach(() => {
    vi.clearAllMocks()
    
    // Default auth mock
    useAuth.mockReturnValue({
      user: mockUser,
      isAuthenticated: true
    })
  })

  describe('Initial state', () => {
    it('should initialize with correct default values', () => {
      const { result } = renderHook(() => 
        useVote(mockPrompt.id, mockPrompt.votes, mockPrompt.userVote)
      )

      expect(result.current.votes).toBe(5)
      expect(result.current.userVote).toBe(null)
      expect(result.current.isVoting).toBe(false)
      expect(result.current.hasUserVoted).toBe(false)
      expect(result.current.error).toBe(null)
      expect(result.current.canVote).toBe(true)
    })

    it('should initialize with user vote when provided', () => {
      const { result } = renderHook(() => 
        useVote(mockPrompt.id, mockPrompt.votes, 'up')
      )

      expect(result.current.userVote).toBe('up')
      expect(result.current.hasUserVoted).toBe(true)
    })

    it('should update when props change', () => {
      const { result, rerender } = renderHook(
        ({ votes, userVote }) => useVote(mockPrompt.id, votes, userVote),
        { initialProps: { votes: 5, userVote: null } }
      )

      expect(result.current.votes).toBe(5)
      expect(result.current.userVote).toBe(null)

      rerender({ votes: 10, userVote: 'up' })

      expect(result.current.votes).toBe(10)
      expect(result.current.userVote).toBe('up')
    })
  })

  describe('Authentication', () => {
    it('should set canVote to false when not authenticated', () => {
      useAuth.mockReturnValue({
        user: null,
        isAuthenticated: false
      })

      const { result } = renderHook(() => 
        useVote(mockPrompt.id, mockPrompt.votes, mockPrompt.userVote)
      )

      expect(result.current.canVote).toBe(false)
    })

    it('should throw AUTH_REQUIRED error when voting without authentication', async () => {
      useAuth.mockReturnValue({
        user: null,
        isAuthenticated: false
      })

      const { result } = renderHook(() => 
        useVote(mockPrompt.id, mockPrompt.votes, mockPrompt.userVote)
      )

      await act(async () => {
        try {
          await result.current.handleVote('toggle')
        } catch (error) {
          expect(error.code).toBe('AUTH_REQUIRED')
          expect(error.message).toBe('Authentication required to vote')
        }
      })

      expect(result.current.error).toBe('Please log in to vote')
    })
  })

  describe('Voting functionality', () => {
    let mockPromptAPI

    beforeEach(async () => {
      // Import and get the mock
      const { promptsAPI } = await import('../../api/client.js')
      mockPromptAPI = promptsAPI
    })

    it('should handle toggle vote (add vote)', async () => {
      mockPromptAPI.vote.mockResolvedValue({
        id: 1,
        votes: 6,
        userVote: 'up'
      })

      const { result } = renderHook(() => 
        useVote(mockPrompt.id, mockPrompt.votes, null)
      )

      await act(async () => {
        await result.current.handleVote('toggle')
      })

      expect(mockPromptAPI.vote).toHaveBeenCalledWith(1, 1, 'toggle')
      expect(result.current.votes).toBe(6)
      expect(result.current.userVote).toBe('up')
      expect(result.current.hasUserVoted).toBe(true)
    })

    it('should handle toggle vote (remove vote)', async () => {
      mockPromptAPI.vote.mockResolvedValue({
        id: 1,
        votes: 4,
        userVote: null
      })

      const { result } = renderHook(() => 
        useVote(mockPrompt.id, 5, 'up')
      )

      await act(async () => {
        await result.current.handleVote('toggle')
      })

      expect(mockPromptAPI.vote).toHaveBeenCalledWith(1, 1, 'toggle')
      expect(result.current.votes).toBe(4)
      expect(result.current.userVote).toBe(null)
      expect(result.current.hasUserVoted).toBe(false)
    })

    it('should handle upvote', async () => {
      mockPromptAPI.vote.mockResolvedValue({
        id: 1,
        votes: 6,
        userVote: 'up'
      })

      const { result } = renderHook(() => 
        useVote(mockPrompt.id, mockPrompt.votes, null)
      )

      await act(async () => {
        await result.current.upvote()
      })

      expect(mockPromptAPI.vote).toHaveBeenCalledWith(1, 1, 'up')
      expect(result.current.votes).toBe(6)
      expect(result.current.userVote).toBe('up')
    })

    it('should handle downvote', async () => {
      mockPromptAPI.vote.mockResolvedValue({
        id: 1,
        votes: 4,
        userVote: 'down'
      })

      const { result } = renderHook(() => 
        useVote(mockPrompt.id, mockPrompt.votes, null)
      )

      await act(async () => {
        await result.current.downvote()
      })

      expect(mockPromptAPI.vote).toHaveBeenCalledWith(1, 1, 'down')
      expect(result.current.votes).toBe(4)
      expect(result.current.userVote).toBe('down')
    })

    it('should handle change from upvote to downvote', async () => {
      mockPromptAPI.vote.mockResolvedValue({
        id: 1,
        votes: 3,
        userVote: 'down'
      })

      const { result } = renderHook(() => 
        useVote(mockPrompt.id, 5, 'up')
      )

      await act(async () => {
        await result.current.downvote()
      })

      expect(mockPromptAPI.vote).toHaveBeenCalledWith(1, 1, 'down')
      expect(result.current.votes).toBe(3)
      expect(result.current.userVote).toBe('down')
    })
  })

  describe('Optimistic updates', () => {
    let mockPromptAPI

    beforeEach(async () => {
      const { promptsAPI } = await import('../../api/client.js')
      mockPromptAPI = promptsAPI
    })

    it('should apply optimistic updates immediately', async () => {
      // Make API call slow to test optimistic updates
      mockPromptAPI.vote.mockImplementation(() => 
        new Promise(resolve => setTimeout(() => resolve({
          id: 1,
          votes: 6,
          userVote: 'up'
        }), 100))
      )

      const { result } = renderHook(() => 
        useVote(mockPrompt.id, 5, null)
      )

      // Start the vote
      act(() => {
        result.current.handleVote('toggle')
      })

      // Should immediately show optimistic update
      expect(result.current.votes).toBe(6)
      expect(result.current.userVote).toBe('up')
      expect(result.current.isVoting).toBe(true)

      // Wait for API call to complete
      await act(async () => {
        await new Promise(resolve => setTimeout(resolve, 150))
      })

      expect(result.current.isVoting).toBe(false)
    })

    it('should rollback on error', async () => {
      mockPromptAPI.vote.mockRejectedValue(new Error('Network error'))

      const { result } = renderHook(() => 
        useVote(mockPrompt.id, 5, null)
      )

      await act(async () => {
        try {
          await result.current.handleVote('toggle')
        } catch (error) {
          // Expected to throw
        }
      })

      // Should rollback to original state
      expect(result.current.votes).toBe(5)
      expect(result.current.userVote).toBe(null)
      expect(result.current.error).toBe('Failed to vote. Please try again.')
    })
  })

  describe('Loading states', () => {
    let mockPromptAPI

    beforeEach(async () => {
      const { promptsAPI } = await import('../../api/client.js')
      mockPromptAPI = promptsAPI
    })

    it('should set isVoting during API call', async () => {
      mockPromptAPI.vote.mockImplementation(() => 
        new Promise(resolve => setTimeout(() => resolve({
          id: 1,
          votes: 6,
          userVote: 'up'
        }), 50))
      )

      const { result } = renderHook(() => 
        useVote(mockPrompt.id, mockPrompt.votes, null)
      )

      expect(result.current.isVoting).toBe(false)

      act(() => {
        result.current.handleVote('toggle')
      })

      expect(result.current.isVoting).toBe(true)

      await act(async () => {
        await new Promise(resolve => setTimeout(resolve, 100))
      })

      expect(result.current.isVoting).toBe(false)
    })

    it('should prevent multiple simultaneous votes', async () => {
      mockPromptAPI.vote.mockImplementation(() => 
        new Promise(resolve => setTimeout(() => resolve({
          id: 1,
          votes: 6,
          userVote: 'up'
        }), 100))
      )

      const { result } = renderHook(() => 
        useVote(mockPrompt.id, mockPrompt.votes, null)
      )

      // Start first vote
      act(() => {
        result.current.handleVote('toggle')
      })

      expect(result.current.isVoting).toBe(true)

      // Try to vote again - should be ignored
      act(() => {
        result.current.handleVote('toggle')
      })

      // Should only be called once
      expect(mockPromptAPI.vote).toHaveBeenCalledTimes(1)
    })
  })

  describe('Error handling', () => {
    let mockPromptAPI

    beforeEach(async () => {
      const { promptsAPI } = await import('../../api/client.js')
      mockPromptAPI = promptsAPI
    })

    it('should handle API errors gracefully', async () => {
      mockPromptAPI.vote.mockRejectedValue(new Error('Server error'))

      const { result } = renderHook(() => 
        useVote(mockPrompt.id, mockPrompt.votes, null)
      )

      await act(async () => {
        try {
          await result.current.handleVote('toggle')
        } catch (error) {
          expect(error.message).toBe('Server error')
        }
      })

      expect(result.current.error).toBe('Failed to vote. Please try again.')
      expect(result.current.isVoting).toBe(false)
    })

    it('should clear errors', () => {
      const { result } = renderHook(() => 
        useVote(mockPrompt.id, mockPrompt.votes, null)
      )

      // Simulate an error
      act(() => {
        result.current.error = 'Test error'
      })

      act(() => {
        result.current.clearError()
      })

      expect(result.current.error).toBe(null)
    })
  })

  describe('Callback functionality', () => {
    let mockPromptAPI
    let mockCallback

    beforeEach(async () => {
      const { promptsAPI } = await import('../../api/client.js')
      mockPromptAPI = promptsAPI
      mockCallback = vi.fn()
    })

    it('should call onVoteChange callback when provided', async () => {
      const updatedPrompt = {
        id: 1,
        votes: 6,
        userVote: 'up'
      }

      mockPromptAPI.vote.mockResolvedValue(updatedPrompt)

      const { result } = renderHook(() => 
        useVote(mockPrompt.id, mockPrompt.votes, null, mockCallback)
      )

      await act(async () => {
        await result.current.handleVote('toggle')
      })

      expect(mockCallback).toHaveBeenCalledWith(updatedPrompt)
    })

    it('should not call callback on error', async () => {
      mockPromptAPI.vote.mockRejectedValue(new Error('Server error'))

      const { result } = renderHook(() => 
        useVote(mockPrompt.id, mockPrompt.votes, null, mockCallback)
      )

      await act(async () => {
        try {
          await result.current.handleVote('toggle')
        } catch (error) {
          // Expected to throw
        }
      })

      expect(mockCallback).not.toHaveBeenCalled()
    })
  })

  describe('Database backend', () => {
    let mockBrowserAPI

    beforeEach(async () => {
      // Mock config to use database
      vi.doMock('../../config.js', () => ({
        USE_DATABASE_INSTEAD_OF_JSON_FILE: true
      }))

      const { promptsAPI } = await import('../../api/browserEndpoints.js')
      mockBrowserAPI = promptsAPI
    })

    it('should use browser endpoints when configured', async () => {
      mockBrowserAPI.vote.mockResolvedValue({
        id: 1,
        votes: 6,
        userVote: 'up'
      })

      const { result } = renderHook(() => 
        useVote(mockPrompt.id, mockPrompt.votes, null)
      )

      await act(async () => {
        await result.current.handleVote('toggle')
      })

      expect(mockBrowserAPI.vote).toHaveBeenCalledWith(1, 1, 'toggle')
    })
  })
})