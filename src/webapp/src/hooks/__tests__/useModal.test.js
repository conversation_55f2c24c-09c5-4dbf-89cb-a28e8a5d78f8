import { renderHook, act } from '@testing-library/react'
import { fireEvent } from '@testing-library/react'
import useModal, { useMultiModal, useConfirmModal, useDrawer } from '../useModal'

// Mock the useKeyboard hook
jest.mock('../useKeyboard', () => ({
  useEscapeKey: jest.fn()
}))

describe('useModal', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    // Reset body overflow style
    document.body.style.overflow = ''
  })

  describe('basic functionality', () => {
    it('initializes with closed state by default', () => {
      const { result } = renderHook(() => useModal())
      
      expect(result.current.isOpen).toBe(false)
      expect(result.current.isClosing).toBe(false)
    })

    it('initializes with open state when specified', () => {
      const { result } = renderHook(() => useModal({ initialOpen: true }))
      
      expect(result.current.isOpen).toBe(true)
    })

    it('opens modal when open is called', () => {
      const { result } = renderHook(() => useModal())
      
      act(() => {
        result.current.open()
      })
      
      expect(result.current.isOpen).toBe(true)
      expect(result.current.isClosing).toBe(false)
    })

    it('closes modal when close is called', async () => {
      const { result } = renderHook(() => useModal({ initialOpen: true }))
      
      act(() => {
        result.current.close()
      })
      
      expect(result.current.isClosing).toBe(true)
      
      // Wait for close animation
      await act(async () => {
        await new Promise(resolve => setTimeout(resolve, 200))
      })
      
      expect(result.current.isOpen).toBe(false)
      expect(result.current.isClosing).toBe(false)
    })

    it('toggles modal state', () => {
      const { result } = renderHook(() => useModal())
      
      act(() => {
        result.current.toggle()
      })
      
      expect(result.current.isOpen).toBe(true)
      
      act(() => {
        result.current.toggle()
      })
      
      expect(result.current.isClosing).toBe(true)
    })
  })

  describe('callbacks', () => {
    it('calls onOpen when modal opens', () => {
      const onOpen = jest.fn()
      const { result } = renderHook(() => useModal({ onOpen }))
      
      act(() => {
        result.current.open('test data')
      })
      
      expect(onOpen).toHaveBeenCalledWith('test data')
    })

    it('calls onClose when modal closes', async () => {
      const onClose = jest.fn()
      const { result } = renderHook(() => useModal({ 
        initialOpen: true,
        onClose 
      }))
      
      act(() => {
        result.current.close('close data')
      })
      
      await act(async () => {
        await new Promise(resolve => setTimeout(resolve, 200))
      })
      
      expect(onClose).toHaveBeenCalledWith('close data')
    })
  })

  describe('outside click handling', () => {
    it('closes modal on outside click when enabled', () => {
      const { result } = renderHook(() => useModal({ 
        initialOpen: true,
        closeOnOutsideClick: true 
      }))
      
      const mockEvent = {
        target: document.createElement('div'),
        currentTarget: document.createElement('div')
      }
      mockEvent.target = mockEvent.currentTarget
      
      act(() => {
        result.current.handleOutsideClick(mockEvent)
      })
      
      expect(result.current.isClosing).toBe(true)
    })

    it('does not close modal on outside click when disabled', () => {
      const { result } = renderHook(() => useModal({ 
        initialOpen: true,
        closeOnOutsideClick: false 
      }))
      
      const mockEvent = {
        target: document.createElement('div'),
        currentTarget: document.createElement('div')
      }
      mockEvent.target = mockEvent.currentTarget
      
      act(() => {
        result.current.handleOutsideClick(mockEvent)
      })
      
      expect(result.current.isOpen).toBe(true)
      expect(result.current.isClosing).toBe(false)
    })
  })

  describe('body scroll prevention', () => {
    it('prevents body scroll when modal is open', () => {
      const { result } = renderHook(() => useModal({ preventBodyScroll: true }))
      
      act(() => {
        result.current.open()
      })
      
      expect(document.body.style.overflow).toBe('hidden')
    })

    it('restores body scroll when modal closes', async () => {
      document.body.style.overflow = 'auto'
      const { result } = renderHook(() => useModal({ 
        initialOpen: true,
        preventBodyScroll: true 
      }))
      
      act(() => {
        result.current.close()
      })
      
      await act(async () => {
        await new Promise(resolve => setTimeout(resolve, 200))
      })
      
      expect(document.body.style.overflow).toBe('auto')
    })

    it('does not affect body scroll when disabled', () => {
      const originalOverflow = document.body.style.overflow
      const { result } = renderHook(() => useModal({ preventBodyScroll: false }))
      
      act(() => {
        result.current.open()
      })
      
      expect(document.body.style.overflow).toBe(originalOverflow)
    })
  })
})

describe('useMultiModal', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    document.body.style.overflow = ''
  })

  it('manages multiple modals independently', () => {
    const { result } = renderHook(() => useMultiModal(['modal1', 'modal2']))
    
    act(() => {
      result.current.modalControls.modal1.open()
    })
    
    expect(result.current.modalControls.modal1.isOpen).toBe(true)
    expect(result.current.modalControls.modal2.isOpen).toBe(false)
    
    act(() => {
      result.current.modalControls.modal2.open()
    })
    
    expect(result.current.modalControls.modal1.isOpen).toBe(true)
    expect(result.current.modalControls.modal2.isOpen).toBe(true)
  })

  it('closes all modals', async () => {
    const { result } = renderHook(() => useMultiModal(['modal1', 'modal2']))
    
    act(() => {
      result.current.modalControls.modal1.open()
      result.current.modalControls.modal2.open()
    })
    
    act(() => {
      result.current.closeAllModals()
    })
    
    await act(async () => {
      await new Promise(resolve => setTimeout(resolve, 200))
    })
    
    expect(result.current.modalControls.modal1.isOpen).toBe(false)
    expect(result.current.modalControls.modal2.isOpen).toBe(false)
  })

  it('tracks open modals correctly', () => {
    const { result } = renderHook(() => useMultiModal(['modal1', 'modal2', 'modal3']))
    
    act(() => {
      result.current.modalControls.modal1.open()
      result.current.modalControls.modal3.open()
    })
    
    expect(result.current.openModals).toEqual(expect.arrayContaining(['modal1', 'modal3']))
    expect(result.current.hasOpenModals).toBe(true)
  })
})

describe('useConfirmModal', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('opens confirmation modal with custom config', () => {
    const { result } = renderHook(() => useConfirmModal())
    
    act(() => {
      result.current.confirm({
        title: 'Delete Item',
        message: 'Are you sure you want to delete this item?',
        confirmText: 'Delete',
        cancelText: 'Keep'
      })
    })
    
    expect(result.current.isOpen).toBe(true)
    expect(result.current.config.title).toBe('Delete Item')
    expect(result.current.config.confirmText).toBe('Delete')
  })

  it('calls onConfirm when confirmed', () => {
    const onConfirm = jest.fn()
    const { result } = renderHook(() => useConfirmModal())
    
    act(() => {
      result.current.confirm({ onConfirm })
    })
    
    act(() => {
      result.current.handleConfirm()
    })
    
    expect(onConfirm).toHaveBeenCalled()
  })

  it('calls onCancel when cancelled', () => {
    const onCancel = jest.fn()
    const { result } = renderHook(() => useConfirmModal())
    
    act(() => {
      result.current.confirm({ onCancel })
    })
    
    act(() => {
      result.current.handleCancel()
    })
    
    expect(onCancel).toHaveBeenCalled()
  })
})

describe('useDrawer', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('provides drawer-specific methods', () => {
    const { result } = renderHook(() => useDrawer())
    
    expect(typeof result.current.openDrawer).toBe('function')
    expect(typeof result.current.closeDrawer).toBe('function')
    expect(typeof result.current.toggleDrawer).toBe('function')
  })

  it('does not prevent body scroll by default', () => {
    const { result } = renderHook(() => useDrawer())
    
    act(() => {
      result.current.openDrawer()
    })
    
    expect(document.body.style.overflow).not.toBe('hidden')
  })
})