import { renderHook, act } from '@testing-library/react'
import useA<PERSON><PERSON><PERSON>y, { useAIQueryStreaming, useAIQuerySimple } from '../useAIQuery'

// Mock localStorage
const mockLocalStorage = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn()
}
Object.defineProperty(window, 'localStorage', { value: mockLocalStorage })

// Mock fetch
global.fetch = jest.fn()

// Mock AbortController
global.AbortController = jest.fn(() => ({
  abort: jest.fn(),
  signal: { aborted: false }
}))

describe('useAIQuery', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    mockLocalStorage.getItem.mockReturnValue(null)
  })

  describe('initialization', () => {
    it('initializes with empty state', () => {
      const { result } = renderHook(() => useAIQuery())
      
      expect(result.current.messages).toEqual([])
      expect(result.current.query).toBe('')
      expect(result.current.isLoading).toBe(false)
      expect(result.current.error).toBe(null)
      expect(result.current.hasMessages).toBe(false)
    })

    it('initializes with initial messages', () => {
      const initialMessages = [
        { id: 1, type: 'user', message: 'Hello' }
      ]
      
      const { result } = renderHook(() => useAIQuery({ initialMessages }))
      
      expect(result.current.messages).toEqual(initialMessages)
      expect(result.current.hasMessages).toBe(true)
    })

    it('loads persisted messages from localStorage', () => {
      const persistedMessages = [
        { id: 1, type: 'user', message: 'Persisted message' }
      ]
      mockLocalStorage.getItem.mockReturnValue(JSON.stringify(persistedMessages))
      
      const { result } = renderHook(() => useAIQuery({ 
        persistMessages: true,
        storageKey: 'test-messages'
      }))
      
      expect(mockLocalStorage.getItem).toHaveBeenCalledWith('test-messages')
      expect(result.current.messages).toEqual(persistedMessages)
    })
  })

  describe('message management', () => {
    it('adds a message', () => {
      const { result } = renderHook(() => useAIQuery())
      
      act(() => {
        result.current.addMessage({
          type: 'user',
          message: 'Test message'
        })
      })
      
      expect(result.current.messages).toHaveLength(1)
      expect(result.current.messages[0].message).toBe('Test message')
      expect(result.current.messages[0].id).toBeDefined()
    })

    it('updates a message', () => {
      const { result } = renderHook(() => useAIQuery({
        initialMessages: [{ id: 1, type: 'user', message: 'Original' }]
      }))
      
      act(() => {
        result.current.updateMessage(1, { message: 'Updated' })
      })
      
      expect(result.current.messages[0].message).toBe('Updated')
    })

    it('removes a message', () => {
      const { result } = renderHook(() => useAIQuery({
        initialMessages: [
          { id: 1, type: 'user', message: 'Message 1' },
          { id: 2, type: 'user', message: 'Message 2' }
        ]
      }))
      
      act(() => {
        result.current.removeMessage(1)
      })
      
      expect(result.current.messages).toHaveLength(1)
      expect(result.current.messages[0].id).toBe(2)
    })

    it('clears all messages', () => {
      const { result } = renderHook(() => useAIQuery({
        initialMessages: [{ id: 1, type: 'user', message: 'Test' }]
      }))
      
      act(() => {
        result.current.clearMessages()
      })
      
      expect(result.current.messages).toHaveLength(0)
    })

    it('trims messages when exceeding max limit', () => {
      const { result } = renderHook(() => useAIQuery({ maxMessages: 2 }))
      
      act(() => {
        result.current.addMessage({ type: 'user', message: 'Message 1' })
        result.current.addMessage({ type: 'user', message: 'Message 2' })
        result.current.addMessage({ type: 'user', message: 'Message 3' })
      })
      
      expect(result.current.messages).toHaveLength(2)
      expect(result.current.messages[0].message).toBe('Message 2')
      expect(result.current.messages[1].message).toBe('Message 3')
    })
  })

  describe('query submission', () => {
    it('submits a query successfully', async () => {
      const onSubmit = jest.fn().mockResolvedValue('AI response')
      const { result } = renderHook(() => useAIQuery({ onSubmit }))
      
      act(() => {
        result.current.setQuery('Test query')
      })
      
      await act(async () => {
        await result.current.submitQuery()
      })
      
      expect(result.current.messages).toHaveLength(2) // User + AI message
      expect(result.current.messages[0].type).toBe('user')
      expect(result.current.messages[0].message).toBe('Test query')
      expect(result.current.messages[1].type).toBe('ai')
      expect(result.current.query).toBe('') // Query cleared after submission
      expect(onSubmit).toHaveBeenCalledWith('Test query', expect.any(Object))
    })

    it('handles query submission with custom text', async () => {
      const onSubmit = jest.fn().mockResolvedValue('AI response')
      const { result } = renderHook(() => useAIQuery({ onSubmit }))
      
      await act(async () => {
        await result.current.submitQuery('Custom query')
      })
      
      expect(result.current.messages[0].message).toBe('Custom query')
      expect(onSubmit).toHaveBeenCalledWith('Custom query', expect.any(Object))
    })

    it('prevents submission when loading', async () => {
      const onSubmit = jest.fn().mockImplementation(() => new Promise(() => {})) // Never resolves
      const { result } = renderHook(() => useAIQuery({ onSubmit }))
      
      act(() => {
        result.current.setQuery('Query 1')
      })
      
      // Start first submission
      act(() => {
        result.current.submitQuery()
      })
      
      expect(result.current.isLoading).toBe(true)
      
      // Try to submit again while loading
      act(() => {
        result.current.setQuery('Query 2')
      })
      
      await act(async () => {
        await result.current.submitQuery()
      })
      
      expect(onSubmit).toHaveBeenCalledTimes(1) // Only called once
    })

    it('handles submission errors', async () => {
      const error = new Error('Submission failed')
      const onSubmit = jest.fn().mockRejectedValue(error)
      const onError = jest.fn()
      
      const { result } = renderHook(() => useAIQuery({ onSubmit, onError }))
      
      act(() => {
        result.current.setQuery('Test query')
      })
      
      await act(async () => {
        await result.current.submitQuery()
      })
      
      expect(result.current.error).toBe('Submission failed')
      expect(result.current.isLoading).toBe(false)
      expect(onError).toHaveBeenCalledWith(error, 'Test query', expect.any(Number))
    })
  })

  describe('query cancellation', () => {
    it('cancels ongoing query', async () => {
      const abortController = { abort: jest.fn() }
      global.AbortController.mockReturnValue(abortController)
      
      const onSubmit = jest.fn().mockImplementation(() => new Promise(() => {}))
      const { result } = renderHook(() => useAIQuery({ onSubmit }))
      
      act(() => {
        result.current.setQuery('Test query')
      })
      
      // Start submission
      act(() => {
        result.current.submitQuery()
      })
      
      expect(result.current.isLoading).toBe(true)
      
      // Cancel
      act(() => {
        result.current.cancelQuery()
      })
      
      expect(abortController.abort).toHaveBeenCalled()
      expect(result.current.isLoading).toBe(false)
    })
  })

  describe('message persistence', () => {
    it('persists messages to localStorage', () => {
      const { result } = renderHook(() => useAIQuery({ 
        persistMessages: true,
        storageKey: 'test-key'
      }))
      
      act(() => {
        result.current.addMessage({ type: 'user', message: 'Test' })
      })
      
      expect(mockLocalStorage.setItem).toHaveBeenCalledWith(
        'test-key',
        expect.stringContaining('Test')
      )
    })

    it('clears persisted messages', () => {
      const { result } = renderHook(() => useAIQuery({ 
        persistMessages: true,
        storageKey: 'test-key',
        initialMessages: [{ id: 1, type: 'user', message: 'Test' }]
      }))
      
      act(() => {
        result.current.clearMessages()
      })
      
      expect(mockLocalStorage.removeItem).toHaveBeenCalledWith('test-key')
    })
  })

  describe('utility functions', () => {
    it('regenerates response', async () => {
      const onSubmit = jest.fn().mockResolvedValue('New response')
      const initialMessages = [
        { id: 1, type: 'user', message: 'User query' },
        { id: 2, type: 'ai', message: 'AI response' }
      ]
      
      const { result } = renderHook(() => useAIQuery({ 
        onSubmit,
        initialMessages 
      }))
      
      await act(async () => {
        await result.current.regenerateResponse(2)
      })
      
      expect(onSubmit).toHaveBeenCalledWith('User query', expect.any(Object))
    })

    it('retries last query', async () => {
      const onSubmit = jest.fn().mockResolvedValue('Retry response')
      const initialMessages = [
        { id: 1, type: 'user', message: 'Last query' },
        { id: 2, type: 'ai', message: 'AI response' }
      ]
      
      const { result } = renderHook(() => useAIQuery({ 
        onSubmit,
        initialMessages 
      }))
      
      await act(async () => {
        await result.current.retryLastQuery()
      })
      
      expect(onSubmit).toHaveBeenCalledWith('Last query', expect.any(Object))
    })

    it('gets message history in correct format', () => {
      const messages = [
        { id: 1, type: 'user', message: 'User message' },
        { id: 2, type: 'ai', message: 'AI message' }
      ]
      
      const { result } = renderHook(() => useAIQuery({ initialMessages: messages }))
      
      const history = result.current.getMessageHistory()
      
      expect(history).toEqual([
        { role: 'user', content: 'User message' },
        { role: 'assistant', content: 'AI message' }
      ])
    })
  })
})

describe('useAIQueryStreaming', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('makes streaming API call', async () => {
    const mockResponse = {
      ok: true,
      body: {
        getReader: jest.fn().mockReturnValue({
          read: jest.fn()
            .mockResolvedValueOnce({ done: false, value: new TextEncoder().encode('Hello') })
            .mockResolvedValueOnce({ done: true })
        })
      }
    }
    
    fetch.mockResolvedValue(mockResponse)
    
    const { result } = renderHook(() => useAIQueryStreaming('/api/chat'))
    
    act(() => {
      result.current.setQuery('Test query')
    })
    
    await act(async () => {
      await result.current.submitQuery()
    })
    
    expect(fetch).toHaveBeenCalledWith('/api/chat', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: expect.stringContaining('Test query'),
      signal: expect.any(Object)
    })
  })
})

describe('useAIQuerySimple', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('makes simple API call', async () => {
    const mockResponse = {
      ok: true,
      json: jest.fn().mockResolvedValue({ response: 'Simple response' })
    }
    
    fetch.mockResolvedValue(mockResponse)
    
    const { result } = renderHook(() => useAIQuerySimple('/api/simple'))
    
    act(() => {
      result.current.setQuery('Test query')
    })
    
    await act(async () => {
      await result.current.submitQuery()
    })
    
    expect(fetch).toHaveBeenCalledWith('/api/simple', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: expect.stringContaining('Test query'),
      signal: expect.any(Object)
    })
    
    expect(result.current.messages[1].message).toBe('Simple response')
  })
})