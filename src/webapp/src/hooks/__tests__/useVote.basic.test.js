import { renderHook } from '@testing-library/react'
import { vi, describe, it, expect, beforeEach } from 'vitest'
import { useVote } from '../useVote'

// Mock the auth context
const mockAuth = vi.fn(() => ({
  user: { id: 1, username: 'testuser' },
  isAuthenticated: true
}))

vi.mock('../../auth/AuthContext', () => ({
  useAuth: mockAuth
}))

// Mock config
vi.mock('../../config.js', () => ({
  default: {
    USE_DATABASE_INSTEAD_OF_JSON_FILE: false
  }
}))

describe('useVote basic functionality', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    mockAuth.mockReturnValue({
      user: { id: 1, username: 'testuser' },
      isAuthenticated: true
    })
  })

  it('should initialize with correct default values', () => {
    const { result } = renderHook(() => 
      useVote(1, 5, null)
    )

    expect(result.current.votes).toBe(5)
    expect(result.current.userVote).toBe(null)
    expect(result.current.isVoting).toBe(false)
    expect(result.current.hasUserVoted).toBe(false)
    expect(result.current.error).toBe(null)
    expect(result.current.canVote).toBe(true)
  })

  it('should initialize with user vote when provided', () => {
    const { result } = renderHook(() => 
      useVote(1, 5, 'up')
    )

    expect(result.current.userVote).toBe('up')
    expect(result.current.hasUserVoted).toBe(true)
  })

  it('should set canVote to false when not authenticated', () => {
    mockAuth.mockReturnValue({
      user: null,
      isAuthenticated: false
    })

    const { result } = renderHook(() => 
      useVote(1, 5, null)
    )

    expect(result.current.canVote).toBe(false)
  })

  it('should provide voting functions', () => {
    const { result } = renderHook(() => 
      useVote(1, 5, null)
    )

    expect(typeof result.current.handleVote).toBe('function')
    expect(typeof result.current.upvote).toBe('function')
    expect(typeof result.current.downvote).toBe('function')
    expect(typeof result.current.toggleVote).toBe('function')
    expect(typeof result.current.clearError).toBe('function')
  })
})