import { renderHook, act } from '@testing-library/react'
import useInputOutputExamples, { useExampleEditor } from '../useInputOutputExamples'

const mockInitialExamples = [
  {
    id: '1',
    input: 'Test input 1',
    output: 'Test output 1'
  },
  {
    id: '2', 
    input: 'Test input 2',
    output: 'Test output 2'
  }
]

describe('useInputOutputExamples', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('initialization', () => {
    it('initializes with provided examples', () => {
      const { result } = renderHook(() => useInputOutputExamples(mockInitialExamples))
      
      expect(result.current.examples).toEqual(mockInitialExamples)
      expect(result.current.hasChanges).toBe(false)
      expect(result.current.isEmpty).toBe(false)
      expect(result.current.canAddMore).toBe(true)
    })

    it('initializes with empty array by default', () => {
      const { result } = renderHook(() => useInputOutputExamples())
      
      expect(result.current.examples).toEqual([])
      expect(result.current.isEmpty).toBe(true)
    })
  })

  describe('addExample', () => {
    it('adds a new example successfully', () => {
      const onAdd = jest.fn()
      const { result } = renderHook(() => useInputOutputExamples([], { onAdd }))
      
      act(() => {
        const success = result.current.addExample({
          input: 'New input',
          output: 'New output'
        })
        expect(success).toBe(true)
      })
      
      expect(result.current.examples).toHaveLength(1)
      expect(result.current.examples[0].input).toBe('New input')
      expect(result.current.hasChanges).toBe(true)
      expect(onAdd).toHaveBeenCalled()
    })

    it('prevents adding beyond max limit', () => {
      const { result } = renderHook(() => useInputOutputExamples([], { maxExamples: 2 }))
      
      act(() => {
        result.current.addExample({ input: 'Test 1', output: 'Output 1' })
        result.current.addExample({ input: 'Test 2', output: 'Output 2' })
        const success = result.current.addExample({ input: 'Test 3', output: 'Output 3' })
        expect(success).toBe(false)
      })
      
      expect(result.current.examples).toHaveLength(2)
      expect(result.current.errors.general).toContain('Maximum 2 examples allowed')
    })

    it('validates example before adding', () => {
      const { result } = renderHook(() => useInputOutputExamples([]))
      
      act(() => {
        const success = result.current.addExample({
          input: '', // Invalid - empty input
          output: 'Valid output'
        })
        expect(success).toBe(false)
      })
      
      expect(result.current.examples).toHaveLength(0)
      expect(result.current.hasErrors).toBe(true)
    })
  })

  describe('updateExample', () => {
    it('updates an existing example', () => {
      const onEdit = jest.fn()
      const { result } = renderHook(() => useInputOutputExamples(mockInitialExamples, { onEdit }))
      
      act(() => {
        const success = result.current.updateExample(0, {
          input: 'Updated input'
        })
        expect(success).toBe(true)
      })
      
      expect(result.current.examples[0].input).toBe('Updated input')
      expect(result.current.examples[0].output).toBe('Test output 1') // Unchanged
      expect(result.current.hasChanges).toBe(true)
      expect(onEdit).toHaveBeenCalled()
    })

    it('validates before updating', () => {
      const { result } = renderHook(() => useInputOutputExamples(mockInitialExamples))
      
      act(() => {
        const success = result.current.updateExample(0, {
          input: '' // Invalid - empty input
        })
        expect(success).toBe(false)
      })
      
      expect(result.current.examples[0].input).toBe('Test input 1') // Unchanged
      expect(result.current.hasErrors).toBe(true)
    })

    it('handles invalid index', () => {
      const { result } = renderHook(() => useInputOutputExamples(mockInitialExamples))
      
      act(() => {
        const success = result.current.updateExample(10, { input: 'Test' })
        expect(success).toBe(false)
      })
      
      expect(result.current.hasChanges).toBe(false)
    })
  })

  describe('deleteExample', () => {
    it('deletes an example', () => {
      const onDelete = jest.fn()
      const { result } = renderHook(() => useInputOutputExamples(mockInitialExamples, { onDelete }))
      
      act(() => {
        const success = result.current.deleteExample(0)
        expect(success).toBe(true)
      })
      
      expect(result.current.examples).toHaveLength(1)
      expect(result.current.examples[0].id).toBe('2')
      expect(result.current.hasChanges).toBe(true)
      expect(onDelete).toHaveBeenCalledWith(mockInitialExamples[0], 0, expect.any(Array))
    })

    it('handles invalid index', () => {
      const { result } = renderHook(() => useInputOutputExamples(mockInitialExamples))
      
      act(() => {
        const success = result.current.deleteExample(10)
        expect(success).toBe(false)
      })
      
      expect(result.current.examples).toHaveLength(2)
      expect(result.current.hasChanges).toBe(false)
    })
  })

  describe('moveExample', () => {
    it('moves example to new position', () => {
      const { result } = renderHook(() => useInputOutputExamples(mockInitialExamples))
      
      act(() => {
        const success = result.current.moveExample(0, 1)
        expect(success).toBe(true)
      })
      
      expect(result.current.examples[0].id).toBe('2')
      expect(result.current.examples[1].id).toBe('1')
      expect(result.current.hasChanges).toBe(true)
    })

    it('handles invalid indices', () => {
      const { result } = renderHook(() => useInputOutputExamples(mockInitialExamples))
      
      act(() => {
        const success = result.current.moveExample(0, 10)
        expect(success).toBe(false)
      })
      
      expect(result.current.hasChanges).toBe(false)
    })
  })

  describe('duplicateExample', () => {
    it('duplicates an example', () => {
      const { result } = renderHook(() => useInputOutputExamples([mockInitialExamples[0]]))
      
      act(() => {
        const success = result.current.duplicateExample(0)
        expect(success).toBe(true)
      })
      
      expect(result.current.examples).toHaveLength(2)
      expect(result.current.examples[1].input).toBe('Test input 1 (copy)')
      expect(result.current.examples[1].output).toBe('Test output 1')
      expect(result.current.hasChanges).toBe(true)
    })

    it('respects max examples limit', () => {
      const { result } = renderHook(() => useInputOutputExamples(mockInitialExamples, { maxExamples: 2 }))
      
      act(() => {
        const success = result.current.duplicateExample(0)
        expect(success).toBe(false)
      })
      
      expect(result.current.examples).toHaveLength(2)
      expect(result.current.errors.general).toContain('Maximum 2 examples allowed')
    })
  })

  describe('utility functions', () => {
    it('clears all examples', () => {
      const { result } = renderHook(() => useInputOutputExamples(mockInitialExamples))
      
      act(() => {
        result.current.clearAllExamples()
      })
      
      expect(result.current.examples).toHaveLength(0)
      expect(result.current.hasChanges).toBe(true)
      expect(result.current.isEmpty).toBe(true)
    })

    it('resets to initial state', () => {
      const { result } = renderHook(() => useInputOutputExamples(mockInitialExamples))
      
      act(() => {
        result.current.addExample({ input: 'New', output: 'New' })
        result.current.resetToInitial()
      })
      
      expect(result.current.examples).toEqual(mockInitialExamples)
      expect(result.current.hasChanges).toBe(false)
    })

    it('saves changes', async () => {
      const onUpdate = jest.fn().mockResolvedValue()
      const { result } = renderHook(() => useInputOutputExamples(mockInitialExamples, { onUpdate }))
      
      act(() => {
        result.current.addExample({ input: 'New', output: 'New' })
      })
      
      await act(async () => {
        const success = await result.current.save()
        expect(success).toBe(true)
      })
      
      expect(onUpdate).toHaveBeenCalled()
      expect(result.current.hasChanges).toBe(false)
    })

    it('gets example errors', () => {
      const { result } = renderHook(() => useInputOutputExamples([]))
      
      act(() => {
        result.current.addExample({ input: '', output: 'Valid' }) // Invalid
      })
      
      const errors = result.current.getExampleErrors(0)
      expect(errors).toContain('Input is required')
    })
  })

  describe('validation', () => {
    it('validates input length', () => {
      const { result } = renderHook(() => useInputOutputExamples([]))
      
      act(() => {
        const success = result.current.addExample({
          input: 'a'.repeat(1001), // Too long
          output: 'Valid output'
        })
        expect(success).toBe(false)
      })
      
      expect(result.current.hasErrors).toBe(true)
    })

    it('validates output length', () => {
      const { result } = renderHook(() => useInputOutputExamples([]))
      
      act(() => {
        const success = result.current.addExample({
          input: 'Valid input',
          output: 'a'.repeat(2001) // Too long
        })
        expect(success).toBe(false)
      })
      
      expect(result.current.hasErrors).toBe(true)
    })

    it('uses custom validation', () => {
      const validateExample = jest.fn().mockReturnValue(['Custom error'])
      const { result } = renderHook(() => useInputOutputExamples([], { validateExample }))
      
      act(() => {
        const success = result.current.addExample({
          input: 'Valid input',
          output: 'Valid output'
        })
        expect(success).toBe(false)
      })
      
      expect(validateExample).toHaveBeenCalled()
      expect(result.current.hasErrors).toBe(true)
    })
  })
})

describe('useExampleEditor', () => {
  const initialExample = { input: 'Test input', output: 'Test output' }

  it('initializes with provided example', () => {
    const { result } = renderHook(() => useExampleEditor(initialExample))
    
    expect(result.current.example).toEqual(initialExample)
    expect(result.current.isDirty).toBe(false)
    expect(result.current.isValid).toBe(true)
  })

  it('updates input and marks as dirty', () => {
    const { result } = renderHook(() => useExampleEditor(initialExample))
    
    act(() => {
      result.current.updateInput('New input')
    })
    
    expect(result.current.example.input).toBe('New input')
    expect(result.current.isDirty).toBe(true)
  })

  it('updates output and marks as dirty', () => {
    const { result } = renderHook(() => useExampleEditor(initialExample))
    
    act(() => {
      result.current.updateOutput('New output')
    })
    
    expect(result.current.example.output).toBe('New output')
    expect(result.current.isDirty).toBe(true)
  })

  it('validates example', () => {
    const { result } = renderHook(() => useExampleEditor({ input: '', output: '' }))
    
    act(() => {
      const isValid = result.current.validate()
      expect(isValid).toBe(false)
    })
    
    expect(result.current.errors).toContain('Input is required')
    expect(result.current.errors).toContain('Output is required')
    expect(result.current.isValid).toBe(false)
  })

  it('resets to initial state', () => {
    const { result } = renderHook(() => useExampleEditor(initialExample))
    
    act(() => {
      result.current.updateInput('Changed')
      result.current.reset()
    })
    
    expect(result.current.example).toEqual(initialExample)
    expect(result.current.isDirty).toBe(false)
    expect(result.current.errors).toHaveLength(0)
  })

  it('saves valid example', () => {
    const onSave = jest.fn()
    const { result } = renderHook(() => useExampleEditor(initialExample, { onSave }))
    
    act(() => {
      const success = result.current.save()
      expect(success).toBe(true)
    })
    
    expect(onSave).toHaveBeenCalledWith(initialExample)
    expect(result.current.isDirty).toBe(false)
  })

  it('does not save invalid example', () => {
    const onSave = jest.fn()
    const { result } = renderHook(() => useExampleEditor({ input: '', output: '' }, { onSave }))
    
    act(() => {
      const success = result.current.save()
      expect(success).toBe(false)
    })
    
    expect(onSave).not.toHaveBeenCalled()
    expect(result.current.isDirty).toBe(false)
  })
})