import { renderHook } from '@testing-library/react'
import { fireEvent } from '@testing-library/react'
import { vi } from 'vitest'
import useKeyboard, { useEscapeKey, useEnterKey, useArrowKeys, useHotkeys } from '../useKeyboard'

describe('useKeyboard', () => {
  afterEach(() => {
    // Clean up any event listeners
    vi.clearAllMocks()
  })

  describe('useKeyboard basic functionality', () => {
    it('calls handler when specified key is pressed', () => {
      const escapeHandler = vi.fn()
      const enterHandler = vi.fn()
      
      renderHook(() => useKeyboard({
        keyHandlers: {
          'Escape': escapeHandler,
          'Enter': enterHandler
        }
      }))
      
      fireEvent.keyDown(window, { key: 'Escape' })
      expect(escapeHandler).toHaveBeenCalledTimes(1)
      
      fireEvent.keyDown(window, { key: 'Enter' })
      expect(enterHandler).toHaveBeenCalledTimes(1)
    })

    it('does not call handler when different key is pressed', () => {
      const handler = vi.fn()
      
      renderHook(() => useKeyboard({
        keyHandlers: {
          'Escape': handler
        }
      }))
      
      fireEvent.keyDown(window, { key: 'Enter' })
      expect(handler).not.toHaveBeenCalled()
    })

    it('does not call handler when disabled', () => {
      const handler = vi.fn()
      
      renderHook(() => useKeyboard({
        keyHandlers: {
          'Escape': handler
        },
        enabled: false
      }))
      
      fireEvent.keyDown(window, { key: 'Escape' })
      expect(handler).not.toHaveBeenCalled()
    })

    it('passes event object to handler', () => {
      const handler = vi.fn()
      
      renderHook(() => useKeyboard({
        keyHandlers: {
          'Escape': handler
        }
      }))
      
      const event = new KeyboardEvent('keydown', { key: 'Escape' })
      fireEvent(window, event)
      
      expect(handler).toHaveBeenCalledWith(expect.objectContaining({
        key: 'Escape'
      }))
    })

    it('prevents default when preventDefault is true', () => {
      const handler = vi.fn()
      const preventDefault = vi.fn()
      
      renderHook(() => useKeyboard({
        keyHandlers: {
          'Escape': handler
        },
        preventDefault: true
      }))
      
      const event = new KeyboardEvent('keydown', { key: 'Escape' })
      event.preventDefault = preventDefault
      fireEvent(window, event)
      
      expect(preventDefault).toHaveBeenCalled()
    })
  })

  describe('useEscapeKey', () => {
    it('calls handler when Escape is pressed', () => {
      const handler = vi.fn()
      
      renderHook(() => useEscapeKey(handler))
      
      fireEvent.keyDown(window, { key: 'Escape' })
      expect(handler).toHaveBeenCalledTimes(1)
    })

    it('does not call handler when disabled', () => {
      const handler = vi.fn()
      
      renderHook(() => useEscapeKey(handler, false))
      
      fireEvent.keyDown(window, { key: 'Escape' })
      expect(handler).not.toHaveBeenCalled()
    })
  })

  describe('useEnterKey', () => {
    it('calls handler when Enter is pressed', () => {
      const handler = vi.fn()
      
      renderHook(() => useEnterKey(handler))
      
      fireEvent.keyDown(window, { key: 'Enter' })
      expect(handler).toHaveBeenCalledTimes(1)
    })

    it('ignores Shift+Enter when preventShiftEnter is true', () => {
      const handler = vi.fn()
      
      renderHook(() => useEnterKey(handler, true))
      
      fireEvent.keyDown(window, { key: 'Enter', shiftKey: true })
      expect(handler).not.toHaveBeenCalled()
    })

    it('allows Shift+Enter when preventShiftEnter is false', () => {
      const handler = vi.fn()
      
      renderHook(() => useEnterKey(handler, false))
      
      fireEvent.keyDown(window, { key: 'Enter', shiftKey: true })
      expect(handler).toHaveBeenCalledTimes(1)
    })
  })

  describe('useArrowKeys', () => {
    it('calls appropriate handlers for arrow keys', () => {
      const onUp = vi.fn()
      const onDown = vi.fn()
      const onLeft = vi.fn()
      const onRight = vi.fn()
      
      renderHook(() => useArrowKeys({ onUp, onDown, onLeft, onRight }))
      
      fireEvent.keyDown(window, { key: 'ArrowUp' })
      expect(onUp).toHaveBeenCalledTimes(1)
      
      fireEvent.keyDown(window, { key: 'ArrowDown' })
      expect(onDown).toHaveBeenCalledTimes(1)
      
      fireEvent.keyDown(window, { key: 'ArrowLeft' })
      expect(onLeft).toHaveBeenCalledTimes(1)
      
      fireEvent.keyDown(window, { key: 'ArrowRight' })
      expect(onRight).toHaveBeenCalledTimes(1)
    })

    it('only handles specified arrow keys', () => {
      const onUp = vi.fn()
      
      renderHook(() => useArrowKeys({ onUp }))
      
      fireEvent.keyDown(window, { key: 'ArrowUp' })
      expect(onUp).toHaveBeenCalledTimes(1)
      
      fireEvent.keyDown(window, { key: 'ArrowDown' })
      // onDown was not provided, so no handler should be called
    })
  })

  describe('useHotkeys', () => {
    it('calls handler for ctrl+key combinations', () => {
      const saveHandler = vi.fn()
      
      renderHook(() => useHotkeys({
        'ctrl+s': saveHandler
      }))
      
      fireEvent.keyDown(window, { key: 's', ctrlKey: true })
      expect(saveHandler).toHaveBeenCalledTimes(1)
    })

    it('calls handler for shift+key combinations', () => {
      const helpHandler = vi.fn()
      
      renderHook(() => useHotkeys({
        'shift+?': helpHandler
      }))
      
      fireEvent.keyDown(window, { key: '?', shiftKey: true })
      expect(helpHandler).toHaveBeenCalledTimes(1)
    })

    it('supports cmd+ as alias for ctrl+ on Mac', () => {
      const saveHandler = vi.fn()
      
      renderHook(() => useHotkeys({
        'cmd+s': saveHandler
      }))
      
      fireEvent.keyDown(window, { key: 's', metaKey: true })
      expect(saveHandler).toHaveBeenCalledTimes(1)
    })

    it('does not call handler when disabled', () => {
      const handler = vi.fn()
      
      renderHook(() => useHotkeys({
        'ctrl+s': handler
      }, false))
      
      fireEvent.keyDown(window, { key: 's', ctrlKey: true })
      expect(handler).not.toHaveBeenCalled()
    })

    it('prevents default for matched hotkeys', () => {
      const handler = vi.fn()
      const preventDefault = vi.fn()
      
      renderHook(() => useHotkeys({
        'ctrl+s': handler
      }))
      
      const event = new KeyboardEvent('keydown', { key: 's', ctrlKey: true })
      event.preventDefault = preventDefault
      fireEvent(window, event)
      
      expect(preventDefault).toHaveBeenCalled()
    })
  })

  describe('cleanup', () => {
    it('removes event listeners on unmount', () => {
      const removeEventListener = vi.spyOn(window, 'removeEventListener')
      const handler = vi.fn()
      
      const { unmount } = renderHook(() => useKeyboard({
        keyHandlers: {
          'Escape': handler
        }
      }))
      
      unmount()
      
      expect(removeEventListener).toHaveBeenCalledWith('keydown', expect.any(Function))
    })
  })
})