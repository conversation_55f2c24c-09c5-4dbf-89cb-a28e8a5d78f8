import { renderHook, act } from '@testing-library/react'
import { useFilters } from '../useFilters'

// Mock data for testing
const mockPrompts = [
  {
    id: 1,
    title: 'Advanced Investment Analysis',
    description: 'Comprehensive analysis framework for investment decisions',
    content: 'Detailed content about investment analysis with financial modeling',
    category: 'Investment Analysis',
    status: 'Published',
    difficulty: 'Advanced',
    author: 'PE Team',
    votes: 150,
    tags: ['investment', 'analysis', 'finance']
  },
  {
    id: 2,
    title: 'Basic Marketing Strategy',
    description: 'Simple marketing framework for beginners',
    content: 'Basic marketing concepts and strategies',
    category: 'Marketing',
    status: 'Approved',
    difficulty: 'Beginner',
    author: 'Marketing Team',
    votes: 75,
    tags: ['marketing', 'strategy', 'beginner']
  },
  {
    id: 3,
    title: 'Intermediate Financial Planning',
    description: 'Financial planning techniques for growing companies',
    content: 'Intermediate financial planning strategies and tools',
    category: 'Financial Planning',
    status: 'In Development',
    difficulty: 'Intermediate',
    author: 'Finance Team', 
    votes: 200,
    tags: ['finance', 'planning', 'growth']
  },
  {
    id: 4,
    title: 'Advanced Marketing Analytics',
    description: 'Deep dive into marketing analytics and metrics',
    content: 'Advanced analytics techniques for marketing professionals',
    category: 'Marketing',
    status: 'Published',
    difficulty: 'Advanced',
    author: 'Analytics Team',
    votes: 120,
    tags: ['marketing', 'analytics', 'metrics']
  }
]

const mockCategories = [
  { name: 'Investment Analysis', count: 1 },
  { name: 'Marketing', count: 2 },
  { name: 'Financial Planning', count: 1 }
]

describe('useFilters Hook', () => {
  
  describe('Basic Functionality', () => {
    test('should initialize with default filters', () => {
      const { result } = renderHook(() => useFilters(mockPrompts, mockCategories))
      
      expect(result.current.filters).toEqual({
        searchQuery: '',
        category: '',
        status: [],
        difficulty: [],
        author: '',
        sortBy: 'trending'
      })
      expect(result.current.filteredPrompts).toHaveLength(4)
      expect(result.current.activeFilterCount).toBe(0)
    })

    test('should initialize with custom initial filters', () => {
      const initialFilters = {
        searchQuery: 'investment',
        category: 'Marketing',
        sortBy: 'title'
      }
      
      const { result } = renderHook(() => 
        useFilters(mockPrompts, mockCategories, initialFilters)
      )
      
      expect(result.current.filters.searchQuery).toBe('investment')
      expect(result.current.filters.category).toBe('Marketing')
      expect(result.current.filters.sortBy).toBe('title')
    })

    test('should return all prompts when no filters applied', () => {
      const { result } = renderHook(() => useFilters(mockPrompts, mockCategories))
      
      expect(result.current.filteredPrompts).toHaveLength(4)
      expect(result.current.filteredPrompts).toEqual(
        expect.arrayContaining([
          expect.objectContaining({ id: 1 }),
          expect.objectContaining({ id: 2 }),
          expect.objectContaining({ id: 3 }),
          expect.objectContaining({ id: 4 })
        ])
      )
    })
  })

  describe('Search Functionality', () => {
    test('should filter by search query in title', async () => {
      const { result } = renderHook(() => useFilters(mockPrompts, mockCategories))
      
      act(() => {
        result.current.setFilter('searchQuery', 'investment')
      })

      // Wait for debounce
      await act(async () => {
        await new Promise(resolve => setTimeout(resolve, 350))
      })
      
      expect(result.current.filteredPrompts).toHaveLength(1)
      expect(result.current.filteredPrompts[0].title).toContain('Investment')
    })

    test('should filter by search query in description', async () => {
      const { result } = renderHook(() => useFilters(mockPrompts, mockCategories))
      
      act(() => {
        result.current.setFilter('searchQuery', 'framework')
      })

      await act(async () => {
        await new Promise(resolve => setTimeout(resolve, 350))
      })
      
      expect(result.current.filteredPrompts).toHaveLength(2)
    })

    test('should filter by search query in tags', async () => {
      const { result } = renderHook(() => useFilters(mockPrompts, mockCategories))
      
      act(() => {
        result.current.setFilter('searchQuery', 'analytics')
      })

      await act(async () => {
        await new Promise(resolve => setTimeout(resolve, 350))
      })
      
      expect(result.current.filteredPrompts).toHaveLength(1)
      expect(result.current.filteredPrompts[0].tags).toContain('analytics')
    })

    test('should handle multiple search terms (AND operation)', async () => {
      const { result } = renderHook(() => useFilters(mockPrompts, mockCategories))
      
      act(() => {
        result.current.setFilter('searchQuery', 'marketing advanced')
      })

      await act(async () => {
        await new Promise(resolve => setTimeout(resolve, 350))
      })
      
      expect(result.current.filteredPrompts).toHaveLength(1)
      expect(result.current.filteredPrompts[0].title).toBe('Advanced Marketing Analytics')
    })

    test('should debounce search input', () => {
      const { result } = renderHook(() => useFilters(mockPrompts, mockCategories))
      
      // Initial search query should not immediately affect debounced query
      act(() => {
        result.current.setFilter('searchQuery', 'test')
      })
      
      expect(result.current.filters.searchQuery).toBe('test')
      expect(result.current.debouncedSearchQuery).toBe('')
    })

    test('should handle whitespace-only search queries', async () => {
      const { result } = renderHook(() => useFilters(mockPrompts, mockCategories))
      
      act(() => {
        result.current.setFilter('searchQuery', '   ')
      })

      await act(async () => {
        await new Promise(resolve => setTimeout(resolve, 350))
      })
      
      // Should return all prompts when search is only whitespace
      expect(result.current.filteredPrompts).toHaveLength(4)
    })
  })

  describe('Category Filtering', () => {
    test('should filter by category', () => {
      const { result } = renderHook(() => useFilters(mockPrompts, mockCategories))
      
      act(() => {
        result.current.setFilter('category', 'Marketing')
      })
      
      expect(result.current.filteredPrompts).toHaveLength(2)
      result.current.filteredPrompts.forEach(prompt => {
        expect(prompt.category).toBe('Marketing')
      })
    })

    test('should handle case-insensitive category filtering', () => {
      const { result } = renderHook(() => useFilters(mockPrompts, mockCategories))
      
      act(() => {
        result.current.setFilter('category', 'marketing')
      })
      
      expect(result.current.filteredPrompts).toHaveLength(2)
    })
  })

  describe('Status Filtering', () => {
    test('should filter by single status', () => {
      const { result } = renderHook(() => useFilters(mockPrompts, mockCategories))
      
      act(() => {
        result.current.setFilter('status', ['Published'])
      })
      
      expect(result.current.filteredPrompts).toHaveLength(2)
      result.current.filteredPrompts.forEach(prompt => {
        expect(prompt.status).toBe('Published')
      })
    })

    test('should filter by multiple statuses', () => {
      const { result } = renderHook(() => useFilters(mockPrompts, mockCategories))
      
      act(() => {
        result.current.setFilter('status', ['Published', 'Approved'])
      })
      
      expect(result.current.filteredPrompts).toHaveLength(3)
    })

    test('should toggle multiple status filters', () => {
      const { result } = renderHook(() => useFilters(mockPrompts, mockCategories))
      
      // Add first status
      act(() => {
        result.current.toggleMultiFilter('status', 'Published')
      })
      
      expect(result.current.filters.status).toEqual(['Published'])
      
      // Add second status
      act(() => {
        result.current.toggleMultiFilter('status', 'Approved')
      })
      
      expect(result.current.filters.status).toEqual(['Published', 'Approved'])
      
      // Remove first status
      act(() => {
        result.current.toggleMultiFilter('status', 'Published')
      })
      
      expect(result.current.filters.status).toEqual(['Approved'])
    })
  })

  describe('Difficulty Filtering', () => {
    test('should filter by difficulty', () => {
      const { result } = renderHook(() => useFilters(mockPrompts, mockCategories))
      
      act(() => {
        result.current.setFilter('difficulty', ['Advanced'])
      })
      
      expect(result.current.filteredPrompts).toHaveLength(2)
      result.current.filteredPrompts.forEach(prompt => {
        expect(prompt.difficulty).toBe('Advanced')
      })
    })

    test('should filter by multiple difficulties', () => {
      const { result } = renderHook(() => useFilters(mockPrompts, mockCategories))
      
      act(() => {
        result.current.setFilter('difficulty', ['Advanced', 'Beginner'])
      })
      
      expect(result.current.filteredPrompts).toHaveLength(3)
    })
  })

  describe('Author Filtering', () => {
    test('should filter by author', () => {
      const { result } = renderHook(() => useFilters(mockPrompts, mockCategories))
      
      act(() => {
        result.current.setFilter('author', 'PE Team')
      })
      
      expect(result.current.filteredPrompts).toHaveLength(1)
      expect(result.current.filteredPrompts[0].author).toBe('PE Team')
    })

    test('should handle partial author name matching', () => {
      const { result } = renderHook(() => useFilters(mockPrompts, mockCategories))
      
      act(() => {
        result.current.setFilter('author', 'Team')
      })
      
      expect(result.current.filteredPrompts).toHaveLength(4)
    })
  })

  describe('Sorting Functionality', () => {
    test('should sort by trending (votes descending)', () => {
      const { result } = renderHook(() => useFilters(mockPrompts, mockCategories))
      
      act(() => {
        result.current.setFilter('sortBy', 'trending')
      })
      
      const votes = result.current.filteredPrompts.map(prompt => prompt.votes)
      expect(votes).toEqual([200, 150, 120, 75])
    })

    test('should sort by title alphabetically', () => {
      const { result } = renderHook(() => useFilters(mockPrompts, mockCategories))
      
      act(() => {
        result.current.setFilter('sortBy', 'title')
      })
      
      const titles = result.current.filteredPrompts.map(prompt => prompt.title)
      expect(titles[0]).toBe('Advanced Investment Analysis')
      expect(titles[1]).toBe('Advanced Marketing Analytics')
      expect(titles[2]).toBe('Basic Marketing Strategy')
      expect(titles[3]).toBe('Intermediate Financial Planning')
    })

    test('should sort by difficulty', () => {
      const { result } = renderHook(() => useFilters(mockPrompts, mockCategories))
      
      act(() => {
        result.current.setFilter('sortBy', 'difficulty')
      })
      
      const difficulties = result.current.filteredPrompts.map(prompt => prompt.difficulty)
      expect(difficulties).toEqual(['Beginner', 'Intermediate', 'Advanced', 'Advanced'])
    })

    test('should sort by recent (ID descending)', () => {
      const { result } = renderHook(() => useFilters(mockPrompts, mockCategories))
      
      act(() => {
        result.current.setFilter('sortBy', 'recent')
      })
      
      const ids = result.current.filteredPrompts.map(prompt => prompt.id)
      expect(ids).toEqual([4, 3, 2, 1])
    })
  })

  describe('Filter Combinations', () => {
    test('should apply multiple filters simultaneously', async () => {
      const { result } = renderHook(() => useFilters(mockPrompts, mockCategories))
      
      act(() => {
        result.current.setFilter('category', 'Marketing')
        result.current.setFilter('difficulty', ['Advanced'])
        result.current.setFilter('searchQuery', 'analytics')
      })

      await act(async () => {
        await new Promise(resolve => setTimeout(resolve, 350))
      })
      
      expect(result.current.filteredPrompts).toHaveLength(1)
      expect(result.current.filteredPrompts[0].title).toBe('Advanced Marketing Analytics')
    })

    test('should return empty array when filters exclude all items', async () => {
      const { result } = renderHook(() => useFilters(mockPrompts, mockCategories))
      
      act(() => {
        result.current.setFilter('category', 'Marketing')
        result.current.setFilter('searchQuery', 'investment')
      })

      await act(async () => {
        await new Promise(resolve => setTimeout(resolve, 350))
      })
      
      expect(result.current.filteredPrompts).toHaveLength(0)
    })
  })

  describe('Filter Counts and Metrics', () => {
    test('should calculate filter counts correctly', () => {
      const { result } = renderHook(() => useFilters(mockPrompts, mockCategories))
      
      expect(result.current.filterCounts.categories['Marketing']).toBe(2)
      expect(result.current.filterCounts.categories['Investment Analysis']).toBe(1)
      expect(result.current.filterCounts.statuses['Published']).toBe(2)
      expect(result.current.filterCounts.difficulties['Advanced']).toBe(2)
    })

    test('should calculate active filter count', () => {
      const { result } = renderHook(() => useFilters(mockPrompts, mockCategories))
      
      expect(result.current.activeFilterCount).toBe(0)
      
      act(() => {
        result.current.setFilter('category', 'Marketing')
        result.current.setFilter('searchQuery', 'test')
      })
      
      expect(result.current.activeFilterCount).toBe(2)
    })

    test('should provide hasActiveFilters boolean', () => {
      const { result } = renderHook(() => useFilters(mockPrompts, mockCategories))
      
      expect(result.current.hasActiveFilters).toBe(false)
      
      act(() => {
        result.current.setFilter('category', 'Marketing')
      })
      
      expect(result.current.hasActiveFilters).toBe(true)
    })

    test('should generate filter summary', () => {
      const { result } = renderHook(() => useFilters(mockPrompts, mockCategories))
      
      act(() => {
        result.current.setFilter('searchQuery', 'test query')
        result.current.setFilter('category', 'Marketing')
        result.current.setFilter('status', ['Published', 'Approved'])
      })
      
      const summary = result.current.getFilterSummary
      expect(summary).toContain('Search: "test query"')
      expect(summary).toContain('Category: Marketing')
      expect(summary).toContain('Status: Published, Approved')
    })
  })

  describe('Reset Functionality', () => {
    test('should reset all filters', () => {
      const { result } = renderHook(() => useFilters(mockPrompts, mockCategories))
      
      // Apply some filters
      act(() => {
        result.current.setFilter('searchQuery', 'test')
        result.current.setFilter('category', 'Marketing')
        result.current.setFilter('status', ['Published'])
        result.current.setFilter('difficulty', ['Advanced'])
        result.current.setFilter('author', 'PE Team')
        result.current.setFilter('sortBy', 'title')
      })
      
      expect(result.current.activeFilterCount).toBe(5)
      
      // Reset filters
      act(() => {
        result.current.resetFilters()
      })
      
      expect(result.current.filters).toEqual({
        searchQuery: '',
        category: '',
        status: [],
        difficulty: [],
        author: '',
        sortBy: 'trending'
      })
      expect(result.current.activeFilterCount).toBe(0)
    })
  })

  describe('URL Sync Utilities', () => {
    test('should generate URL parameters from filters', () => {
      const { result } = renderHook(() => useFilters(mockPrompts, mockCategories))
      
      act(() => {
        result.current.setFilter('searchQuery', 'test query')
        result.current.setFilter('category', 'Marketing')
        result.current.setFilter('status', ['Published', 'Approved'])
        result.current.setFilter('sortBy', 'title')
      })
      
      const params = result.current.getFilterParams()
      expect(params).toContain('q=test+query')
      expect(params).toContain('category=Marketing')
      expect(params).toContain('status=Published%2CApproved')
      expect(params).toContain('sort=title')
    })

    test('should set filters from URL parameters', () => {
      const { result } = renderHook(() => useFilters(mockPrompts, mockCategories))
      
      act(() => {
        result.current.setFiltersFromParams('q=test&category=Marketing&status=Published,Approved&sort=title')
      })
      
      expect(result.current.filters.searchQuery).toBe('test')
      expect(result.current.filters.category).toBe('Marketing')
      expect(result.current.filters.status).toEqual(['Published', 'Approved'])
      expect(result.current.filters.sortBy).toBe('title')
    })
  })

  describe('Utility Functions', () => {
    test('should highlight search terms', () => {
      const { result } = renderHook(() => useFilters(mockPrompts, mockCategories))
      
      const highlighted = result.current.highlightSearchTerms(
        'This is a test sentence with investment terms', 
        'test investment'
      )
      
      expect(highlighted).toContain('<mark>test</mark>')
      expect(highlighted).toContain('<mark>investment</mark>')
    })

    test('should handle empty highlight inputs', () => {
      const { result } = renderHook(() => useFilters(mockPrompts, mockCategories))
      
      expect(result.current.highlightSearchTerms('', 'test')).toBe('')
      expect(result.current.highlightSearchTerms('test', '')).toBe('test')
      expect(result.current.highlightSearchTerms('', '')).toBe('')
    })
  })

  describe('Performance Optimizations', () => {
    test('should memoize filtered results', () => {
      const { result, rerender } = renderHook(() => useFilters(mockPrompts, mockCategories))
      
      const firstResult = result.current.filteredPrompts
      
      // Rerender without changing inputs
      rerender()
      
      // Should return same reference due to memoization
      expect(result.current.filteredPrompts).toBe(firstResult)
    })

    test('should update when prompts change', () => {
      const { result, rerender } = renderHook(
        ({ prompts }) => useFilters(prompts, mockCategories),
        { initialProps: { prompts: mockPrompts } }
      )
      
      expect(result.current.filteredPrompts).toHaveLength(4)
      
      // Change prompts
      const newPrompts = mockPrompts.slice(0, 2)
      rerender({ prompts: newPrompts })
      
      expect(result.current.filteredPrompts).toHaveLength(2)
    })
  })

  describe('Edge Cases', () => {
    test('should handle empty prompts array', () => {
      const { result } = renderHook(() => useFilters([], mockCategories))
      
      expect(result.current.filteredPrompts).toHaveLength(0)
      // When categories are provided but prompts are empty, counts should be 0 for each category
      expect(result.current.filterCounts.categories['Marketing']).toBe(0)
    })

    test('should handle prompts without required fields', () => {
      const incompletePrompts = [
        { id: 1, title: 'Test' },
        { id: 2, title: 'Another Test', category: 'Test Category' }
      ]
      
      const { result } = renderHook(() => useFilters(incompletePrompts, []))
      
      expect(result.current.filteredPrompts).toHaveLength(2)
      
      // Should not crash when filtering by missing fields
      act(() => {
        result.current.setFilter('difficulty', ['Advanced'])
      })
      
      expect(result.current.filteredPrompts).toHaveLength(0)
    })

    test('should handle undefined or null filter values', () => {
      const { result } = renderHook(() => useFilters(mockPrompts, mockCategories))
      
      act(() => {
        result.current.setFilter('category', null)
        result.current.setFilter('author', undefined)
      })
      
      // Should not crash and should treat as empty/no filter
      expect(result.current.filteredPrompts).toHaveLength(4)
    })
  })
})