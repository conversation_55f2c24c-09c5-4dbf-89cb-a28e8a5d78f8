import { useAuth } from '../auth/AuthContext';

export const usePermissions = () => {
  const { user } = useAuth();

  const hasPermission = (permission) => {
    if (!user || !user.permissions) return false;
    return user.permissions.includes(permission);
  };

  const hasAnyPermission = (permissions) => {
    if (!user || !user.permissions) return false;
    return permissions.some(permission => user.permissions.includes(permission));
  };

  const hasAllPermissions = (permissions) => {
    if (!user || !user.permissions) return false;
    return permissions.every(permission => user.permissions.includes(permission));
  };

  const isAdmin = () => {
    return hasPermission('admin');
  };

  const canRead = () => {
    return hasPermission('read');
  };

  const canWrite = () => {
    return hasPermission('write');
  };

  const canDelete = () => {
    return hasPermission('delete');
  };

  const canManageUsers = () => {
    return hasPermission('user_management');
  };

  const canAccessSystemSettings = () => {
    return hasPermission('system_settings');
  };

  const getRoleDisplayName = () => {
    if (!user) return '';
    
    switch (user.role) {
      case 'admin':
        return 'Administrator';
      case 'editor':
        return 'Editor';
      case 'user':
        return 'User';
      default:
        return user.role;
    }
  };

  const getRoleColor = () => {
    if (!user) return '#6b7280';
    
    switch (user.role) {
      case 'admin':
        return '#dc2626'; // Red
      case 'editor':
        return '#f59e0b'; // Orange
      case 'user':
        return '#059669'; // Green
      default:
        return '#6b7280'; // Gray
    }
  };

  return {
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
    isAdmin,
    canRead,
    canWrite,
    canDelete,
    canManageUsers,
    canAccessSystemSettings,
    getRoleDisplayName,
    getRoleColor,
    permissions: user?.permissions || [],
    role: user?.role || null
  };
};

export default usePermissions;