import { useEffect, useCallback } from 'react'

/**
 * useKeyboard - Hook for handling keyboard events
 * 
 * @param {Object} options
 * @param {Object} options.keyHandlers - Object mapping key names to handler functions
 * @param {Array} options.dependencies - Dependencies array for useCallback
 * @param {boolean} options.enabled - Whether keyboard handling is enabled
 * @param {string} options.target - Target element ('window' | 'document' | element ref)
 * @param {boolean} options.preventDefault - Whether to prevent default behavior
 * 
 * @example
 * // Simple ESC key handler
 * useKeyboard({
 *   keyHandlers: {
 *     'Escape': () => setModalOpen(false)
 *   }
 * })
 * 
 * @example
 * // Multiple key handlers with conditions
 * useKeyboard({
 *   keyHandlers: {
 *     'Escape': () => closeModal(),
 *     'Enter': (e) => !e.shiftKey && submitForm(),
 *     'ArrowUp': () => navigateUp(),
 *     'ArrowDown': () => navigateDown()
 *   },
 *   dependencies: [modalOpen, formData],
 *   enabled: isActive
 * })
 */
function useKeyboard({
  keyHandlers = {},
  dependencies = [],
  enabled = true,
  target = 'window',
  preventDefault = false
} = {}) {
  
  const handleKeyPress = useCallback((event) => {
    if (!enabled) return
    
    const key = event.key
    const handler = keyHandlers[key]
    
    if (handler) {
      if (preventDefault) {
        event.preventDefault()
      }
      handler(event)
    }
  }, [keyHandlers, enabled, preventDefault, ...dependencies])

  useEffect(() => {
    if (!enabled) return

    let targetElement = window
    
    if (target === 'document') {
      targetElement = document
    } else if (target && target.current) {
      targetElement = target.current
    } else if (typeof target === 'object' && target.addEventListener) {
      targetElement = target
    }

    const eventType = 'keydown'
    targetElement.addEventListener(eventType, handleKeyPress)

    return () => {
      targetElement.removeEventListener(eventType, handleKeyPress)
    }
  }, [handleKeyPress, enabled, target])

  return { handleKeyPress }
}

/**
 * useEscapeKey - Specialized hook for ESC key handling
 * 
 * @param {Function} onEscape - Callback when ESC is pressed
 * @param {boolean} enabled - Whether ESC handling is enabled
 * @param {Array} dependencies - Dependencies for the callback
 */
export function useEscapeKey(onEscape, enabled = true, dependencies = []) {
  return useKeyboard({
    keyHandlers: {
      'Escape': onEscape
    },
    dependencies,
    enabled
  })
}

/**
 * useEnterKey - Specialized hook for Enter key handling
 * 
 * @param {Function} onEnter - Callback when Enter is pressed
 * @param {boolean} preventShiftEnter - Whether to ignore Shift+Enter
 * @param {boolean} enabled - Whether Enter handling is enabled
 * @param {Array} dependencies - Dependencies for the callback
 */
export function useEnterKey(onEnter, preventShiftEnter = true, enabled = true, dependencies = []) {
  return useKeyboard({
    keyHandlers: {
      'Enter': (e) => {
        if (preventShiftEnter && e.shiftKey) return
        onEnter(e)
      }
    },
    dependencies,
    enabled,
    preventDefault: true
  })
}

/**
 * useArrowKeys - Hook for arrow key navigation
 * 
 * @param {Object} handlers
 * @param {Function} handlers.onUp - Up arrow handler
 * @param {Function} handlers.onDown - Down arrow handler  
 * @param {Function} handlers.onLeft - Left arrow handler
 * @param {Function} handlers.onRight - Right arrow handler
 * @param {boolean} enabled - Whether arrow key handling is enabled
 * @param {Array} dependencies - Dependencies for the callbacks
 */
export function useArrowKeys({ onUp, onDown, onLeft, onRight }, enabled = true, dependencies = []) {
  const keyHandlers = {}
  
  if (onUp) keyHandlers['ArrowUp'] = onUp
  if (onDown) keyHandlers['ArrowDown'] = onDown
  if (onLeft) keyHandlers['ArrowLeft'] = onLeft
  if (onRight) keyHandlers['ArrowRight'] = onRight

  return useKeyboard({
    keyHandlers,
    dependencies,
    enabled,
    preventDefault: true
  })
}

/**
 * useHotkeys - Hook for custom hotkey combinations
 * 
 * @param {Object} hotkeys - Object mapping hotkey strings to handlers
 * @param {boolean} enabled - Whether hotkey handling is enabled
 * @param {Array} dependencies - Dependencies for the callbacks
 * 
 * @example
 * useHotkeys({
 *   'ctrl+s': () => saveDocument(),
 *   'cmd+k': () => openSearch(),
 *   'shift+?': () => showHelp()
 * })
 */
export function useHotkeys(hotkeys = {}, enabled = true, dependencies = []) {
  const handleKeyPress = useCallback((event) => {
    if (!enabled) return
    
    const key = event.key.toLowerCase()
    const ctrl = event.ctrlKey || event.metaKey
    const shift = event.shiftKey
    const alt = event.altKey
    
    // Build hotkey string
    let hotkeyString = ''
    if (ctrl) hotkeyString += 'ctrl+'
    if (alt) hotkeyString += 'alt+'
    if (shift) hotkeyString += 'shift+'
    hotkeyString += key
    
    // Also check for cmd+ variant on Mac
    const cmdHotkey = hotkeyString.replace('ctrl+', 'cmd+')
    
    const handler = hotkeys[hotkeyString] || hotkeys[cmdHotkey]
    
    if (handler) {
      event.preventDefault()
      handler(event)
    }
  }, [hotkeys, enabled, ...dependencies])

  useEffect(() => {
    if (!enabled) return

    window.addEventListener('keydown', handleKeyPress)
    return () => window.removeEventListener('keydown', handleKeyPress)
  }, [handleKeyPress, enabled])

  return { handleKeyPress }
}

export default useKeyboard