import { useState, useCallback, useRef, useEffect } from 'react'

/**
 * useAIQuery - Hook for managing AI chat state and streaming responses
 * 
 * @param {Object} options
 * @param {Function} options.onSubmit - Callback when query is submitted
 * @param {Function} options.onResponse - Callback when response is received
 * @param {Function} options.onError - Callback when error occurs
 * @param {Function} options.onStreamingUpdate - Callback for streaming updates
 * @param {Array} options.initialMessages - Initial messages array
 * @param {boolean} options.enableStreaming - Whether to enable streaming responses
 * @param {number} options.maxMessages - Maximum number of messages to keep
 * @param {boolean} options.persistMessages - Whether to persist messages in localStorage
 * @param {string} options.storageKey - localStorage key for persisting messages
 * 
 * @returns {Object} AI query state and methods
 */
function useAIQuery(options = {}) {
  const {
    onSubmit,
    onResponse,
    onError,
    onStreamingUpdate,
    initialMessages = [],
    enableStreaming = true,
    maxMessages = 50,
    persistMessages = false,
    storageKey = 'ai-chat-messages'
  } = options

  // Load messages from localStorage if persistence is enabled
  const loadPersistedMessages = useCallback(() => {
    if (!persistMessages) return initialMessages
    
    try {
      const stored = localStorage.getItem(storageKey)
      return stored ? JSON.parse(stored) : initialMessages
    } catch (error) {
      console.warn('Failed to load persisted messages:', error)
      return initialMessages
    }
  }, [persistMessages, storageKey, initialMessages])

  const [messages, setMessages] = useState(loadPersistedMessages)
  const [query, setQuery] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState(null)
  const [streamingMessageId, setStreamingMessageId] = useState(null)
  const [abortController, setAbortController] = useState(null)

  const queryRef = useRef('')
  const messagesRef = useRef(messages)

  // Update refs when state changes
  useEffect(() => {
    queryRef.current = query
  }, [query])

  useEffect(() => {
    messagesRef.current = messages
  }, [messages])

  // Persist messages to localStorage
  useEffect(() => {
    if (persistMessages && messages.length > 0) {
      try {
        localStorage.setItem(storageKey, JSON.stringify(messages))
      } catch (error) {
        console.warn('Failed to persist messages:', error)
      }
    }
  }, [messages, persistMessages, storageKey])

  const addMessage = useCallback((message) => {
    setMessages(prev => {
      const updated = [...prev, { ...message, id: message.id || Date.now() }]
      
      // Trim messages if exceeding max
      if (updated.length > maxMessages) {
        return updated.slice(-maxMessages)
      }
      
      return updated
    })
  }, [maxMessages])

  const updateMessage = useCallback((messageId, updates) => {
    setMessages(prev => 
      prev.map(msg => 
        msg.id === messageId ? { ...msg, ...updates } : msg
      )
    )
  }, [])

  const removeMessage = useCallback((messageId) => {
    setMessages(prev => prev.filter(msg => msg.id !== messageId))
  }, [])

  const clearMessages = useCallback(() => {
    setMessages([])
    if (persistMessages) {
      try {
        localStorage.removeItem(storageKey)
      } catch (error) {
        console.warn('Failed to clear persisted messages:', error)
      }
    }
  }, [persistMessages, storageKey])

  const handleStreamingResponse = useCallback(async (reader, messageId) => {
    let accumulatedText = ''
    
    try {
      while (true) {
        const { done, value } = await reader.read()
        
        if (done) {
          // Mark message as complete
          updateMessage(messageId, { 
            isStreaming: false,
            message: accumulatedText 
          })
          setStreamingMessageId(null)
          break
        }
        
        // Decode the chunk
        const chunk = new TextDecoder().decode(value)
        accumulatedText += chunk
        
        // Update the message with accumulated text
        updateMessage(messageId, { 
          message: accumulatedText,
          isStreaming: true 
        })
        
        // Call streaming update callback
        if (onStreamingUpdate) {
          onStreamingUpdate(accumulatedText, chunk, messageId)
        }
      }
    } catch (error) {
      if (error.name !== 'AbortError') {
        console.error('Streaming error:', error)
        updateMessage(messageId, { 
          isStreaming: false,
          error: 'Streaming failed'
        })
        if (onError) {
          onError(error, messageId)
        }
      }
    }
  }, [updateMessage, onStreamingUpdate, onError])

  const submitQuery = useCallback(async (queryText) => {
    const currentQuery = queryText || queryRef.current
    if (!currentQuery.trim() || isLoading) return

    setIsLoading(true)
    setError(null)

    // Create user message
    const userMessage = {
      id: Date.now(),
      type: 'user',
      message: currentQuery,
      timestamp: new Date().toISOString()
    }

    addMessage(userMessage)
    setQuery('')

    // Create AI response placeholder
    const aiMessageId = Date.now() + 1
    const aiMessage = {
      id: aiMessageId,
      type: 'ai',
      message: '',
      isStreaming: enableStreaming,
      timestamp: new Date().toISOString()
    }

    addMessage(aiMessage)

    if (enableStreaming) {
      setStreamingMessageId(aiMessageId)
    }

    try {
      // Create abort controller for cancellation
      const controller = new AbortController()
      setAbortController(controller)

      // Call the submit callback
      const response = await onSubmit?.(currentQuery, {
        messages: messagesRef.current,
        signal: controller.signal,
        messageId: aiMessageId
      })

      if (response) {
        if (enableStreaming && response.body && response.body.getReader) {
          // Handle streaming response
          const reader = response.body.getReader()
          await handleStreamingResponse(reader, aiMessageId)
        } else {
          // Handle regular response
          const responseText = typeof response === 'string' ? response : response.message || ''
          updateMessage(aiMessageId, {
            message: responseText,
            isStreaming: false
          })
        }

        if (onResponse) {
          onResponse(response, currentQuery, aiMessageId)
        }
      }
    } catch (error) {
      if (error.name !== 'AbortError') {
        console.error('Query submission error:', error)
        setError(error.message || 'Failed to submit query')
        
        // Update AI message with error
        updateMessage(aiMessageId, {
          message: 'Sorry, I encountered an error processing your request.',
          isStreaming: false,
          error: true
        })

        if (onError) {
          onError(error, currentQuery, aiMessageId)
        }
      }
    } finally {
      setIsLoading(false)
      setAbortController(null)
    }
  }, [
    isLoading, 
    addMessage, 
    enableStreaming, 
    onSubmit, 
    handleStreamingResponse, 
    updateMessage, 
    onResponse, 
    onError
  ])

  const cancelQuery = useCallback(() => {
    if (abortController) {
      abortController.abort()
      setAbortController(null)
    }
    
    setIsLoading(false)
    
    // Mark streaming message as cancelled
    if (streamingMessageId) {
      updateMessage(streamingMessageId, {
        isStreaming: false,
        message: 'Request cancelled.',
        cancelled: true
      })
      setStreamingMessageId(null)
    }
  }, [abortController, streamingMessageId, updateMessage])

  const regenerateResponse = useCallback(async (messageId) => {
    const message = messages.find(msg => msg.id === messageId)
    if (!message || message.type !== 'ai') return

    // Find the previous user message
    const messageIndex = messages.findIndex(msg => msg.id === messageId)
    const userMessage = messages.slice(0, messageIndex).reverse().find(msg => msg.type === 'user')
    
    if (userMessage) {
      // Remove the AI response and regenerate
      removeMessage(messageId)
      await submitQuery(userMessage.message)
    }
  }, [messages, removeMessage, submitQuery])

  const retryLastQuery = useCallback(async () => {
    const lastUserMessage = [...messages].reverse().find(msg => msg.type === 'user')
    if (lastUserMessage) {
      await submitQuery(lastUserMessage.message)
    }
  }, [messages, submitQuery])

  const getMessageHistory = useCallback(() => {
    return messages.map(msg => ({
      role: msg.type === 'user' ? 'user' : 'assistant',
      content: msg.message
    }))
  }, [messages])

  const hasMessages = messages.length > 0
  const lastMessage = messages[messages.length - 1]
  const isStreamingActive = Boolean(streamingMessageId)

  return {
    // State
    messages,
    query,
    isLoading,
    error,
    isStreamingActive,
    hasMessages,
    lastMessage,

    // Message management
    addMessage,
    updateMessage,
    removeMessage,
    clearMessages,

    // Query handling
    setQuery,
    submitQuery,
    cancelQuery,
    regenerateResponse,
    retryLastQuery,

    // Utilities
    getMessageHistory
  }
}

/**
 * useAIQueryStreaming - Specialized hook for streaming AI responses
 */
export function useAIQueryStreaming(apiEndpoint, options = {}) {
  const handleSubmit = useCallback(async (query, context) => {
    const response = await fetch(apiEndpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        query,
        messages: context.messages,
        streaming: true
      }),
      signal: context.signal
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    return response
  }, [apiEndpoint])

  return useAIQuery({
    ...options,
    onSubmit: handleSubmit,
    enableStreaming: true
  })
}

/**
 * useAIQuerySimple - Simplified hook for basic AI queries without streaming
 */
export function useAIQuerySimple(apiEndpoint, options = {}) {
  const handleSubmit = useCallback(async (query, context) => {
    const response = await fetch(apiEndpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        query,
        messages: context.messages
      }),
      signal: context.signal
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const data = await response.json()
    return data.response || data.message || data
  }, [apiEndpoint])

  return useAIQuery({
    ...options,
    onSubmit: handleSubmit,
    enableStreaming: false
  })
}

export default useAIQuery