import { useState, useCallback, useEffect } from 'react'
import { useAuth } from '../auth/AuthContext'
import config from '../config.js'

/**
 * Custom hook for managing prompt voting functionality
 * Provides optimistic updates, error handling, and loading states
 * 
 * @param {string|number} promptId - ID of the prompt to vote on
 * @param {number} initialVotes - Initial vote count
 * @param {string|null} userVote - Current user's vote ('up', 'down', or null)
 * @param {function} onVoteChange - Optional callback when vote changes
 * @returns {object} Vote management object
 */
export function useVote(promptId, initialVotes = 0, userVote = null, onVoteChange = null) {
  const { user, isAuthenticated } = useAuth()
  
  // Local state for optimistic updates
  const [votes, setVotes] = useState(initialVotes)
  const [currentUserVote, setCurrentUserVote] = useState(userVote)
  const [isVoting, setIsVoting] = useState(false)
  const [error, setError] = useState(null)

  // Helper to check if user has voted
  const hasUserVoted = currentUserVote !== null

  // Get API based on configuration
  const getAPI = useCallback(async () => {
    if (config.USE_DATABASE_INSTEAD_OF_JSON_FILE) {
      const endpoints = await import('../api/browserEndpoints.js')
      return endpoints.promptsAPI
    } else {
      const client = await import('../api/client.js')
      return client.promptsAPI
    }
  }, [])

  // Calculate vote delta for optimistic updates
  const calculateVoteDelta = useCallback((voteType, currentVote) => {
    if (voteType === 'toggle') {
      return currentVote ? -1 : 1
    }
    
    if (voteType === 'up') {
      if (currentVote === 'up') return -1  // Remove upvote
      if (currentVote === 'down') return 2 // Change from down to up
      return 1 // Add upvote
    }
    
    if (voteType === 'down') {
      if (currentVote === 'down') return 1  // Remove downvote (add 1 back)
      if (currentVote === 'up') return -2   // Change from up to down
      return -1 // Add downvote
    }
    
    return 0
  }, [])

  // Calculate new user vote state
  const calculateNewUserVote = useCallback((voteType, currentVote) => {
    if (voteType === 'toggle') {
      return currentVote ? null : 'up'
    }
    
    if (voteType === 'up') {
      return currentVote === 'up' ? null : 'up'
    }
    
    if (voteType === 'down') {
      return currentVote === 'down' ? null : 'down'
    }
    
    return currentVote
  }, [])

  // Main vote handler
  const handleVote = useCallback(async (voteType = 'toggle') => {
    // Check authentication
    if (!isAuthenticated || !user) {
      const authError = new Error('Authentication required to vote')
      authError.code = 'AUTH_REQUIRED'
      throw authError
    }

    // Prevent multiple simultaneous votes
    if (isVoting) {
      return
    }

    setIsVoting(true)
    setError(null)

    // Calculate optimistic updates
    const voteDelta = calculateVoteDelta(voteType, currentUserVote)
    const newUserVote = calculateNewUserVote(voteType, currentUserVote)
    
    // Store original state for rollback
    const originalVotes = votes
    const originalUserVote = currentUserVote

    try {
      // Apply optimistic updates
      setVotes(prevVotes => prevVotes + voteDelta)
      setCurrentUserVote(newUserVote)

      // Make API call
      const promptsAPI = await getAPI()
      const updatedPrompt = await promptsAPI.vote(promptId, user.id, voteType)

      // Update with server response (in case of discrepancy)
      setVotes(updatedPrompt.votes)
      setCurrentUserVote(updatedPrompt.userVote)

      // Call optional callback
      if (onVoteChange) {
        onVoteChange(updatedPrompt)
      }

    } catch (error) {
      console.error('Vote failed:', error)
      
      // Rollback optimistic updates
      setVotes(originalVotes)
      setCurrentUserVote(originalUserVote)
      
      // Set error for UI display
      if (error.code === 'AUTH_REQUIRED') {
        setError('Please log in to vote')
      } else {
        setError('Failed to vote. Please try again.')
      }
      
      // Re-throw for component handling
      throw error
    } finally {
      setIsVoting(false)
    }
  }, [
    isAuthenticated,
    user,
    isVoting,
    promptId,
    votes,
    currentUserVote,
    onVoteChange,
    getAPI,
    calculateVoteDelta,
    calculateNewUserVote
  ])

  // Convenience methods for specific vote types
  const upvote = useCallback(() => handleVote('up'), [handleVote])
  const downvote = useCallback(() => handleVote('down'), [handleVote])
  const toggleVote = useCallback(() => handleVote('toggle'), [handleVote])

  // Clear error function
  const clearError = useCallback(() => setError(null), [])

  // Sync with external prop changes
  useEffect(() => {
    setVotes(initialVotes)
  }, [initialVotes])

  useEffect(() => {
    setCurrentUserVote(userVote)
  }, [userVote])

  return {
    // State
    votes,
    userVote: currentUserVote,
    isVoting,
    hasUserVoted,
    error,
    
    // Actions
    handleVote,
    upvote,
    downvote,
    toggleVote,
    clearError,
    
    // Computed properties
    canVote: isAuthenticated && !isVoting
  }
}

export default useVote