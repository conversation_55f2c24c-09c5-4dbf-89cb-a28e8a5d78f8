# AI Query System Technical Architecture

## Overview

The AI Query System provides intelligent prompt search and recommendation capabilities for Apollo's prompt library. The system integrates OpenAI's GPT-4 model to deliver contextual search results, prompt recommendations, and natural language query processing against the prompt database.

## Atomic Design Architecture

The AI query system follows Atomic Design principles with a clear component hierarchy:

### Component Structure
```
src/ai-query/
├── atoms/              # Basic UI elements (empty - using shared atoms)
├── molecules/          # Simple component combinations (empty - using shared)
├── organisms/          # Complex UI components
│   ├── AIQueryInterface.jsx          # Basic AI chat interface
│   ├── SharedAIQueryInterface.jsx    # Production AI interface with OpenAI
│   └── AIQueryInterface.css          # Shared styles
├── templates/          # Page-level templates (empty - using shared)
├── services/           # Business logic and API integration
│   └── AIQueryService.js             # OpenAI integration service
└── utils/              # Helper functions and utilities
    └── PromptContextBuilder.js       # Context preparation utilities
```

### Component Hierarchy

**Organisms (Complex Components):**
- `SharedAIQueryInterface`: Production-ready AI chat interface
- `AIQueryInterface`: Basic demo interface (legacy)

**Services (Business Logic):**
- `AIQueryService`: Handles OpenAI API communication
- `PromptContextBuilder`: Prepares prompt data for AI consumption

## OpenAI Integration Architecture

### Service Layer (`AIQueryService.js`)

The service implements a singleton pattern for OpenAI API management:

```javascript
class AIQueryService {
  constructor() {
    const apiKey = import.meta.env.VITE_OPENAI_API_KEY;
    
    if (!apiKey) {
      console.warn('⚠️ VITE_OPENAI_API_KEY not found. AI queries will use simulation mode.');
      this.openai = null;
    } else {
      this.openai = new OpenAI({
        apiKey: apiKey,
        dangerouslyAllowBrowser: true  // Required for client-side usage
      });
    }
  }
}
```

### Configuration Management

AI system configuration is centralized in `/src/config.js`:

```javascript
const config = {
  // AI Configuration
  USE_OPENAI: true,                    // Toggle for OpenAI vs simulation
  OPENAI_MODEL: 'gpt-4o',             // Model version
  OPENAI_TEMPERATURE: 0.7,            // Response creativity
  OPENAI_MAX_TOKENS: 1000,            // Response length limit
  DEBUG: import.meta.env.MODE === 'development'
}
```

### System Prompt Engineering

The AI assistant uses a specialized system prompt for Apollo's context:

```javascript
this.systemPrompt = `You are an AI assistant for Apollo Global Management's prompt library. 
You have access to the complete database of investment analysis prompts below.

When answering questions about prompts:
- Provide accurate information from the provided prompt data
- Include specific citations using [prompt_id] format for any prompts you reference
- Give relevant recommendations based on user needs
- Use a professional tone appropriate for investment professionals
- If asked about prompts that don't exist, say so clearly
- When suggesting multiple prompts, explain why each is relevant
- Format responses clearly with titles, difficulty levels, and descriptions
- Always explain why each prompt is relevant to the user's query

Available prompts database:
{PROMPT_CONTEXT}`;
```

## State Management and Persistence Strategy

### Shared State Architecture

The AI chat state is managed at the App component level and shared across views:

```javascript
// AI Chat state - shared across all views
const [aiChatMessages, setAiChatMessages] = useState([])
const [aiChatQuery, setAiChatQuery] = useState('')
const [aiChatLoading, setAiChatLoading] = useState(false)
const [aiChatExpanded, setAiChatExpanded] = useState(false)
```

### State Persistence Strategy

**Session Persistence:**
- Chat messages persist during navigation between views
- Query state maintained when switching between main view and detail view
- Expansion state preserved across route changes

**No Persistent Storage:**
- Chat history clears on page refresh (by design for privacy)
- No localStorage or sessionStorage usage for chat data
- Fresh conversation starts with each session

### Component Props Flow

```javascript
<SharedAIQueryInterface
  prompts={prompts}              // Complete prompt database
  categories={categories}        // Category metadata
  onCitationClick={handleClick}  // Citation navigation callback
  messages={aiChatMessages}      // Shared message state
  setMessages={setAiChatMessages}
  query={aiChatQuery}           // Shared query state
  setQuery={setAiChatQuery}
  loading={aiChatLoading}       // Shared loading state
  setLoading={setAiChatLoading}
  isExpanded={aiChatExpanded}   // Shared expansion state
  setIsExpanded={setAiChatExpanded}
/>
```

## Configuration and Environment Setup

### Environment Variables

**Required for Production:**
```bash
VITE_OPENAI_API_KEY=sk-...  # OpenAI API key for production
```

**Optional Configuration:**
```bash
VITE_API_BASE_URL=http://localhost:3000  # Backend API URL
MODE=development                          # Development/production mode
```

### Development vs Production Modes

**Development Mode:**
- Enhanced logging and debugging
- Fallback to simulation if OpenAI fails
- Debug console output for API calls

**Production Mode:**
- Error handling without verbose logging
- Silent fallback to simulation mode
- User-friendly error messages

### Fallback Configuration

The system implements graceful degradation:

1. **Primary**: OpenAI API with real GPT-4 responses
2. **Fallback**: Simulated responses using prompt database search
3. **Error Handling**: User-friendly error messages with retry options

## API Interaction Patterns

### Query Processing Flow

```javascript
async queryPrompts(userQuestion, promptContext, conversationHistory = []) {
  try {
    if (!this.openai) {
      throw new Error('OpenAI client not initialized - API key missing');
    }

    const systemPromptWithContext = this.systemPrompt.replace('{PROMPT_CONTEXT}', 
      JSON.stringify(promptContext, null, 2));

    const messages = [
      { role: 'system', content: systemPromptWithContext },
      ...conversationHistory,
      { role: 'user', content: userQuestion }
    ];

    const response = await this.openai.chat.completions.create({
      model: config.OPENAI_MODEL,
      messages: messages,
      temperature: config.OPENAI_TEMPERATURE,
      max_tokens: config.OPENAI_MAX_TOKENS
    });

    return {
      success: true,
      response: response.choices[0].message.content,
      usage: response.usage
    };
  } catch (error) {
    console.error('AI Query Error:', error);
    return {
      success: false,
      error: error.message || 'Failed to process query'
    };
  }
}
```

### Context Building Strategy

The `PromptContextBuilder` prepares optimized context for the AI:

```javascript
static buildContext(prompts, categories = []) {
  const contextData = {
    total_prompts: prompts.length,
    categories: this.getCategoryStats(prompts, categories),
    prompts: prompts.map(prompt => ({
      id: prompt.id,
      title: prompt.title,
      description: prompt.description,
      content: prompt.content ? prompt.content.substring(0, 500) + '...' : '',
      category: prompt.category,
      tags: prompt.tags || [],
      status: prompt.status,
      difficulty: prompt.difficulty,
      votes: prompt.votes || 0,
      author: prompt.author,
      created_at: prompt.created_at || prompt.timeAgo
    }))
  };

  return contextData;
}
```

**Context Optimization:**
- Content truncated to 500 characters to manage token limits
- Statistical metadata included for category analysis
- Full prompt metadata preserved for accurate citations

## Error Handling and Fallback Mechanisms

### Multi-Level Error Handling

**1. Service Level Error Handling:**
```javascript
try {
  const result = await AIQueryService.queryPrompts(currentQuery, context, conversationHistory)
  if (result.success) {
    // Process successful response
  } else {
    // Fallback to simulation if OpenAI fails
    console.warn('OpenAI failed, falling back to simulation:', result.error);
    await simulatedResponse(currentQuery)
  }
} catch (error) {
  console.error('AI Query Error:', error)
  const errorMessage = { 
    type: 'ai', 
    message: `I apologize, but I encountered an error processing your request: ${error.message}. Please try again or check your connection.` 
  }
  setMessages(prev => [...prev, errorMessage])
}
```

**2. Graceful Degradation Strategy:**

| Scenario | Behavior |
|----------|----------|
| No API Key | Automatic simulation mode with warning |
| API Rate Limit | Fallback to simulation with user notification |
| Network Error | Retry once, then simulation with error message |
| Invalid Response | Parse error and provide fallback response |

**3. Simulation Mode Implementation:**

```javascript
const simulatedResponse = async (currentQuery) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const relevantPrompts = prompts.filter(p => 
        p.title.toLowerCase().includes(currentQuery.toLowerCase()) ||
        p.description.toLowerCase().includes(currentQuery.toLowerCase()) ||
        (p.tags && p.tags.some(tag => tag.toLowerCase().includes(currentQuery.toLowerCase())))
      ).slice(0, 3)

      let responseMessage = `I found ${relevantPrompts.length > 0 ? relevantPrompts.length : 'several'} prompts related to "${currentQuery}". Here are the most relevant ones:\n\n`
      
      // Generate contextual relevance explanations
      relevantPrompts.forEach(prompt => {
        let relevanceReason = determineRelevance(prompt, currentQuery)
        responseMessage += `[${prompt.id}] **${prompt.title}** (${prompt.difficulty}) - ${prompt.description.substring(0, 100)}...\n\n*Relevant because: ${relevanceReason}*\n\n`
      })

      const aiMessage = { type: 'ai', message: responseMessage }
      setMessages(prev => [...prev, aiMessage])
      resolve()
    }, 1000)
  })
}
```

### User Experience During Errors

**Loading States:**
- Spinner animation during API calls
- "Thinking..." message for user feedback
- Disabled input during processing

**Error Communication:**
- Professional error messages appropriate for investment professionals
- Clear guidance on next steps (retry, check connection)
- No technical details exposed to end users

**Fallback UX:**
- Seamless transition to simulation mode
- No indication of degraded service to maintain confidence
- Consistent response formatting between modes

## Citation and Navigation System

### Citation Parsing

The system extracts and formats citations from AI responses:

```javascript
extractCitations(responseText) {
  const citationRegex = /\[(\d+)\]/g;
  const citations = [];
  let match;

  while ((match = citationRegex.exec(responseText)) !== null) {
    citations.push(parseInt(match[1]));
  }

  return [...new Set(citations)]; // Remove duplicates
}
```

### Interactive Citation Links

Citations are rendered as clickable buttons that navigate to prompt details:

```javascript
const citationMatch = part.match(/\[(\d+)\]/)
if (citationMatch) {
  const promptId = parseInt(citationMatch[1])
  const prompt = prompts.find(p => p.id === promptId)
  return (
    <button 
      key={i}
      className="citation-link"
      onClick={() => handleCitationClick(promptId)}
    >
      [{prompt ? prompt.title : `Prompt ${promptId}`}]
    </button>
  )
}
```

## Performance Considerations

### Token Management
- System prompt optimized for clarity without excessive length
- Context content truncated to 500 characters per prompt
- Conversation history included for context continuity

### Request Optimization
- Single API call per query (no streaming implemented)
- Context built once per query session
- Cached prompt data prevents redundant database calls

### Memory Management
- Messages array grows with conversation but clears on refresh
- No persistent storage reduces memory footprint
- Component cleanup on unmount prevents memory leaks

## Security Considerations

### API Key Management
- Environment variable storage for API keys
- Client-side usage with `dangerouslyAllowBrowser: true` (acceptable for demo)
- No API key exposure in client-side code

### Content Filtering
- Professional system prompt reduces inappropriate responses
- Corporate context maintains appropriate tone
- No user-generated system prompts to prevent injection

### Privacy
- No conversation logging or persistence
- No user data sent to OpenAI beyond query context
- Session-only chat history for privacy compliance

## Integration Points

### Main Application Integration

The AI system integrates with the main app through:

**1. State Management:**
- Shared state lifted to App component level
- Props drilling for consistent state across views

**2. Navigation Integration:**
- Citation clicks trigger navigation to prompt details
- State preservation during view transitions
- Back navigation maintains scroll position

**3. Data Integration:**
- Real-time access to prompt database
- Category and filter integration
- Vote and interaction state synchronization

### Future Extension Points

**Potential Enhancements:**
- Conversation persistence with user accounts
- Advanced citation formatting (markdown support)
- Multi-model support (Claude, Gemini integration)
- Streaming responses for improved UX
- Conversation export functionality
- Advanced analytics and usage tracking

This architecture provides a robust, scalable foundation for AI-powered prompt discovery while maintaining professional standards appropriate for Apollo Global Management's investment analysis platform.