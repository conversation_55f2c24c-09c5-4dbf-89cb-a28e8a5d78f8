# User Stories - Apollo Prompt Library

## Voting System

**As a user, when I click on an unvoted prompt's vote number, it should increment by 1 and show as "voted" (purple/highlighted state)**

**As a user, when I click on a prompt I've already voted on, it should decrement by 1 and return to the unvoted state (gray)**

**As a user, I should see visual feedback - voted prompts show the number in purple with a highlighted background, unvoted prompts show in gray**

**As a user, my vote state should persist (stored in JSON) so when I refresh or navigate away and back, my votes are remembered**

## Navigation & Detail View

**As a user, when I click on a prompt card (but not the vote button), I should navigate to the detailed view of that prompt**

**As a user, I should be able to see different versions of a prompt and switch between them**

**As a user, I should be able to return to the main library view from the prompt detail page**

**As a user, when I return from a prompt detail page, I should be taken back to the same scroll position I was at before**

**As a user, when I return from a prompt detail page, my search terms and filters should remain exactly as I left them**

**As a user, I should be able to close the prompt detail view by pressing the ESC key**

## Comments & Interaction

**As a user, I should see the comment count on each prompt card to understand engagement level**

**As a user, I should be able to filter and search prompts based on categories, tags, and status**

## Visual Feedback & UI

**As a user, all interactions should provide immediate visual feedback (hover states, click animations)**

**As a user, the interface should maintain Apollo's professional dark theme throughout all interactions**

**As a user, voting and navigation should work smoothly without page refreshes or loading delays**

## New Prompt Submission

**As a user, when I click the "Submit Idea" button, I should be able to create a new prompt that automatically gets tagged as "Suggested" status**

**As a user, when I submit a new prompt idea, it should immediately appear in the prompt list with "Suggested" status so I can see my contribution**

**As a user, when I submit a new prompt, the form should reset and close automatically after successful submission**

**As a user, I should see "Suggested" prompts clearly differentiated in the UI so I know which are community contributions vs. approved content**

**As a user, the status ordering in filters should follow workflow progression: Suggested → Approved → In Development → Under Review → Published**

## Filter Behavior

**As a user, when I click on a prompt status filter, it should select that status and show only prompts with that status**

**As a user, when I click on a selected prompt status filter again, it should deselect and show all prompts (same behavior as categories)**

## Visual Design

**As a user, the Apollo "A" logo should be displayed in white font at rest, with a smooth animation to green color on hover**