{"permissions": {"allow": ["Bash(npm install:*)", "Bash(npm run test:run:*)", "Bash(git add:*)", "Bash(git commit:*)", "Bash(timeout 10s npm run dev)", "Bash(node capture-all.js)", "Bash(node:*)", "Bash(npm run dev:*)", "Bash(npm test)", "Bash(rg:*)", "Bash(npm test:*)", "Bash(npm run build:*)", "Bash(grep:*)", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(pkill:*)", "Bash(timeout 10s npm run build)", "Bash(npm run lint)", "Bash(ls:*)"], "deny": []}}