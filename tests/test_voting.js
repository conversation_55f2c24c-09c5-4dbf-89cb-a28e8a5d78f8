// Voting functionality test script
// Run this in the browser console at http://localhost:5173/

console.log('=== Voting Functionality Test ===');

// Test 1: Check if vote buttons exist and are clickable
const voteButtons = document.querySelectorAll('.vote-count');
console.log(`Found ${voteButtons.length} vote buttons`);

if (voteButtons.length > 0) {
    // Test 2: Click first vote button and verify response
    const firstButton = voteButtons[0];
    const initialCount = parseInt(firstButton.textContent);
    const initialClasses = firstButton.className;
    
    console.log(`Initial vote count: ${initialCount}`);
    console.log(`Initial classes: ${initialClasses}`);
    
    // Simulate click
    firstButton.click();
    
    setTimeout(() => {
        const newCount = parseInt(firstButton.textContent);
        const newClasses = firstButton.className;
        
        console.log(`New vote count: ${newCount}`);
        console.log(`New classes: ${newClasses}`);
        
        // Check if count changed and classes updated
        if (newCount !== initialCount) {
            console.log('✅ Vote count updated successfully');
        } else {
            console.log('❌ Vote count did not update');
        }
        
        if (newClasses !== initialClasses) {
            console.log('✅ Visual state updated successfully');
        } else {
            console.log('❌ Visual state did not update');
        }
        
        // Test 3: Click again to test toggle functionality
        firstButton.click();
        
        setTimeout(() => {
            const toggledCount = parseInt(firstButton.textContent);
            const toggledClasses = firstButton.className;
            
            console.log(`Toggled vote count: ${toggledCount}`);
            console.log(`Toggled classes: ${toggledClasses}`);
            
            // Test 4: Test multiple cards
            if (voteButtons.length > 1) {
                console.log('Testing second vote button...');
                const secondButton = voteButtons[1];
                const secondInitialCount = parseInt(secondButton.textContent);
                secondButton.click();
                
                setTimeout(() => {
                    const secondNewCount = parseInt(secondButton.textContent);
                    if (secondNewCount !== secondInitialCount) {
                        console.log('✅ Multiple card voting works');
                    } else {
                        console.log('❌ Multiple card voting failed');
                    }
                }, 100);
            }
        }, 100);
    }, 100);
} else {
    console.log('❌ No vote buttons found');
}

// Test 5: Check for animations and visual feedback
const style = getComputedStyle(document.querySelector('.vote-count'));
console.log('CSS transitions:', style.transition);
console.log('CSS animations:', style.animation);