# Apollo Prompt Library - Development Commands
# Minimal cheatsheet for running the React app and screenshot capture

# Start development server
server:
    cd src/webapp && npm run dev

# Deploy to Vercel production
deploy:
    cd src/webapp && vercel --prod

# Take all screenshots in parallel
screenshots:
    cd src/screenshots && node capture-all.js

# Run tests in watch mode
test:
    cd src/webapp && npm test

# Run tests once
test-run:
    cd src/webapp && npm run test:run

# Run tests with coverage
test-coverage:
    cd src/webapp && npm run test:coverage

# Run tests with UI
test-ui:
    cd src/webapp && npm run test:ui

# Show prompts data
show_prompts:
    cat src/webapp/src/data/prompts.json | jq length
    @echo "Full prompts data location: src/webapp/src/data/prompts.json"

# Show AI system prompt
show-prompt:
    @echo "🤖 AI System Prompt location:"
    @echo "  src/webapp/src/ai-query/services/AIQueryService.js:34-72"
    @echo ""
    @sed -n '34,72p' src/webapp/src/ai-query/services/AIQueryService.js

# Database diagnostic commands
db-count:
    @echo "🗄️ Database Entry Counts:"
    @echo "Open browser console and run: dbCheck.countEntries()"
    @echo "Or manually check: Object.keys(localStorage).filter(k => k.startsWith('apollo_'))"

db-list:
    @echo "📋 Database Contents:"
    @echo "Open browser console and run: dbCheck.listPrompts()"
    @echo "Full diagnostic: dbCheck.fullReport()"

db-clear:
    @echo "🧹 Clear Database:"
    @echo "Open browser console and run: dbCheck.clearAndReinitialize()"
    @echo "Or manually: localStorage.clear(); location.reload()"

db-debug:
    @echo "🔧 Database Debug Commands:"
    @echo "  db-count    - Count entries in localStorage"
    @echo "  db-list     - List all prompts in database"  
    @echo "  db-clear    - Clear and reinitialize database"
    @echo ""
    @echo "Browser Console Commands:"
    @echo "  dbCheck.fullReport()         - Complete diagnostic"
    @echo "  dbCheck.countEntries()       - Count all entries"
    @echo "  dbCheck.listPrompts()        - List all prompts"
    @echo "  dbCheck.clearAndReinitialize() - Reset database"

# Key files
files:
    @echo "📁 Key Files:"
    @echo "  React App:     src/webapp/"
    @echo "  Screenshots:   src/screenshots/"
    @echo "  Prompts Data:  src/webapp/src/data/prompts.json (40 prompts)"
    @echo "  Config:        src/webapp/src/config.js"
    @echo "  Database:      src/webapp/src/api/browserDatabase.js"
    @echo "  Debug Tools:   src/webapp/src/debug/"
    @echo "  AI System Prompt: src/webapp/src/ai-query/services/AIQueryService.js:34-72"
    @echo "  Tasks:         zTasks.md"
    @echo "  Tests:         src/webapp/src/__tests__/"